package com.drex.customer;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Hello world!
 *
 */
@SpringBootConfiguration
@SpringBootApplication(scanBasePackages = {"com.drex"})
@EnableDubbo
public class DrexCustomerApplication
{
    public static void main( String[] args )
    {
        SpringApplication.run(DrexCustomerApplication.class, args);
    }
}
