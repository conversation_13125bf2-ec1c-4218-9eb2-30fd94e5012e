# Server configuration
server.port=8081

# Dubbo configuration
dubbo.application.name=drex-customer
dubbo.application.id=drex-customer
dubbo.application.checkSerializable=false
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20880
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.provider.timeout=10000
dubbo.consumer.timeout=10000
#dubbo.registry.address=zookeeper://zookeeper.drex-dev.svc.cluster.local:2181
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.application.parameters.router=traffic
dubbo.provider.parameters.traffic=${TRAFFIC_TAG}
dubbo.provider.prefer-serialization=hessian2,fastjson2
dubbo.provider.serialization=hessian2
dubbo.application.serialize-check-status=WARN
dubbo.application.check-serializable=false
dubbo.application.metadata-type=remote
dubbo.tracing.enabled=true

management.metrics.tags.application=drex-customer
management.health.db.enabled=true
management.health.solr.enabled=false
management.health.mongo.enabled=true
management.health.cassandra.enabled=false
management.health.elasticsearch.enabled=false
management.health.influxdb.enabled=false
management.health.neo4j.enabled=false
management.server.port=9090
management.health.defaults.enabled=false
management.endpoint.health.show-details=always
management.endpoints.migrate-legacy-ids=true
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.prometheus.metrics.export.enabled=true
dubbo.metrics.protocol=prometheus
dubbo.metrics.aggregation.enabled=true
dubbo.metrics.prometheus.exporter.enabled=true
management.endpoints.web.exposure.include=env,health,info,httptrace,metrics,heapdump,threaddump,prometheus,dubbo,druid

# Logging configuration
logging.level.root=INFO
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n