syntax = "proto3";
package com.aspen.aweb.api.account;
option java_package = "com.aspen.aweb.api.account";

message VirtualAccountResponseDTO {
    // Strategy的主键id
    string id = 1;
    // 用户id
    string customerId = 2;
    // 用户名,默认是邮箱
    string userName = 3;
    // 金额
    string hotMoney = 4;
    // 冻结的金额
    string coldMoney = 5;
    // 币种
    string coinCode = 6;
    // 状态
    string status = 7;
}

message SpotAccountRequest {
  // 用户id
  string userName = 1;
  string currency = 2;
}

message SpotAccountDeductionRequest {
  // 用户id
  string id = 1;
  string userName = 2;
  string currency = 3;
  double amount = 4;
}



message SpotVirtualAccountResponse {
  string code = 1;
  string msg = 2;
  VirtualAccountResponseDTO virtualAccountResponseDTO = 3;
}


//////////////////////以下服务定义
service UserAccountFacade {
  // 用户现货账户查询
  rpc userSpotAccount(SpotAccountRequest) returns (SpotVirtualAccountResponse);
  // 用户现货账扣款
  rpc userSpotAccountDeduction(SpotAccountDeductionRequest) returns (SpotVirtualAccountResponse);

}