package com.drex.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.activity.dal.tablestore.builder.TaskConfigBuilder;
import com.drex.activity.dal.tablestore.model.TaskConfig;
import com.drex.activity.task.model.constant.ActivityConstant;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22 20:24
 * @description:
 */
@Slf4j
@Component
public class TaskConfigBuilderImpl extends WideColumnStoreBuilder<TaskConfig> implements TaskConfigBuilder {

    @PostConstruct
    public void init() {
        super.init(TaskConfig.class);
    }

    @Override
    public String tableName() {
        return "task_config";
    }

    @Override
    public boolean insert(TaskConfig taskConfig) {
        return super.putRow(taskConfig, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(TaskConfig taskConfig) {
        return update(taskConfig, null);
    }

    @Override
    public boolean update(TaskConfig taskConfig, List<String> updateColumns) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        if(CollectionUtils.isNotEmpty(updateColumns)){
            return super.updateRow(taskConfig, updateColumns, condition);
        }else {
            return super.updateRow(taskConfig, condition);
        }
    }

    @Override
    public boolean delete(List<String> ids) {
        List<TaskConfig> configList = new ArrayList<>();
        for(String id : ids){
            TaskConfig taskConfig = new TaskConfig();
            taskConfig.setTaskId(id);
            configList.add(taskConfig);
        }
        return super.batchDeleteRows(configList);
    }

    @Override
    public TaskConfig getTaskById(String taskId) {
        TaskConfig config = new TaskConfig();
        config.setTaskId(taskId);
        return super.getRow(config);
    }

    @Override
    public List<TaskConfig> getTaskByCode(String appId, String code) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("app_id", appId));
        builder.must(QueryBuilders.term("code", code));
        return pageSearch(builder.build() , null, 0, 10, TaskConfig.SEARCH_TASK_CONFIG).getRows();
    }

    @Override
    public List<TaskConfig> findTaskByAppId(String appId) {
        log.info("findTaskBySaasId appId:{}", appId);
        BoolQuery.Builder filter = QueryBuilders.bool()
                .filter(QueryBuilders.term("app_id", appId))
                .filter(QueryBuilders.term("show_list", true))
                .filter(QueryBuilders.terms("status").terms(ActivityConstant.CommonStatus.ACTIVE.name(), ActivityConstant.CommonStatus.GRAY.name()));
        Sort sort = new Sort(Arrays.asList(new FieldSort("order", SortOrder.ASC)));
        return pageSearch(filter.build(), sort, 0, 100, TaskConfig.SEARCH_TASK_CONFIG).getRows();
    }
}
