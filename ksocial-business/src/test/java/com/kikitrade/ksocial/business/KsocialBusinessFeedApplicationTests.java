package com.kikitrade.ksocial.business;

import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.common.constant.BaseConstant;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.common.thread.ThreadContextHolder;
import com.kikitrade.ksocial.api.model.feed.FeedResponse;
import com.kikitrade.ksocial.api.model.feed.FlatFeedRequest;
import com.kikitrade.ksocial.api.model.feed.GetFeedRequest;
import com.kikitrade.ksocial.api.service.feed.IRemoteMixedFeedService;
import com.kikitrade.ksocial.base.util.ChainIdUtil;
import com.kikitrade.ksocial.business.feed.IFlatFeedBusinessService;
import com.kikitrade.ksocial.common.constants.reaction.KindEnum;
import com.kikitrade.ksocial.common.parameter.FeedEnrichOption;
import com.kikitrade.ksocial.common.parameter.FeedReaction;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;
import java.util.Map;


@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = KsocialBusinessFeedApplicationTests.class)
@TestPropertySource(locations = "classpath:application-default.properties")
@SpringBootApplication(scanBasePackages = {"com.kikitrade.ksocial.dal", "com.kikitrade.ksocial.*"})
@Slf4j
public class KsocialBusinessFeedApplicationTests {

    @Autowired
    private IFlatFeedBusinessService flatFeedBusinessService;

    @Autowired
    private IRemoteMixedFeedService remoteMixedFeedService;


    @Test
    public void testFeeds() {
        ChainIdUtil.setChainId("84532");
        String feedId = "";
        String syncViewId = "";
        Map param  = new HashMap<>();
        param.put("a_community","0x1");
        param.put("aCommunity","0x1");

        FlatFeedRequest feedRequest = FlatFeedRequest
                .builder()
                .key("aggregated_community_pin_post")
                .operator("4")
                .params(param).limit(10)
                .build();

        TokenPage<FeedResponse> feeds = flatFeedBusinessService.feeds(feedRequest);
        System.out.printf(JSON.toJSONString(feeds));
    }


    @Test
    public void testBountyFeeds() {
        ChainIdUtil.setChainId("84532");
        String feedId = "";
        String syncViewId = "";
        Map param  = new HashMap<>();
        //param.put("a_profile","0x32");
        param.put("a_link_source","twitter");
        /*param.put("a_highlight_start_time", "-1");
        param.put("a_highlight_end_time",Long.MAX_VALUE);
*/
        FeedEnrichOption build = FeedEnrichOption.builder().withReaction(
                FeedReaction.builder().reactions(Lists.newArrayList("earliest_upvote")).reactionLimit(10).ownKinds(Lists.newArrayList(KindEnum.VOTE)).build())
                .withUser(true)
                .build();
        FlatFeedRequest feedRequest = FlatFeedRequest
                .builder()
                .key("aggregated_bounty_explore")
                .operator("4")
                .enrichOption(build)
                .params(param).limit(10)
                .build();

        TokenPage<FeedResponse> feeds = flatFeedBusinessService.feeds(feedRequest);
        System.out.printf(JSON.toJSONString(feeds));
    }

    @Test
    public void testListFeeds() {
        GetFeedRequest getFeedRequest = GetFeedRequest.builder().build();
        FeedResponse feedResponse = remoteMixedFeedService.get(getFeedRequest);
        System.out.printf(JSON.toJSONString(feedResponse));
    }


    @Test
    public void tesCommentList() {
        ThreadContextHolder.set(BaseConstant.CHAIN_ID,"84532");
        FlatFeedRequest feedRequest = FlatFeedRequest.builder().key("timeline_activity_comments_50248148500611079").limit(10).build();
        TokenPage<FeedResponse> feedResponse = flatFeedBusinessService.feeds(feedRequest);
        System.out.printf(JSON.toJSONString(feedResponse));
    }



}
