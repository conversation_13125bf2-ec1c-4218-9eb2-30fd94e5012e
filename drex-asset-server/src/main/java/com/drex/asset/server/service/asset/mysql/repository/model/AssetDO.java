package com.drex.asset.server.service.asset.mysql.repository.model;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import java.math.BigDecimal;

@Data
@TableName("asset")
public class AssetDO extends BaseModelDO {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;  //id

    @TableField("customer_id")
    private String customerId;  //用户id

    @TableField("dapp_id")
    private String dappId; // dapp id

    @TableField("asset_type")
    private Integer assetType; // 账户类型

    @TableField("available")
    private BigDecimal available;  //可用总额

    @Version
    @TableField("version")
    private Integer version;  //版本号

    @TableField("status")
    private Integer status;  //账户状态（0 不可用 1可用）

    @TableField(exist = false)
    private boolean strictCheck = true; // strict check
}