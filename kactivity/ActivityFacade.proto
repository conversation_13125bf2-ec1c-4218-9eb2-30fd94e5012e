syntax = "proto3";
import "google/protobuf/timestamp.proto";
package com.kikitrade.activity.facade.award;
option java_package = "com.kikitrade.activity.facade.award";
option java_multiple_files = true;

enum ActivitySourceEnum{
  TASK = 0;
  OPERATE = 1;
}

enum ActivityTypeEnum{
  //层级发奖
  HIERARCHY = 0;
  //邀请发奖
  INVITE = 1;
  //自定义
  CUSTOMIZE = 2;
  //普通
  NORMAL = 3;
}

enum ActivitySubTypeEnum {
  NONE = 0;
  TRADE_REBATE = 1;   // 交易返佣
  KYC1 = 2;    // KYC1奖励
}


enum ActivityStatusEnum{
  DRAFT = 0;
  ACTIVE = 1;
  PAUSE = 2;
  END = 3;
}

enum BatchStatusEnum{

  ALL = 0;
  NOT_IMPORTED = 1;
  IMPORTING = 2;
  IMPORT_FAILED = 3;

  UNAUDITED = 4;
  REJECTED = 5;
  APPROVED = 6;

  AWARDING = 7;
  AWARD_FAILED = 8;
  AWARD_SUCCESS = 9;

  ODPS_RUNNING = 10;
  ODPS_FAILED = 11;
}

enum AuditTypeEnum{
  APPROVE = 0;
  REJECT = 1;
}

enum BatchFrequency{
  EVERY_DAY = 0;
  FINISH = 1;
  EVERY_MONTH = 2;
  EVERY_WEEK = 3; // 每周
}

message UploadResponse{
  bool success = 1;
  string message = 2;
  string url = 3;
}

message RewardRule{
  string level = 1;
  string min = 2;
  string max = 3;
  string side = 4;
  string userType = 5;
  string awardType = 6;
  string awardAmount = 7;
  string award = 8;
  string vipLevel = 9;
}

message ConditionRule{
  string name = 1;
  string alias = 2;
  string filter = 3;
  string value = 4;
}

/**
  活动保存实体类
 */
message ActivityDTO{
  //activityId 为空时新增，不为空时修改
  string id = 1;
  string activityName = 2;
  ActivityTypeEnum type = 3;
  string startTime = 4;
  //结束时间大于开始时间
  string endTime = 5;
  string remark = 6;
  ActivityStatusEnum status = 7;
  string activityArea = 8;
  bool autoCreateBatch = 9;
  BatchFrequency batchFrequency = 10;
  repeated RewardRule rewardRule = 11;

  repeated ConditionRule conditionRule = 12;
  bool autoApprove = 13;
  string taskId = 14;
  string conditionCode = 15;

  ActivitySubTypeEnum subType = 16; // 子活动类型
}

/**
 保存活动返回值
 */
message ActivityResponse{
  bool success = 1;
  string message = 2;
  string id = 3;
}

/**
  保存/修改批次请求实体
 */
message ActivityBatchDTO{
  //batchId 新增空
  string id = 1;
  string batchName = 2;
  string activityId = 3;
  string activityName = 4;
  //数字货币、道具，code
  string rewardType = 5;
  //货币金额
  string amount = 6;
  //货币单位
  string currency = 7;
  //批次描述
  string remark = 8;
  //是否立即发奖
  bool scheduled = 9;
  //发奖时间
  string scheduledTime = 10;
  //最后修改人
  string amended = 11;
  //上传的csv文件
  string sourceOssUrl = 12;

  repeated RewardRule rewardRule = 13;
}

message ActivityBatch{
  string id = 1;
  string name = 2;
  string activityId = 3;
  string activityName = 4;
  //数字货币、道具
  string rewardType = 5;
  //货币金额
  string amount = 6;
  //货币单位
  string currency = 7;
  //批次描述
  string remark = 8;
  //是否立即发奖
  bool scheduled = 9;
  //发奖时间
  string scheduledTime = 10;
  //saasId
  string saasId = 11;
  string prizeAmount = 12;
  string winners = 13;
  string amended = 14;
  string modified = 15;
  BatchStatusEnum status = 16;
  //下载地址
  string ossUrl = 17;
  string generateTime = 18;
  string sourceOssUrl = 19;

  repeated RewardRule rewardRule = 20;

  string activityType = 21;

  ActivitySourceEnum source = 22;//TASK,OPERATE
}

/**
 查询列表
 */
message ActivityBatchRequest{
  string id = 1;
  string batchName = 2;
  int32 pageNo = 3;
  int32 pageSize = 4;
  BatchStatusEnum status = 5;
  string activityId = 6;
}

/**
 列表返回结果
 */
message ActivityBatchListResponse{
  bool success = 1;
  string message = 2;
  repeated ActivityBatch activityBatch = 3;
}

/**
 批次详情
 */
message ActivityBatchDetailRequest{
  string id = 1;
}

message ActivityBatchResponse{
  bool success = 1;
  string message = 2;
  ActivityBatch activityBatch = 3;
}

message AuditRequest{
  string id = 1;
  AuditTypeEnum auditType = 2;
}

message AuditResponse{
  bool success = 1;
  string message = 2;
}

message Award{
  string id = 1;
  string customerId = 2;
  string phone = 3;
  string email = 4;
  //预计发奖时间
  string awardTime = 5;
  //发奖状态 3 ～ 8
  BatchStatusEnum awardStatus = 6;
  string message = 7;
}

message AwardRequest{
  string id = 1;
}

message AwardListResponse{
  bool success = 1;
  int64 pageNo = 2;
  int64 pageSize = 3;
  int64 total = 4;
}

message AwardDTO{
  repeated string id = 1;
}

message ModifyDetail{
  string id = 1;
  string message = 2;
}

message ModifyAwardResponse{
  bool success = 1;
  string message = 2;
  repeated ModifyDetail detail = 3;
}

message UploadRequest{
  string batchId = 1;
  string fileName = 2;
}

message ExportDataRequest{
  string id = 1;//活动id
  string conditionId = 2;
  repeated Condition conditions = 3;
}

message ImportDataRequest{
  string id = 1;//活动id
  string batchId = 2;
}

message Condition{
  string code = 1;
  string value = 2;
}

message ExportDataResponse{
  bool success = 1;
  string message = 2;
  string url = 3;
}

message ImportDataResponse{
  bool success = 1;
  string message = 2;
}

message EmptyRequest {
}

message ConditionRequest {
  string code = 1;
}

message ConditionCode {
  repeated string code = 1;
}

message ConditionResponse {
  repeated ConditionVO condition = 1;
}

message ConditionVO {
  string name = 1;
  string filter = 2;
  string alisa = 3;
}

message LotteryDTO{
  //奖池id
  string id = 1;
  //valid
  string valid = 2;
  //remark
  string remark = 3;
  //status
  string status = 4;
  //规则vip等级
  string vipLevel = 5;
  //消耗燃料（次）
  string amount = 6;
  //参与次数
  string timesLimit = 7;
  //奖励金额上限
  string rewardLimit = 8;
  //奖品url
  repeated LotteryItem item = 9;
}

message LotteryVO {
  //奖池基本信息
  Lottery lottery = 1;
  //奖池奖品
  repeated LotteryItem item = 2;
}

message Lottery {
  string id = 1;
  //valid
  string valid = 2;
  //remark
  string remark = 3;
  //status
  string status = 4;
  //规则vip等级
  string vipLevel = 5;
  //消耗燃料（次）
  string amount = 6;
  //参与次数
  string timesLimit = 7;
  //奖励上限
  string rewardLimit = 8;
}

message LotteryItem{
  //奖励名称
  string name = 1;
  //奖励类型
  string currency = 2;
  //投放奖励
  string amount = 3;
  //投放数量
  int32 num = 4;
  //中奖概率
  string percent = 5;
  //是否兜底
  bool isLow = 6;
  //POINT、TOKEN
  string awardType = 7;
  //剩余数量
  int32 remainNum = 8;
}

message LotteryDeleteDTO{
  string id = 1;
}

message LotteryResponse{
  bool success = 1;
  string message = 2;
  LotteryVO lotteryVO = 3;
}

message LotteryRequest{
  string valid = 1;
  int32 offset = 2;
  int32 limit = 3;
}

message LotteryDetailRequest{
  string id = 1;
}

message LotteryListResponse{
  repeated Lottery lottery = 1;
}

service ActivityFacade{

  /**
   保存或修改活动
   */
  rpc saveOrUpdateActivity(ActivityDTO) returns (ActivityResponse);

  /**
    新增或修改批次(过期)
   */
  rpc saveOrUpdateBatch(ActivityBatchDTO) returns (ActivityBatchResponse);

  /**
   删除批次(过期)
   */
  rpc deleteBatch(ActivityBatchDTO) returns (ActivityBatchResponse);

  /**
    批次列表查询(过期)
   */
  rpc queryBatchForList(ActivityBatchRequest) returns (ActivityBatchListResponse);

  /**
   批次详情接口(过期)
   */
  rpc queryDetail(ActivityBatchDetailRequest) returns (ActivityBatch);

  /**
    终审通过(过期)
   */
  rpc audit(AuditRequest) returns (AuditResponse);

  /**
   查询奖例列表(过期)
   */
  rpc queryRewardList(AwardRequest) returns (AwardListResponse);

  /**
   删除详情(过期)
   */
  rpc deleteReward(AwardDTO) returns (ModifyAwardResponse);

  /**
   上传文件(过期)
   */
  rpc uploadFile(UploadRequest) returns (UploadResponse);

  /**
   导出数据
   */
  rpc exportData(ExportDataRequest) returns (ExportDataResponse);

  /**
   导入数据
   */
  rpc importData(ImportDataRequest) returns (ImportDataResponse);

  /**
   保存奖池
  */
  rpc saveLottery(LotteryDTO) returns (LotteryResponse);

  /**
   删除奖池
   */
  rpc deleteLottery(LotteryDeleteDTO) returns(LotteryResponse);

  /**
   奖池列表
   */
  rpc lotteryList(LotteryRequest) returns(LotteryListResponse);

  /**
   奖池详情
   */
  rpc lotteryDetail(LotteryDetailRequest) returns(LotteryVO);

  /**
   查询规则名称列表
   */
  rpc getConditionCodes(EmptyRequest) returns (ConditionCode);

  /**
   查询规则详情
 */
  rpc getCondition(ConditionRequest) returns (ConditionResponse);
}
