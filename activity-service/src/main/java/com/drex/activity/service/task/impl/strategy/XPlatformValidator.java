package com.drex.activity.service.task.impl.strategy;

import com.alibaba.fastjson2.JSONObject;
import com.drex.activity.service.task.SocialBehaviorValidator;
import com.drex.activity.task.model.SocialConstant;
import com.drex.activity.task.model.request.SocialEventBody;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * X平台(Twitter)社交行为验证策略实现
 */
@Component
public class XPlatformValidator implements SocialBehaviorValidator {

    @Override
    public Boolean validate(SocialConstant.EventEnum eventEnum, SocialEventBody socialEventBody, JSONObject attrJson) {
        if (eventEnum.equals(SocialConstant.EventEnum.replay)) {
            return validateReplay(socialEventBody, attrJson);
        }
        return Boolean.FALSE;
    }

    /**
     * 验证回复行为
     *
     * @param socialEventBody 社交事件内容
     * @param attrJson 任务配置属性
     * @return 是否符合要求
     */
    private Boolean validateReplay(SocialEventBody socialEventBody, JSONObject attrJson) {
        String replyContent = socialEventBody.getReplyContent();
        if (StringUtils.isBlank(replyContent)) {
            return Boolean.FALSE;
        }

        // @指定用户之一 && 回复内容长度 >= minLength(配置值)
        String[] handles = attrJson.getString("handles").split(",");
        // 默认最小长度为16，可从配置中读取
        Long minLength = attrJson.getLongValue("minLength", 16L);

        for (String handle : handles) {
            if (replyContent.contains("@" + handle)) {
                // 去除@用户名后的实际内容长度
                int contentLength = replyContent.replaceAll("@\\w+", "").length();
                if (contentLength >= minLength) {
                    return Boolean.TRUE;
                }
            }
        }

        return Boolean.FALSE;
    }

    @Override
    public SocialConstant.PlatformEnum getSupportedPlatform() {
        return SocialConstant.PlatformEnum.X;
    }
}
