package com.drex.activity.service.remote.impl;

import com.drex.activity.service.task.ActivityMaizeService;
import com.drex.activity.task.api.RemoteMaizeService;
import com.drex.activity.task.model.response.MaizeDTO;
import com.drex.activity.task.model.ActivityException;
import com.drex.activity.task.model.request.GenerateMaizeRequest;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

@DubboService
@Slf4j
public class RemoteMaizeServiceImpl implements RemoteMaizeService {

    @Resource
    private ActivityMaizeService activityMaizeService;

    @Override
    public Response<MaizeDTO> generateMaize(GenerateMaizeRequest request) {
        try {
            return Response.success(activityMaizeService.generateMaize(request));
        } catch (ActivityException e) {
            return Response.error(e.getCode().name(), e.getMessage());
        }
    }

    @Override
    public Response<Boolean> checkMaizeValid(String customerId, String socialPlatform, String maizeCode) {
        return Response.success(activityMaizeService.checkMaizeValid(customerId, socialPlatform, maizeCode));
    }
}
