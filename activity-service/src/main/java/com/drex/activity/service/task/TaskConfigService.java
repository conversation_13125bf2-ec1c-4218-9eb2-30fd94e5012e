package com.drex.activity.service.task;

import com.drex.activity.task.model.TaskConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 11:03
 * @description:
 */
public interface TaskConfigService {

    boolean upsert(TaskConfigDTO taskConfigDTO);

    List<TaskConfigDTO> findTaskByAppId(String appId);

    TaskConfigDTO findByTaskId(String taskId);

    List<TaskConfigDTO> findByTaskCode(String appId, String code);

    boolean delete(String id);
}
