package com.drex.activity.service.task.domain;

import cn.hutool.core.util.RandomUtil;
import com.drex.activity.task.model.Award;
import com.drex.activity.task.model.TaskConfigDTO;
import com.drex.activity.task.model.constant.ActivityTaskConstant;
import org.apache.commons.collections.MapUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/5 18:30
 */
public class TaskAwardDomain {

    public static List<Award> getAward(TaskConfigDTO taskConfigDTO, AtomicLong progress, String vipLevel) {
        Map<String, List<Award>> reward = taskConfigDTO.getReward();
        long index = progress.get() % taskConfigDTO.getRewardFrequency();
        List<Award> awards = reward.get(String.valueOf(index));
        if (awards != null && progress.get() <= taskConfigDTO.getLimit(vipLevel)) {
            if (vipLevel != null) {
                return awards.stream().filter(award -> vipLevel.equals(award.getVipLevel())).toList();
            } else {
                if (awards.size() > 1) {
                    return awards.stream().filter(award -> "NORMAL".equals(award.getVipLevel())).toList();
                } else {
                    Award award = awards.get(0);
                    if (award.getVipLevel() == null || "NORMAL".equals(award.getVipLevel())) {
                        return Arrays.asList(award);
                    }
                }
            }
        }
        return null;
    }
}
