package com.drex.activity.service.mq;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.TopicConfig;
import com.drex.activity.task.model.ActivityException;
import com.drex.activity.task.model.ActivityResponseCode;
import com.kikitrade.framework.observability.tracing.annotation.TracingSpan;
import com.kikitrade.framework.observability.tracing.constant.TracingBusiness;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.framework.ons.OnsProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
@Slf4j
public class ActivityEventListener implements OnsMessageListener {

    @Resource
    private ActivityEventAction activityEventAction;
    @Resource
    private OnsProperties onsProperties;
    @Resource
    @Lazy
    private ActivityEventListener activityEventListener;

    private static final String TOPIC = "T_DREX_ACTIVITY_EVENT";

    @Override
    public String topic() {
        return TOPIC + onsProperties.getEnv();
    }

    @Override
    public Action doConsume(Message message, ConsumeContext consumeContext) {
        return activityEventListener.doProcess(message, consumeContext);
    }

    @TracingSpan(name = "drex-activityEventListener", business = TracingBusiness.none)
    public Action doProcess(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody());
        log.info("[task] eventAction start,{},{},{}", body, message.getMsgID(), message.getTopic());

        ActivityEventMessage activityEventMessage = JSON.parseObject(body, ActivityEventMessage.class);

        try {
            String customerId = activityEventMessage.getCustomerId();
            if(StringUtils.isBlank(customerId) || "-1".equals(customerId) || customerId.equals("null")){
                return Action.CommitMessage;
            }
            activityEventAction.action(activityEventMessage);
            return Action.CommitMessage;
        } catch (ActivityException activityException) {
            if (activityException.getCode() == ActivityResponseCode.TASK_RETRY) {
                log.error("[task] eventAction retry, activityEventMessage:{},{}", activityEventMessage, activityException.getCode());
                return Action.ReconsumeLater;
            }
        } catch (Exception ex) {
            log.error("[task] eventAction error, activityEventMessage{}", activityEventMessage, ex);
        }
        return Action.CommitMessage;
    }
}
