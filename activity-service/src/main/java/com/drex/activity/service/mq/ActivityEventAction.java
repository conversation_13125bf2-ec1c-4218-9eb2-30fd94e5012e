package com.drex.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.drex.activity.service.config.TaskCodeConfig;
import com.drex.activity.service.task.ActivityTaskService;
import com.drex.activity.service.task.TaskConfigService;
import com.drex.activity.task.model.Award;
import com.drex.activity.task.model.TaskConfigDTO;
import com.drex.activity.task.model.constant.BusinessMonitorConstant;
import com.drex.activity.task.model.ActivityException;
import com.drex.activity.service.common.ActivityDTO;
import com.drex.activity.task.model.ActivityResponseCode;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class ActivityEventAction {

    @Resource
    private ActivityTaskService activityTaskService;
    @Resource
    private TaskConfigService taskConfigService;
    @Autowired
    private KiKiMonitor kiKiMonitor;

    public ActivityDTO<List<Award>> action(ActivityEventMessage activityEventMessage) throws ActivityException {
        String appId = String.valueOf(activityEventMessage.getBody().get("appId"));
        String taskId = String.valueOf(activityEventMessage.getBody().get("taskId"));
        String taskCode = activityEventMessage.getEventCode();
        String customerId = activityEventMessage.getCustomerId();
        log.info("user_track [task] start [{}]", taskCode);

        if (appId != null && taskCode != null) {
            kiKiMonitor.monitor(BusinessMonitorConstant.EVENT, new String[]{"appId", appId, "code", taskCode, "stage", "listener"});
        }

        List<TaskConfigDTO> taskConfigs = getTaskConfigs(appId, taskId, taskCode);
        log.info("[task] eventAction taskConfig: customerId={}, taskConfigs={}", customerId, JSON.toJSONString(taskConfigs));

        if (CollectionUtils.isEmpty(taskConfigs)) {
            if (appId != null && taskCode != null) {
                kiKiMonitor.monitor(BusinessMonitorConstant.EVENT, new String[]{"appId", appId, "code", taskCode, "stage", "expired"});
            }
            return ActivityDTO.<List<Award>>builder().code(ActivityResponseCode.TASK_EXPIRED).build();
        }

        ActivityDTO<List<Award>> award = null;
        for (TaskConfigDTO config : taskConfigs) {
            log.info("user_track [task] [{},{}]", taskCode, config.getTaskId());
//            TaskCodeConfig taskCodeConfig = TaskCodeConfig.getValue(config.getCode());
//
//            if (taskCodeConfig == null) {
//                log.warn("[task] eventAction, event_not_found: customerId={}, taskCode={}", customerId, taskCode);
//                return ActivityDTO.<List<Award>>builder().code(ActivityResponseCode.TASK_EVENT_NOT_FOUND).build();
//            }

            try {
                ActivityEventMessage activityEventMessageA = BeanUtil.copyProperties(activityEventMessage, ActivityEventMessage::new);
                award = fillTask(config, activityEventMessageA, null);
            } catch (Exception ex) {
                log.error("[task] eventAction error: activityEventMessage={}, exception={}", activityEventMessage, ex);
            }
        }
        log.info("user_track [task] end [{}]", taskCode);

        return ActivityDTO.<List<Award>>builder().code(ActivityResponseCode.SUCCESS).obj(award != null ? award.getObj() : null).build();
    }

    private List<TaskConfigDTO> getTaskConfigs(String appId, String taskId, String taskCode) {
        List<TaskConfigDTO> taskConfigs = new ArrayList<>();

        if (taskId != null) {
            TaskConfigDTO configDTO = taskConfigService.findByTaskId(taskId);
            if (configDTO != null) {
                taskConfigs.add(configDTO);
            }
        } else {
            List<TaskConfigDTO> configDTOS = taskConfigService.findByTaskCode(appId, taskCode);
            if (CollectionUtils.isNotEmpty(configDTOS)) {
                taskConfigs.addAll(configDTOS);
            }
        }
        return taskConfigs;
    }


    /**
     * 完成任务
     *
     * @param config
     * @param activityEventMessage
     * @param eventCode
     * @return
     * @throws Exception
     */
    private ActivityDTO<List<Award>> fillTask(TaskConfigDTO config, ActivityEventMessage activityEventMessage, TaskCodeConfig eventCode) throws Exception {

        //做任务
        List<Award> award = activityTaskService.doTask(activityEventMessage, config, eventCode);
        log.info("[task] eventAction success,{},{}", activityEventMessage.getCustomerId(), activityEventMessage.getEventCode());
        ActivityDTO.ActivityDTOBuilder<List<Award>> builder = ActivityDTO.builder();
        return builder.code(ActivityResponseCode.SUCCESS).obj(award).build();
    }
}
