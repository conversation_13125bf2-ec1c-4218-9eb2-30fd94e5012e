/**
 * Copyright:   北京互融时代软件有限公司
 *
 * @author: <PERSON>
 * @version: V1.0
 * @Date: 2015年9月16日 上午11:04:39
 */
package com.kikitrade.kcustomer.common.util;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 日期工具类
 *
 * <AUTHOR>
 */
public class DateUtil {

    public static final String[] PARSEPATTERNS = new String[]{
            "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss",
            "yyyy.MM.dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd HH:mm",
            "yyyy.MM.dd HH:mm", "yyyy-MM-dd HH", "yyyy/MM/dd HH",
            "yyyy.MM.dd HH", "yyyy-MM-dd", "yyyy/MM/dd", "yyyy.MM.dd"};
    public static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期转字符串 根据给定日期格式，格式化日期
     *
     * @param date
     * @return
     */
    public static String dateToUTCString(Date date) {
        if (date != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(PARSEPATTERNS[1]);
            return new StringBuffer().append(sdf.format(date)).append("(UTC)").toString();
        }
        return "";
    }

    public static String dateToString(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return new StringBuffer().append(sdf.format(date)).toString();
        }
        return "";
    }
}
