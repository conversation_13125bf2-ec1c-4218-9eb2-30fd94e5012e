package com.kikitrade.kcustomer.common.constants;


public enum SmsTemplateType {

    /**
     * 注册手机号已经存在
     */
    REGISTER_PHONE_EXIST,
    /**
     * 重置密码/绑定/换绑/后暂不允许出金
     */
    WITHDRAW_NOT_ALLOW,

    REGISTER,
    SECURITY_OP,
    PWD_INCORRECT_LOCK,
    PWD_RESET,
    KYC_SUCCESS,
    BINDING_PHONE,
    /**
     * 换绑时旧手机验证码 带有安全提示
     */
    REPLACE_BIND_PHONE_SECURITY,
    /**
     * 验证新手机
     */
    REPLACE_BINDING_PHONE,
    WITHDRAW,
    LOGIN,
    PAYMENT_CONFIRM,
    UNBIND_GOOGLE,
    /**
     * 换绑邮箱时的手机安全提示
     */
    BEFORE_EMAIL_REPLACE,
    /**
     * 换绑邮箱成功
     */
    REPLACE_BIND_EMAIL_SUCCESS,
    /**
     * 换绑手机成功
     */
    REPLACE_BIND_PHONE_SUCCESS,

    ;

}
