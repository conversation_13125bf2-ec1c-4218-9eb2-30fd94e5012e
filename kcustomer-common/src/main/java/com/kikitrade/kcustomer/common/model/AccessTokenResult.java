package com.kikitrade.kcustomer.common.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/11 19:18
 * @description:
 */
@Data
public class AccessTokenResult implements Serializable {
    @JSONField(name = "access_token")
    private String accessToken;

    @JSONField(name = "token_type")
    private String tokenType;

    @JSONField(name = "expires_in")
    private Integer expiresIn;
}
