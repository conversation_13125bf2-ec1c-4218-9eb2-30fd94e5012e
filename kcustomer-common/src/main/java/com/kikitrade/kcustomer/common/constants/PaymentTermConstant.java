package com.kikitrade.kcustomer.common.constants;

import java.util.Arrays;

/**
 * @author: penuel
 * @date: 2021/7/14 16:34
 * @desc: TODO
 */
public interface PaymentTermConstant {

    String EmailTemplate_Id_Success = "payment_term_add_success";
    String EmailTemplate_Id_Fail = "payment_term_add_fail";
    String EmailTemplate_Content_Key_StatusDesc = "statusDesc";

    String FireBaseTemplate_Type_Success = "payment_term_success";
    String FireBaseTemplate_Type_Fail = "payment_term_fail";
    String FireBaseTemplate_Title_Key_Success = "fcm.title.payment.term.success";
    String FireBaseTemplate_Body_Key_Success = "fcm.body.payment.term.success";
    String FireBaseTemplate_Title_Key_Fail = "fcm.title.payment.term.fail";
    String FireBaseTemplate_Body_Key_Fail = "fcm.body.payment.term.fail";
    // 发送审核请求给管理端
    String AUDIT_PAYMENT_TERM_URL = "/api/method/operation.operation.doctype.circle_deposit_bank_card_management.circle_deposit_bank_card_management.webhook";

    enum Status {
        unaudited, //待审核
        approved,  //管理端审核通过
        rejected,  //管理端审核拒绝
        active,  // 账户可用 （对circle来说，是账户在circle创建成功）
        disable, // 账户不可用 （对circle来说，是账户在circle创建失败）
        ;

        public static Status get(String status) {
            return Arrays.stream(values()).filter(s -> s.name() == status).findFirst().orElse(null);
        }
    }

    enum Usage {
        audit(0),
        circle(1),
        c2c(2),
        ;

        private int code;

        Usage(int code){
            this.code = code;
        }

        public static Usage get(String usage) {
            return Arrays.stream(values()).filter(s -> s.name() == usage).findFirst().orElse(null);
        }

        public static Usage fromCode(int code) {
            return Arrays.stream(values()).filter(s -> s.ordinal() == code).findFirst().orElse(null);
        }
    }

    enum OperateType {
        REJECT, APPROVE,
        ADD, DELETE,
        ;
    }

    enum Type {
        CARD(0),
        BANK(1),
        FPS(2),
        ;

        private int code;

        Type(int code){
            this.code = code;
        }

        public static Type get(String type) {
            return Arrays.stream(values()).filter(s -> s.name() == type).findFirst().orElse(null);
        }

        public static Type fromCode(int code) {
            return Arrays.stream(values()).filter(s -> s.ordinal() == code).findFirst().orElse(null);
        }
    }
}
