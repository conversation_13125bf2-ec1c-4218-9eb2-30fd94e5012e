package com.kikitrade.kcustomer.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @author: penuel
 * @date: 2021/7/13 11:05
 * @desc: TODO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceInfoDTO implements Serializable {

    private String deviceId; //设备信息 adid或idfa
    private String adId;//可能同上
    private String deviceType; //设备类型 android, ios, web, desktop
    private String osPlatform; //系统平台iOS or Android
    private String osVersion;//系统版本 10.15.7(iOS) or (10)android
    private String apiVersion;//设备api version android-23
    private String deviceVersion;//设备版本 iphone11或者xiaomi10
    private String deviceName; //Redmi K20 Pro
    private String deviceBrand; //设备品牌 Xiaomi, Redmi

    private String appVersion; //app version
    private String ip;//ip信息

    private String networkType;//网络类型

    private String customerId;

    //操作类型 ，1、唤醒app，2、登录app，3、登出app
    private int operationType;
    private String facebookDeviceId;
    private String adjustDeviceId;
    private String appName;
    private Long lastLoginTime;
    private Long lastLogoutTime;
    private Long created;
    private Long modified;



    @Data
    public static class Operation implements Serializable {
        public Operation() {

        }

        public Operation(DeviceInfoDTO deviceInfoDTO) {
            this.customerId = deviceInfoDTO.getCustomerId();
            this.ip = deviceInfoDTO.getIp();
            this.type = deviceInfoDTO.getOperationType();
            this.appVersion = deviceInfoDTO.getAppVersion();
            this.networkType = deviceInfoDTO.getNetworkType();
        }

        private int type;
        private String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        private String customerId;
        private String ip;
        private String appVersion;
        private String networkType;
    }

    public enum operationTypeEnum {
        AROSE(1, "唤醒APP"),
        LOGIN(2, "登录APP"),
        LOGOUT(3, "登出app");

        private int code;
        private String desc;

        operationTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
