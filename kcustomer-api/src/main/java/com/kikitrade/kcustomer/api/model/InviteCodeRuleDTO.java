package com.kikitrade.kcustomer.api.model;

import com.kikitrade.kcustomer.api.constants.InviteCodeRuleConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * Description:
 *
 * @Author: simon.shan
 * DateTime: 2022-10-28 14:02
 */
@Data
@Accessors(chain = true)
public class InviteCodeRuleDTO implements Serializable {
    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请者id
     */
    private String inviter;

    /**
     * 被邀请者id
     */
    private String invitee;

    /**
     * 最大邀请数量
     */
    private String inviteNumber;

    /**
     * 可用数量
     */
    private String availableNumber;

    /**
     * 邀请类型
     */
    private InviteCodeRuleConstant.InviteType inviteType;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 投入日期
     */
    private Date investDate;

    /**
     * 业务标签
     */
    private String tag;

    /**
     * 状态
     */
    private InviteCodeRuleConstant.RuleStatus status;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;
}
