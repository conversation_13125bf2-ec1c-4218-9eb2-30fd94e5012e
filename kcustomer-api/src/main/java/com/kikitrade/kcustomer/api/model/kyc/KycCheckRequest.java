package com.kikitrade.kcustomer.api.model.kyc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KycCheckRequest implements Serializable {

    @NotNull
    private String saasId;

    @NotNull
    private String customerId;

    /**
     * onfido 创建检查时带上证件原始信息 -> 目前没有
     */
    private String checkInfo;
}
