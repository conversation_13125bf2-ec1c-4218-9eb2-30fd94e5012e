package com.kikitrade.kcustomer.api.constants;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Getter
@Deprecated
public enum SmsTemplateType {


    REGISTER("SMS_211496189", "SMS_211481247", "SMS_211491229", Arrays.asList("code")),
    SECURITY_OP("SMS_211486298", "SMS_211481258", "SMS_211481259", Arrays.asList("code")),
    PWD_INCORRECT_LOCK("SMS_211975983", "SMS_211985886", "SMS_211975982", Arrays.asList("time")),
    PWD_RESET("SMS_211491301", "SMS_211496257", "SMS_211496260", Arrays.asList("code")),
    KYC_SUCCESS("SMS_211481327", "SMS_211496269", "SMS_211481326", null),
    SETTLE_NOTICE("SMS_234137751", "SMS_234152757", "SMS_234152757", Arrays.asList("account", "asset", "order")),
    // 只包含资产清算
    SETTLE_NOTICE_ASSET("SMS_234137747", "SMS_234137753", "SMS_234137753", Arrays.asList("account", "asset")),
    // 只包含撤单
    SETTLE_NOTICE_ORDER("SMS_234137835", "SMS_234142726", "SMS_234142726", Arrays.asList("account", "order")),
    RISK_NOTICE("SMS_234137598", "SMS_234142579", "SMS_234142579", null),

    //termii
    P2P_MERCHANT_APPLY_APPROVE("TERMII", null, null, null, null),
    P2P_MERCHANT_APPLY_REJECT("TERMII", null, null, null, null),
    P2P_MERCHANT_APPLY_SUBMIT("TERMII", null, null, null, null),
    P2P_MERCHANT_APPLY_UNDER_REVIEW("TERMII", null, null, null, null),
    P2P_MERCHANT_RETIRED("TERMII", null, null, null, null),
    P2P_MERCHANT_FROZEN("TERMII", null, null, null, null),
    P2P_MERCHANT_ACTIVE("TERMII", null, null, null, null),
    P2P_ORDER_APPEAL("TERMII", null, null, null, null),
    P2P_ORDER_CANCEL("TERMII", null, null, null, null),
    P2P_ORDER_INTERVENTION("TERMII", null, null, null, null),
    P2P_ORDER_TIMEOUT_APPEAL("TERMII", null, null, null, null),
    P2P_ORDER_TIMEOUT_CANCEL("TERMII", null, null, null, null),
    P2P_PLACE_ORDER("TERMII", null, null, null, null),
    P2P_PAYER_CONFIRM("TERMII", null, null, null, null),
    P2P_PAYEE_CONFIRM("TERMII", null, null, null, null),
    P2P_ORDER_APPEAL_FINISH("TERMII", null, null, null, null),
    PAYSTACK_KYC_L2_VERIFY_FAIL("TERMII", null, null, null, null),
    PAYSTACK_KYC_L2_VERIFY_SUCCESS("TERMII", null, null, null, null),
    PAYSTACK_KYC_L3_VERIFY_SUCCESS("TERMII", null, null, null, null),
    PAYSTACK_KYC_L3_VERIFY_FAIL("TERMII", null, null, null, null),
    TERMII_REGISTER("TERMII", null, null, null, null),
    TERMII_PWD_RESET("TERMII", null, null, null, null),
    TERMII_BINDING_PHONE("TERMII", null, null, null, null),
    TERMII_REPLACE_BINDING_PHONE("TERMII", null, null, null, null),
    TERMII_WITHDRAW("TERMII", null, null, null, null),
    TERMII_LOGIN("TERMII", null, null, null, null),
    PAYMENT_CONFIRM("TERMII", null, null, null, null),
    UNBIND_GOOGLE("TERMII", null, null, null, null),

    ;

    String channelType;
    String en;
    String cn;
    String hk;
    List<String> params = new ArrayList<>();


    SmsTemplateType(String channelType, String en, String cn, String hk, List<String> params) {
        this.channelType = channelType;
        this.en = en;
        this.cn = cn;
        this.hk = hk;
        if (!CollectionUtils.isEmpty(params)) {
            this.params = params;
        }
    }

    SmsTemplateType(String en, String cn, List<String> params) {
        this.en = en;
        this.cn = cn;
        if (!CollectionUtils.isEmpty(params)) {
            this.params = params;
        }
    }

    SmsTemplateType(String en, String cn, String hk, List<String> params) {
        this.en = en;
        this.cn = cn;
        this.hk = hk;
        if (!CollectionUtils.isEmpty(params)) {
            this.params = params;
        }
    }

    public boolean hasValidParam(Map<String, String> params) {
        if (this.getParams() == null || this.getParams().isEmpty()) {
            return true;
        }
        if (params == null || params.isEmpty()) {//上面判断需要的params已经不是空了，如果参数为空则为false
            return false;
        }
        boolean valid = params.keySet().containsAll(this.getParams());
        if (!valid) {
            log.warn("invalid param for smsType:{}, expect:{} ,actual:{}", this.name(), this.params.toString(), JSON.toJSONString(params));
        }
        return valid;
    }


    public static SmsTemplateType get(String name) {
        return Arrays.stream(SmsTemplateType.values()).filter(f -> f.name().equalsIgnoreCase(name))
                .findFirst().orElse(null);
    }
}
