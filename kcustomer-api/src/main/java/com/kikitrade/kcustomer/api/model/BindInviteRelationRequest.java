package com.kikitrade.kcustomer.api.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 绑定邀请人和被邀请人的关系
 * <AUTHOR>
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BindInviteRelationRequest extends CustomerCommonRequest implements Serializable {
    @NotNull
    private String inviterId;
    @NotNull
    private String inviteeId;
}
