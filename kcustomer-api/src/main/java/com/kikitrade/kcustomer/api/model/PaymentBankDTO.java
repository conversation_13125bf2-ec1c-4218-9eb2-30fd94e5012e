package com.kikitrade.kcustomer.api.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: penuel
 * @date: 2021/12/27 17:36
 * @desc: TODO
 */
@Data
public class PaymentBankDTO implements Serializable {

    private String name;
    private String swiftCode;
    private String bankCode;
    private String country;
    private String city;
    private String address;
    private BigDecimal networkFee;

}
