package com.kikitrade.kcustomer.api.model;

import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 被邀请人信息
 * <AUTHOR>
 */
@Data
public class InviteeInfoDTO implements Serializable {

    private String inviterId;

    private String inviteeId;

    private String nickName;

    private String account;

    private Date registerTime;

    /**
     * kyc认证等级
     */
    private CustomerIdentityConstants.KycLevel kycAuthLevel;

    private Date inviteTime;
}
