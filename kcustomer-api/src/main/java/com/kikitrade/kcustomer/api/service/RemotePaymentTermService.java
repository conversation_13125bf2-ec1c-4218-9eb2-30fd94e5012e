package com.kikitrade.kcustomer.api.service;

import com.kikitrade.framework.common.model.Page;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.PaymentBankDTO;
import com.kikitrade.kcustomer.api.model.PaymentTermDTO;
import com.kikitrade.kcustomer.api.model.PaymentTermSearchRequest;
import com.kikitrade.kcustomer.common.constants.PaymentTermConstant;

import java.util.List;

public interface RemotePaymentTermService {

    PaymentTermDTO add(PaymentTermDTO paymentTerm) throws CustomerException;

    /**
     * 此方法只提供覆盖原来被拒绝或者失败的paymentTerm，进行修改；
     * 其他皆不适用
     *
     * @param paymentTerm
     * @return
     * @throws CustomerException
     */
    PaymentTermDTO reSubmit(PaymentTermDTO paymentTerm) throws CustomerException;

    List<PaymentTermDTO> listByCustomerId(String saasId, String customerId, int offset, int limit);

    /**
     * 多元索引，搜索，customerId和usage必须至少有一个是有值的，
     *
     * @param saasId     必填
     * @param customerId
     * @param usage
     * @param statuses   非必须
     * @param offset
     * @param limit
     * @return
     */
    Page<PaymentTermDTO> searchByUsage(String saasId, String customerId, PaymentTermConstant.Usage usage, List<PaymentTermConstant.Status> statuses, int offset, int limit);

    PaymentTermDTO getById(String saasId, String customerId, String paymentTermId);

    PaymentTermDTO getByPaymentTermId(String saasId, String paymentTermId);

    PaymentTermDTO getByOuterId(String saasId, String outerId, PaymentTermConstant.Usage usage);

    PaymentTermDTO getByCustomerId(String saasId, String customerId, PaymentTermConstant.Usage usage);

    boolean auditPaymentTerm(String saasId, String paymentTermId, String customerId, String outerId, PaymentTermConstant.Status status, List<String> statusDescList) throws CustomerException;

    List<PaymentBankDTO> listBanks(String saasId, String customerId);

    /**
     * 添加卡管理
     * @param paymentTermDTO
     * @return
     * @throws CustomerException
     */
    PaymentTermDTO addPaymentTerm(PaymentTermDTO paymentTermDTO) throws CustomerException;

    /**
     * 修改卡
     * @param paymentTermDTO
     * @return
     * @throws CustomerException
     */
    Boolean modify(PaymentTermDTO paymentTermDTO) throws CustomerException;

    /**
     * 修改业务标签
     * @param saasId
     * @param customerId
     * @param id
     * @param business
     * @param operateType
     * @return
     * @throws CustomerException
     */
    Boolean modifyBusiness(String saasId, String customerId, String id, PaymentTermConstant.Usage business, PaymentTermConstant.OperateType operateType) throws CustomerException;

    /**
     * 用户删除卡
     * @param saasId
     * @param customerId
     * @param id
     * @return
     * @throws CustomerException
     */
    Boolean delete(String saasId,String customerId,String id) throws CustomerException;

    /**
     * 查询卡
     * @param saasId
     * @param paymentTermSearchRequest
     * @return
     * @throws CustomerException
     */
    List<PaymentTermDTO> search(String saasId, PaymentTermSearchRequest paymentTermSearchRequest) throws CustomerException;

}
