package com.kikitrade.kcustomer.api.service;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.*;
import com.kikitrade.kcustomer.api.model.kyc.KycCheckRequest;
import com.kikitrade.kcustomer.api.model.kyc.KycRequest;
import com.kikitrade.kcustomer.api.model.kyc.KycTokenRequest;
import com.kikitrade.kcustomer.api.model.kyc.WebhookRequest;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;

/**
 * 用户身份服务（KYC，Risk...)
 */
public interface RemoteCustomerIdentityService {




    CustomerCommonResponse getSdkAccessToken(KycTokenRequest kycTokenRequest) throws CustomerException;

    /**
     * 上传ocr图片
     *
     * @param kycRequest
     * @return
     * @throws CustomerException
     */
    CustomerCommonResponse uploadOcrImage(KycRequest kycRequest) throws CustomerException;


    /**
     * 上传fac,liveness信息
     *
     * @param saasId
     * @param customerId
     * @param certifyId    yundun服务返回的人脸照id
     * @param businessInfo 业务信息
     * @return
     */
    CustomerCommonResponse uploadFaceCompare(String saasId, String customerId, String certifyId, String businessInfo) throws CustomerException;


    /**
     * 获取用户最新的kyc结果
     *
     * @param saasId
     * @param customerId
     * @return
     */
    CustomerCommonResponse getIdentityResult(String saasId, String customerId) throws CustomerException;

    /**
     * 获取用户最新的kyc结果(含Kyc1和Kyc2)
     *
     * @param saasId
     * @param customerId
     * @return
     */
    CustomerCommonResponse getFullIdentityResult(String saasId, String customerId) throws CustomerException;

    boolean manualRejectKyc(String saasId, String customerId, CustomerIdentityConstants.ReviewReason reviewReason) throws CustomerException;

    @Deprecated
    boolean manualAgreeKyc(CustomerIdentityDTO identity) throws CustomerException;

    boolean manualApproveKyc(String saasId, String customerId) throws CustomerException;

    boolean modifyCustomerAddress(UpdateCustomerAddressRequest request);

    /**
     * 给circle用
     * circle 待审核/审核通过时透传用户住址证明
     *
     * @param request
     * @return
     */
    boolean modifyCustomerAddressAndPassport(UpdateCustomerAddressRequest request);

    /**
     * 给circle用
     * circle 审核失败/拒绝后移除用户住址证明
     * @param request
     * @return
     */
    boolean modifyCustomerKyc2Address(UpdateCustomerKyc2AddressRequest request);


    boolean updateKycReviewInfo(ReviewKycInfoRequest request);

    CustomerIdentityDTO getByCustomerId(String saasId, String customerId);

    /**
     * 用户申请kyc2
     *
     * @param request kyc2申请
     * @throws CustomerException 申请失败
     */
    void kyc2Apply(Kyc2ApplyRequest request) throws CustomerException;

    /**
     * 目前只有aspen使用：提交kyc2信息
     *
     * @param customerIdentityDTO
     * @return
     */
    boolean submitKyc2Info(CustomerIdentityDTO customerIdentityDTO) throws CustomerException;

    /**
     * 获取kyc1列表
     *
     * @param saasId
     * @param limit
     * @param nextToken
     * @return
     */
    TokenPage<CustomerIdentityDTO> listCustomerIdentityByKyc1(String saasId, int limit, String nextToken);

    /**
     * 此接口暂时用于aspen将kyc信息同步到kcustomer，后续删除
     *
     * @param customerIdentityDTO
     * @return
     */
    @Deprecated
    boolean aspenSyncKycInfo(CustomerIdentityDTO customerIdentityDTO);

    void webhook(WebhookRequest webhookRequest) throws Exception;

    void createCheck(KycCheckRequest request) throws CustomerException;

    /**
     * 获取简化kyc信息
     *
     * @param saasId
     * @param customerId
     * @return
     */
    SimpleIdentityDTO getSimpleByCustomerId(String saasId, String customerId);

}
