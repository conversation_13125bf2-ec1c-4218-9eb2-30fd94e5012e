package com.kikitrade.kcustomer.api.model;

import lombok.*;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 解绑请求
 *
 * <AUTHOR>
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UnBindRequest extends CustomerCommonRequest implements Serializable {

    @NotNull
    String customerId;
    @NotNull
    String tokenId;
}

