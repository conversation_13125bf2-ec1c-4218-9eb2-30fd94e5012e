package com.kikitrade.kcustomer.api.service;

import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.*;

/**
 * @author: penuel
 * @date: 2021/7/14 15:10
 * @desc: 通知服务
 */
public interface RemoteNotificationService {


    /**
     * 发送短信通知，走knotify
     * @param smsNotificationDTO
     * @return
     */
    boolean send(SmsNotificationDTO smsNotificationDTO);


    /**
     * 发送邮箱通知,底层直接调用notify发送,新功能可直接调用
     *
     * @param emailNotificationDTO
     * @return
     * @throws CustomerException
     */
    boolean send(EmailNotificationDTO emailNotificationDTO);

    /**
     * 发送native通知(通过firebase)
     *
     * @param messageDTO
     * @return
     * @throws CustomerException
     */
    boolean send(FirebaseMessageDTO messageDTO);

    /**
     * 发送dingTalk消息
     * @param messageDTO
     * @return
     */
    boolean send(DingTalkMessageDTO messageDTO);

    /**
     * 给管理端发送通知（邮件）
     * @param manageMessageDTO
     * @return
     */
    boolean send(ManageMessageDTO manageMessageDTO);

    /**
     * 通用推送（暂时只有IM渠道）
     * @param pushMessageDTO
     * @return
     */
    boolean send(PushMessageDTO pushMessageDTO);

    /**
     * 发送邮箱通知，兼容老版本的邮件模版
     *
     * @param messageDTO
     * @return
     * @throws CustomerException
     */
    @Deprecated
    boolean send(EmailMessageDTO messageDTO);


    /**
     * 发送短信通知，兼容老版本
     *
     * @param messageDTO
     * @return
     * @throws CustomerException
     */
    @Deprecated
    boolean send(SmsMessageDTO messageDTO);
}
