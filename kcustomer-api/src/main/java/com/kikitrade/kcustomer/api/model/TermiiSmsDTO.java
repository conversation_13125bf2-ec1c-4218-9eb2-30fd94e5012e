package com.kikitrade.kcustomer.api.model;


import com.kikitrade.kcustomer.api.constants.TermiiSmsConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TermiiSmsDTO implements Serializable {

    //信息接受者 手机号
    private String receiver;

    //信息发送方
    private String sender;

    //短信内容
    private String sms;

    //短信类型
    private String type;

    //渠道类型
    private String channel;


    private String apiKey;

    //-------传递参数
    private String realName;
    TermiiSmsConstants.SmsTemplater templater;
    private String verifyCode;
    private String referenceNo;//业务订单对应的参考码

    //语音验证码时返回的序列号
    private String messageId;
}
