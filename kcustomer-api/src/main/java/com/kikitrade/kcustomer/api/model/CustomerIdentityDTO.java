package com.kikitrade.kcustomer.api.model;

import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: penuel
 * @date: 2020-07-27 18:48
 * @desc: customer的实名认证第三方信息原始信息，非customer中使用给kiki显示的信息
 */
@Data
public class CustomerIdentityDTO implements Serializable {


    private String saasId;
    private String customerId;
    /**
     * {@link com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants.Status}
     * 认证状态  DRAFT=刚提交正在处理, PENDING=风险高
     */
    private String status;

    private String selfieImageBase64;
    //face compare info
    private String faceComparePic;
    //ocr info
    // 同步完数据后废弃 start
    private String ocrPic;
    private int certifiedType;
    private String birthDate;
    private String countryOfBirth;
    private String documentNumber;
    private String documentType;
    private String firstName;
    private String lastName;
    private String fullName;
    private String expiryDate;
    private String issueDate;
    private String issuingCountry;
    private String nationality;
    // 同步完数据后废弃 end
    private CustomerIdentityConstants.Gender gender;
    private List<String> addresses;
    /**
     * 创建review流程的时间
     */
    private Long reviewTime = 0L;
    /**
     * kyc1审核人
     */
    private String level1Reviewer;
    /**
     * 创建review流程的request id
     */
    private String reviewId;
    /**
     * 创建review流程的reason
     */
    private String reviewReason;

    /**
     * {@link com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants.OcrStatus}
     */
    private Integer ocrTaskStatus;
    private String ocrTaskResult;
    /**
     * {@link com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants.LivenessStatus}
     */
    private Integer faceCompareTaskStatus;
    private String faceCompareTaskResult;
    private String riskReason;

    private String city; //城市
    private String street1; //街道信息
    private String street2;
    private String district; //US，CA必须
    private String postalCode; //用户地区邮编

    //passport ocr info
    private String passportOcrPic;
    private CustomerIdentityConstants.CertifiedType passportCertifiedType;
    //passport parse info
    private String passportBirthDate;
    private String passportExpiryDate;
    private String passportIssueDate;
    private String passportDocumentNumber;
    private String passportFirstName;
    private String passportLastName;
    private String passportMiddleName;
    private String passportCountry;
    private Long passportCertifiedTime = 0L;

    //idCard ocr info
    private String idCardOcrPicFront;
    private String idCardOcrPicBack;
    private CustomerIdentityConstants.CertifiedType idCardCertifiedType;
    //idCard parse info
    private String idCardBirthDate;
    private String idCardExpiryDate;
    private String idCardIssueDate;
    private String idCardDocumentNumber;
    private String idCardFirstName;
    private String idCardLastName;
    private String idCardMiddleName;
    private String idCardCountry;
    private Long idCardCertifiedTime = 0L;
    private String idCardExtraInfo;

    private String region;
    private CustomerIdentityConstants.DocType level1DocumentType;
    private CustomerIdentityConstants.DocType level2DocumentType;
    private CustomerIdentityConstants.KycStatus kycStatus;
    private String kycResult;
    private List<String> accountProof;
    private String residentProof;
    private String surveyResults;

    /**
     * kyc类型：：individual个人 0;institution机构 1
     */
    private CustomerIdentityConstants.KycType kycType;
    /**
     * 个人年收入（USD） 例如 0 - 1,000,000
     */
    private String annualIncome;
    /**
     * 个人收入来源
     */
    private String sourceOfIncome;
    /**
     * Business nature (MC)
     */
    private String companyBusinessNature;
    /**
     * TIN (BR number)
     */
    private String companyBrNumber;
    /**
     * 公司类型 Company type
     */
    private String companyType;
    /**
     * 公司法人姓名 Authorized Representative name
     */
    private String companyRepresentativeName;
    /**
     * 公司法人头衔 Authorized Representative title
     */
    private String companyRepresentativeTitle;
    /**
     * 公司联系地址（个人住宅地址） Contact Address
     */
    private String contactAddress;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司注册国家
     */
    private String companyRegistrationCountry;
    /**
     * 联系电话 Phone number
     */
    private String contactPhone;
    /**
     * 联系邮箱地址 Email address
     */
    private String contactEmail;
    /**
     * 资金来源 Source of fund
     */
    private String sourceOfFund;
    /**
     * 流动资金 Liquid Assets 例如 0 - 1,000,000
     */
    private String liquidAssets;
    /**
     * 总资产 Total Assets 例如 0 - 1,000,000
     */
    private String totalAssets;

    /**
     * 创建时间 aspen迁移数据用，后续删除
     */
    private Date created;
    /**
     * 修改时间 aspen迁移数据用，后续删除
     */
    private Date modified;

    /**
     * 调查问卷
     */
    private SurveyQuestionnaire surveyQuestionnaire;

    /**
     * 问卷风险评分
     */
    private Integer surveyScore;

    @Data
    public static class SurveyQuestionnaire implements Serializable {
        // 个人
        // 个人年收入personalAnnualIncome(美元)
        private String personalAnnualIncome;
        // 个人流动资产净值(美元)
        private String liquidNetWorth;
        // 个人主要资产来源
        private String sourceOfNetWorth;
        // 个人投资金额占流动资产净值比率(美元)
        private String percentageOfNetWorthInvestment;
        // 个人就业状况
        private String employmentStatus;
        // 机构
        // 机构预期年营业额(美元)
        private String expectedAnnualTurnover;
        // 机构资产净值(NAV)(美元)
        private String netAssetValue;
        // 机构主要财富来源
        private String sourceOfNAV;
        // 机构用于投资占资产净值的比率(美元)
        private String percentageOfNAVInvestment;
        // 机构主营业务
        private String mainBusinessNature;
        // 公用
        // 投资经验/培训(请选择最高级别)
        private String investmentExperience;
        // 投资持有期
        private String expectedHoldingPeriod;
        // 投资目标
        private String investmentObjective;
        // 投资组合中可承受最高的损失
        private String lossTolerance;
        // 投资下跌20%最有可能的行动
        private String actionDownByPercent20;
    }

    public String getOcrPicFront(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportOcrPic;
            default:
                return idCardOcrPicFront;
        }
    }

    public String getOcrPicBack(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return null;
            default:
                return idCardOcrPicBack;
        }
    }

    public int getCertifiedType(CustomerIdentityConstants.DocType docType) {
        int certifiedType = 1;
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                if (passportCertifiedType != null) {
                    certifiedType = passportCertifiedType.getCode();
                }
                break;
            default:
                if (idCardCertifiedType != null) {
                    certifiedType = idCardCertifiedType.getCode();
                }
                break;
        }
        return certifiedType;
    }

    public String getBirthDate(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportBirthDate;
            default:
                return idCardBirthDate;
        }
    }

    public String getDocumentNumber(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportDocumentNumber;
            default:
                return idCardDocumentNumber;
        }
    }

    public String getFirstName(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportFirstName;
            default:
                return idCardFirstName;
        }
    }

    public String getMiddleName(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportMiddleName;
            default:
                return idCardMiddleName;
        }
    }

    public String getLastName(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportLastName;
            default:
                return idCardLastName;
        }
    }

    public String getExpiryDate(CustomerIdentityConstants.DocType docType) {
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportExpiryDate;
            default:
                return idCardExpiryDate;
        }
    }

    public String getIssueDate(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportIssueDate;
            default:
                return idCardIssueDate;
        }
    }

    public String getIssuingCountry(CustomerIdentityConstants.DocType docType) {
        if (CustomerIdentityConstants.KycType.INSTITUTION.equals(this.kycType)) {
            return companyRegistrationCountry;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportCountry;
            default:
                return idCardCountry;
        }
    }

    public Long getCertifiedTime(CustomerIdentityConstants.DocType docType) {
        if (docType == null) {
            return null;
        }
        switch (CustomerIdentityConstants.IdType.fromDesc(docType.getValue())) {
            case PASSPORT:
                return passportCertifiedTime;
            default:
                return idCardCertifiedTime;
        }
    }

    public String getFullName(CustomerIdentityConstants.DocType docType) {
        if (CustomerIdentityConstants.KycType.INSTITUTION.equals(this.kycType)) {
            return this.companyName;
        }
        String firstName = this.getFirstName(docType);
        String middleName = this.getMiddleName(docType);
        String lastName = this.getLastName(docType);

        List<String> names = new ArrayList<>();
        if (StringUtils.isNotBlank(lastName)) {
            names.add(lastName);
        }
        if (StringUtils.isNotBlank(middleName)) {
            names.add(middleName);
        }
        if (StringUtils.isNotBlank(firstName)) {
            names.add(firstName);
        }
        return String.join(" ", names);
    }
}
