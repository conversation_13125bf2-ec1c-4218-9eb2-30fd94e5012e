package com.kikitrade.kcustomer.api.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/23 下午3:34
 * @description:
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BindSocialRequest extends CustomerCommonRequest{

    String uid;
    String socialPlatform;
    String socialId;
    String socialName;
    Long twitterCreateTime;
    String xVerifiedType;
}
