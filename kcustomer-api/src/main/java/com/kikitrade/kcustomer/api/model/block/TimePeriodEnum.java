package com.kikitrade.kcustomer.api.model.block;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <h1>周期</h1>
 *
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum TimePeriodEnum {
    MINUTE(60000L),
    HOUR(3600000L),
    DAY(86400000L),
    WEEK(604800000L),
    MONTH(2592000000L),
    YEAR(31536000000L),
    ;
    /**
     * 时间周期
     */
    private long millisecond;

    public long getMillisecond() {
        return millisecond;
    }

    public static TimePeriodEnum fromName(String name) {
        for (TimePeriodEnum value : values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return YEAR;
    }

    public static TimePeriodEnum fromMillisecond(long millisecond) {
        for (TimePeriodEnum value : values()) {
            if (value.getMillisecond() == millisecond) {
                return value;
            }
        }
        return YEAR;
    }

}
