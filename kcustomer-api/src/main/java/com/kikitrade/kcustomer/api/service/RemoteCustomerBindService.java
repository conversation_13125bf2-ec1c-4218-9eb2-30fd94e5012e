package com.kikitrade.kcustomer.api.service;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.BindSocialRequest;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.model.UpdateUserVerifiedRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/24 17:16
 */
public interface RemoteCustomerBindService {

    /**
     * 用户注册并绑定
     * @param customerDTO
     * @return
     */
    CustomerBindDTO register(CustomerDTO customerDTO);

    /**
     * 邀请成功通知
     * @param customerDTO
     * @param inviteCount
     */
    void invite(CustomerDTO customerDTO, Long inviteCount);

    /**
     * 绑定社交账号
     * @param saasId
     * @param uid
     * @param socialId
     * @param socialPlatform
     * @return
     * @throws CustomerException
     */
    boolean bindSocial(String saasId, String uid, String socialPlatform, String socialId, String socialName, Long twitterCreateTime) throws CustomerException;

    boolean updateUserVerified(UpdateUserVerifiedRequest request);

    boolean bindSocial(BindSocialRequest request);

    /**
     * 根据第三方用户 id，查询绑定信息
     * @param saasId
     * @param cid
     * @return
     */
    CustomerBindDTO findById(String saasId, String cid);

    /**
     * 根据第三方用户 id，查询该用户在所有平台下的绑定信息
     * @param cid
     * @return
     */
    List<CustomerBindDTO> findAllByCid(String cid);


    /**
     * 查询第三方用户信息
     * @param saasId
     * @param uid
     * @return
     */
    CustomerBindDTO findByUid(String saasId, String uid);

    /**
     * 根据 uid 查询第三方用户信息
     * @param saasId
     * @param uid
     * @return
     */
    TCustomerDTO findTCustomerByUid(String saasId, String uid);

    /**
     * 根据第三方社区用户信息
     * @param saasId
     * @param socialId
     * @return
     */
    List<TCustomerDTO> findTCustomerBySocial(String saasId, String socialPlatform, String socialId);

    /**
     * 根据第三方社区用户信息
     * @param saasId
     * @param socialName
     * @return
     */
    List<TCustomerDTO> findTCustomerBySocialName(String saasId, String socialPlatform, String socialName);

    /**
     * 解绑
     * @param saasId
     * @param uid
     * @return
     */
    Boolean unbindSocial(String saasId, String uid);

    TokenPage<CustomerBindDTO> listVerifiedBySaasId(String saasId, String token, int limit);
}
