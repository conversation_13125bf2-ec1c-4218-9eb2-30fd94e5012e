package com.drex.customer.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.constant.Constant;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 20:53
 * @description:
 */
@Component
public class CustomerBindBuilderImpl extends WideColumnStoreBuilder<CustomerBind> implements CustomerBindBuilder {
    @Override
    public String getTableName() {
        return Constant.TABLE_NAME_CUSTOMER_BIND;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerBind.class);
    }

    @Override
    public boolean insert(CustomerBind customerBind) {
        return super.putRow(customerBind, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean delete(CustomerBind customerBind) {

        return super.deleteRow(customerBind);
    }

    @Override
    public CustomerBind findByCustomerId(String customerId, String socialPlatform) {
        CustomerBind customerBind = new CustomerBind();
        customerBind.setCustomerId(customerId);
        customerBind.setSocialPlatform(socialPlatform);
        return getRow(customerBind);
    }

    @Override
    public List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms) {
        List<CustomerBind> customerBinds = new ArrayList<>();
        socialPlatforms.forEach(socialPlatform -> {
            CustomerBind customerBind = new CustomerBind();
            customerBind.setCustomerId(customerId);
            customerBind.setSocialPlatform(socialPlatform);
            customerBinds.add(customerBind);
        });
        return batchGetRow(customerBinds);
    }


    @Override
    public CustomerBind findBySocialUserId(String socialUserId, String socialPlatform) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("social_user_id", PrimaryKeyValue.fromString(socialUserId), PrimaryKeyValue.fromString(socialUserId)));
        queryList.add(new RangeQueryParameter("social_platform", PrimaryKeyValue.fromString(socialPlatform), PrimaryKeyValue.fromString(socialPlatform)));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQueryOne(CustomerBind.IDX_CUSTOMER_BIND, queryList);
    }
}
