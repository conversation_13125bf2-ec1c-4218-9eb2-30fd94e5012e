package com.drex.customer.dal.tablestore.constant;

import lombok.Getter;

public interface Constant {
    String TABLE_NAME_CUSTOMER = "customer";
    String INDEX_NAME_CUSTOMER_EOA_INDEX = "customer_eoa_index";
    String INDEX_NAME_CUSTOMER_INVITE_CODE_INDEX = "customer_invite_code_index";
    String TABLE_NAME_CUSTOMER_BIND = "customer_bind";

    String COLUMN_NAME_CUSTOMER_ID = "customer_id";
    String COLUMN_NAME_REGISTER_ADDRESS = "register_address";
    String COLUMN_NAME_EOA_ADDRESS = "eoa_address";
    String COLUMN_NAME_KYC_LEVEL = "kyc_level";
    String COLUMN_NAME_REGISTER_TIME = "register_time";
    String COLUMN_NAME_INVITE_CODE = "invite_code";
    String COLUMN_NAME_STATUS = "status";
    String COLUMN_NAME_CREATED = "created";
    String COLUMN_NAME_USERNAME = "username";
    String TABLE_NAME_CUSTOMER_WAIT_LIST = "customer_wait_list";
    String COLUMN_NAME_NAME = "name";
    String COLUMN_NAME_EMAIL = "email";
    String COLUMN_NAME_CHAIN_ID = "chain_id";
    String COLUMN_NAME_SMART_ACCOUNT_ADDRESS = "smart_account_address";
    String COLUMN_NAME_AUTH_PROVIDER = "auth_provider";
    String COLUMN_NAME_ALL_ACCOUNT = "all_account";

    // 邀请关系表名
    String TABLE_NAME_CUSTOMER_REFERRAL = "customer_referral";
    String INDEX_NAME_CUSTOMER_REFERRAL_INDEX = "search_customer_referral";
    // 邀请人 ID
    String COLUMN_NAME_REFERRER_ID = "referrer_id";
    // 业务类型
    String COLUMN_NAME_BUSINESS_TYPE = "business_type";
    String COLUMN_NAME_PROJECT = "project";
    String COLUMN_NAME_EVM_ADDRESS = "evm_address";
    String COLUMN_NAME_CREATE_TIME = "create_time";


    @Getter
    enum CommonStatus {

        valid(1),
        invalid(0)
        ;

        private int status;

        CommonStatus(int status) {
            this.status = status;
        }
    }
}
