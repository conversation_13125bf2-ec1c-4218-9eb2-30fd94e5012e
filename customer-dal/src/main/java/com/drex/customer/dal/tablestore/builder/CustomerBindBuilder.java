package com.drex.customer.dal.tablestore.builder;

import com.drex.customer.dal.tablestore.model.CustomerBind;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/27 20:53
 * @description:
 */
public interface CustomerBindBuilder {

    String getTableName();

    boolean insert(CustomerBind customerBind);

    boolean delete(CustomerBind customerBind);

    CustomerBind findByCustomerId(String customerId, String socialPlatform);

    List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms);

    CustomerBind findBySocialUserId(String socialUserId, String socialPlatform);
}
