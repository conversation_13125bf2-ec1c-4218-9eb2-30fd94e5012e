package com.kikitrade.kcustomer.dal.builder;

import com.kikitrade.kcustomer.dal.model.CustomerAuthAddressDO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/1 3:15 PM
 * @modify
 */
public interface CustomerAuthAddressBuilder {

    boolean create(CustomerAuthAddressDO addressDO);

    CustomerAuthAddressDO getByStatus(String customerId, String status);

    List<CustomerAuthAddressDO> queryByPublicKey(String publicKey);

    boolean updateStatus(CustomerAuthAddressDO addressDO);

    List<CustomerAuthAddressDO> query(String customerId, String platform);
}
