package com.kikitrade.kcustomer.dal.builder;


import com.kikitrade.kcustomer.dal.model.OtcPaymentWayDO;

import java.util.List;

public interface OtcPaymentWayBuilder {

    boolean save(OtcPaymentWayDO otcCustomerPaymentWay);

    boolean update(OtcPaymentWayDO otcCustomerPaymentWay);

    List<OtcPaymentWayDO> getOtcCustomerPaymentWayDOs(String saasId, String customerId, int offset, int limit);

    OtcPaymentWayDO findById(String id, String customerId);

    boolean deleteById(String id, String customerId);
}