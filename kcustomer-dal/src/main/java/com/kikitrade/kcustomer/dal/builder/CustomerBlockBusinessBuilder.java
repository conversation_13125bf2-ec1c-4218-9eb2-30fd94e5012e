package com.kikitrade.kcustomer.dal.builder;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kcustomer.common.constants.BlockConstants;
import com.kikitrade.kcustomer.dal.model.CustomerBlockBusinessDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomerBlockBusinessBuilder {
    /**
     * 通过主键获取
     *
     * @param customerId customerId
     * @param business   业务场景
     * @return CustomerBlockBusinessDO
     */
    CustomerBlockBusinessDO getById(String customerId, String business);

    /**
     * 通过用户Id获取list
     *
     * @param customerId customerId
     * @return list
     */
    List<CustomerBlockBusinessDO> listByCustomerId(String customerId);

    /**
     * 通过用户Id获取list
     *
     * @param customerIds customerId
     * @return list
     */
    List<CustomerBlockBusinessDO> listByCustomerIds(List<String> customerIds);
    /**
     * 创建一条黑名单记录
     *
     * @param customerBlockBusiness customerBlockBusiness
     * @return boolean
     */
    boolean create(CustomerBlockBusinessDO customerBlockBusiness);

    /**
     * 更新一条黑名单记录
     *
     * @param customerBlockBusiness customerBlockBusiness
     * @return boolean
     */
    boolean update(CustomerBlockBusinessDO customerBlockBusiness);

    /**
     * 删除一条封禁记录
     *
     * @param customerId customerId
     * @param business   业务类型
     * @return boolean
     */
    boolean remove(String customerId, String business);

    /**
     * 获取所有指定时间生效的黑名单记录
     *
     * @param date   如果为null会返回所有记录，包括已过期的记录
     * @param offset 开始位置
     * @param limit  查询个数
     * @param blockType
     * @return list
     */
    List<CustomerBlockBusinessDO> listBlocked(Date date, int offset, int limit, String blockType);

    /**
     * 通过用户id获取所有当前生效的黑名单记录
     *
     * @param customerId customerId
     * @return list
     */
    List<CustomerBlockBusinessDO> listBlockedByCustomerId(String customerId);

    /**
     * 管理端分页查询
     * 用户id为空，只查询当前正在生效的记录
     * 用户id不为空，查询该用户所有记录（包括已过期的记录）
     *
     * @param customerIds customerId
     * @param scene      业务场景
     * @param token      nextToken
     * @param limit      查询个数
     * @return page list
     */
    TokenPage<CustomerBlockBusinessDO> pageList(List<String> customerIds, String scene, String token, int limit);

    boolean deleteRow(CustomerBlockBusinessDO customerBlockBusiness);

}
