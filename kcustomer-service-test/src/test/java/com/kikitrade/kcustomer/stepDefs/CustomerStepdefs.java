package com.kikitrade.kcustomer.stepDefs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.constants.BusinessType;
import com.kikitrade.kcustomer.api.constants.CustomerMessageEnum;
import com.kikitrade.kcustomer.api.constants.KycPlatform;
import com.kikitrade.kcustomer.api.constants.SmsType;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.*;
import com.kikitrade.kcustomer.api.model.block.*;
import com.kikitrade.kcustomer.api.model.kyc.KycCheckRequest;
import com.kikitrade.kcustomer.api.model.kyc.KycRequest;
import com.kikitrade.kcustomer.api.model.kyc.KycTokenRequest;
import com.kikitrade.kcustomer.api.model.kyc.WebhookRequest;
import com.kikitrade.kcustomer.api.service.*;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import com.kikitrade.kcustomer.dal.model.*;
import com.kikitrade.kcustomer.mock.CustomerBindBuilderMock;
import com.kikitrade.kcustomer.mock.CustomerStoreBuilderMock;
import com.kikitrade.kcustomer.mock.NotifyServiceReferenceMock;
import com.kikitrade.kcustomer.op.BusinessOP;
import com.kikitrade.kcustomer.util.CheckUtils;
import com.kikitrade.knotify.api.model.EmailMessageDTO;
import com.kikitrade.knotify.api.model.SmsMessageDTO;
import io.cucumber.java.zh_cn.*;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.kikitrade.kcustomer.op.BusinessOP.bindAddressRes;
import static com.kikitrade.kcustomer.op.BusinessOP.customerDTOMap;

@Slf4j
public class CustomerStepdefs {
    @Resource
    RemoteCustomerService remoteCustomerService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    CustomerStoreBuilderMock customerStoreBuilderMock;
    @Resource
    NotifyServiceReferenceMock notifyServiceReferenceMock;
    @Resource
    RemoteBlockBusinessService remoteBlockBusinessService;
    @Resource
    RemoteCustomerBlockService remoteCustomerBlockService;
    @Resource
    RemoteCustomerIdentityService remoteCustomerIdentityService;
    @Resource
    RemoteCustomerInviteService remoteCustomerInviteService;
    @Resource
    RemoteCustomerOpService remoteCustomerOpService;
    @Resource
    RemoteDeviceInfoService remoteDeviceInfoService;
    @Resource
    RemoteVerifyService remoteVerifyService;
    @Resource
    RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    CustomerBindBuilderMock customerBindBuilderMock;

    @假如("Customer-设置redis值为{}")
    public void customer设置redis值为AccountName(String accountName) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:0:11e1656e-7963-4ac3-9819-67b9af4eae05", accountName);
    }

    @假如("Customer-设置redis2值为{}")
    public void customer设置redis2值为AccountName(String accountName) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:0:11e1656e-7963-4ac3-9819-67b9af4eae06", accountName);
    }

    @假如("Customer-收到用户的注册请求{}")
    public void customer收到用户的注册请求CustomerRegisterRequest(String customerRegisterRequest) {
        List<CustomerRegisterRequest> customerRegisterRequestList = JSON.parseArray(customerRegisterRequest, CustomerRegisterRequest.class);
        customerRegisterRequestList.forEach(v -> {
            try {
                remoteCustomerService.register(v);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @那么("Customer-校验产生的customerDO{}")
    public void customer校验产生的customerDOCustomerDO(String customerDO) {
        HashMap<String, CustomerDO> customerDOMap = BusinessOP.customerDOMap;
        List<CustomerDO> CustomerDOSAct = customerDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<CustomerDO>{}", JSON.toJSONString(CustomerDOSAct));
        List<CustomerDO> CustomerDOSExp = JSON.parseArray(customerDO, CustomerDO.class);
        log.info(">>Expect--List<CustomerDO>{}", JSON.toJSONString(CustomerDOSExp));
        CheckUtils.compare(CustomerDOSAct, CustomerDOSExp, new String[]{"id", "openId", "updateTime"});
    }

    @并且("Customer-校验产生的customerInviteDO{}")
    public void customer校验产生的customerInviteDOCustomerInviteDO(String customerInviteDO) {
        HashMap<String, CustomerInviteDO> customerDOMap = BusinessOP.customerInviteDOMap;
        List<CustomerInviteDO> CustomerDOSAct = customerDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<CustomerInviteDO>{}", JSON.toJSONString(CustomerDOSAct));
        List<CustomerInviteDO> CustomerDOSExp = JSON.parseArray(customerInviteDO, CustomerInviteDO.class);
        log.info(">>Expect--List<CustomerInviteDO>{}", JSON.toJSONString(CustomerDOSExp));
        CheckUtils.compare(CustomerDOSAct, CustomerDOSExp, new String[]{"inviteeId", "createTime", "code", "historyCodes", "inviterId", "rootInviterId", "registerTime"});
    }

    @并且("Customer-校验exceptions{}")
    public void customer校验exceptionsExceptions(String exceptions) {
        //实际
        List<Exception> exceptionsAct = BusinessOP.exceptions;
        log.info("exceptionsAct:{}", JSON.toJSONString(exceptionsAct));
        //期望
        List<String> list = JSON.parseArray(exceptions, String.class);
        if (list.size() == 0) {
            Assert.assertEquals(0, exceptionsAct.size());
            return;
        }
        Assert.assertEquals(list.size(), exceptionsAct.size());

        for (int i = 0; i < list.size(); i++) {
            //实际
            String s = exceptionsAct.get(i).toString();
            //期望
            String s1 = list.get(i);
            Assert.assertTrue(s.contains(s1));
        }
    }

    @假如("Customer-并发收到用户的注册请求{}")
    public void customer并发收到用户的注册请求CustomerRegisterRequest(String customerRegisterRequest) {
        List<CustomerRegisterRequest> customerRegisterRequestList = JSON.parseArray(customerRegisterRequest, CustomerRegisterRequest.class);
        int len = customerRegisterRequestList.size();
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch latch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);
        for (int i = 0; i < len; i++) {
            int finalI = i;
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        latch.countDown();
                        downLatch.await();
                        try {
                            remoteCustomerService.register(customerRegisterRequestList.get(finalI));
                        } catch (Exception e) {
                            BusinessOP.exceptions.add(e);
                            log.error(">>异常捕获{}", e);
                        }
                        Thread.sleep(3000);
                    } catch (Exception e) {
                        log.info("捕获异常：{}", e);
                    }
                    endLatch.countDown();
                }
            }).start();
        }
        try {
            latch.await();
            downLatch.countDown();
            endLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @假如("Customer-并发设置redis值为{}")
    public void customer并发设置redis值为AccountName(String accountName) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:0:11e1656e-7963-4ac3-9819-67b9af4eae05", accountName);
    }

    @假如("Customer-并发2设置redis值为{}")
    public void customer并发2设置redis值为AccountName(String accountName2) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:0:11e1656e-7963-4ac3-9819-67b9af4eae06", accountName2);
    }

    @假如("login-时存在customerDO{}")
    public void login时存在customerDOCustomerDO(String customerDO) {
        List<CustomerDO> customerDOS = JSON.parseArray(customerDO, CustomerDO.class);
        customerDOS.forEach(v -> BusinessOP.customerDOMap.put(v.getId(), v));
    }

    @并且("login-时存在customerInviteDO{}")
    public void login时存在customerInviteDOCustomerInviteDO(String customerInviteDO) {
        List<CustomerInviteDO> customerInviteDOS = JSON.parseArray(customerInviteDO, CustomerInviteDO.class);
        customerInviteDOS.forEach(v -> BusinessOP.customerInviteDOMap.put(v.getInviteeId(), v));
    }

    @当("收到用户的登录请求{}结果为{}")
    public void 收到用户的登录请求CustomerLoginRequest结果为CustomerDTO(String customerLoginRequest, String customerDTO) {
        List<CustomerLoginRequest> customerLoginRequests = JSON.parseArray(customerLoginRequest, CustomerLoginRequest.class);
        CustomerDTO customerDTOExp = JSON.parseObject(customerDTO, CustomerDTO.class);
        customerLoginRequests.forEach(v -> {
            try {
                CustomerDTO customerDTOAct = remoteCustomerService.login(v);
                log.info(">>Actual--List<CustomerDTO>{}", JSON.toJSONString(customerDTOAct));
                CheckUtils.compare(customerDTOAct, customerDTOExp);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @当("login-收到用户的请求并发处理{}结果为{}")
    public void login收到用户的请求并发处理CustomerLoginRequest结果为CustomerDTO(String customerLoginRequest, String customerDTO) {
        List<CustomerLoginRequest> customerLoginRequests = JSON.parseArray(customerLoginRequest, CustomerLoginRequest.class);
        CustomerDTO customerDTOExp = JSON.parseObject(customerDTO, CustomerDTO.class);
        int len = customerLoginRequests.size();
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch latch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);
        for (int i = 0; i < len; i++) {
            int finalI = i;
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        latch.countDown();
                        downLatch.await();
                        try {
                            CustomerDTO customerDTOAct = remoteCustomerService.login(customerLoginRequests.get(finalI));
                            log.info(">>Actual--List<CustomerDTO>{}", JSON.toJSONString(customerDTOAct));
                        } catch (Exception e) {
                            BusinessOP.exceptions.add(e);
                            log.error(">>异常捕获{}", e);
                        }
                        Thread.sleep(3000);
                    } catch (Exception e) {
                        log.info("捕获异常：{}", e);
                    }
                    endLatch.countDown();
                }
            }).start();
        }
        try {
            latch.await();
            downLatch.countDown();
            endLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    @当("setAssetPassword-收到用户的设置资金密码{}")
    public void setassetpassword收到用户的设置资金密码AssetPwdRequest(String assetPwdRequest) throws CustomerException {
        AssetPwdRequest assetPwdRequest1 = JSON.parseObject(assetPwdRequest, AssetPwdRequest.class);
        try {
            remoteCustomerService.setAssetPassword(assetPwdRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("setAssetPassword-设置redis值为{}")
    public void setassetpassword设置redis值为Asset(String asset) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:19:123123", asset);
    }

    @假如("setAssetPassword-修改资金密码设置redis值为{}")
    public void setassetpassword修改资金密码设置redis值为Asset(String asset) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:20:123123", asset);
    }

    @并且("customerStoreBuilder.updateAssetPassword异常")
    public void customerstorebuilderUpdateAssetPassword异常() {
        customerStoreBuilderMock.updateAssetPasswordException();
    }

    @当("tryUpdateVipLevel-收到tryUpdateVipLevel请求{}")
    public void tryupdateviplevel收到tryUpdateVipLevel请求UpdateVipRequest(String updateVipRequest) throws CustomerException {
        UpdateVipRequest updateVipRequest1 = JSON.parseObject(updateVipRequest, UpdateVipRequest.class);
        try {
            remoteCustomerService.tryUpdateVipLevel(updateVipRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @当("UpdateVipLevel-收到UpdateVipLevel请求{}")
    public void updateviplevel收到UpdateVipLevel请求UpdateVipRequest(String updateVipRequest) throws CustomerException {
        UpdateVipRequest updateVipRequest1 = JSON.parseObject(updateVipRequest, UpdateVipRequest.class);
        try {
            remoteCustomerService.updateVipLevel(updateVipRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @当("tryUpdateVip-收到tryUpdateVip请求{}")
    public void tryupdatevip收到tryUpdateVip请求UpdateVipRequest(String updateVipRequest) {
        UpdateVipRequest updateVipRequest1 = JSON.parseObject(updateVipRequest, UpdateVipRequest.class);
        try {
            remoteCustomerService.tryUpdateVip(updateVipRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @当("updateVip-收到updateVip请求{}")
    public void updatevip收到updateVip请求UpdateVipRequest(String updateVipRequest) {
        UpdateVipRequest updateVipRequest1 = JSON.parseObject(updateVipRequest, UpdateVipRequest.class);
        try {
            remoteCustomerService.updateVip(updateVipRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @当("sign-收到sign请求{}结果为{}")
    public void sign收到sign请求SignRequest结果为SignInfoDTO(String signRequest, String signInfoDTO) {
        JSONObject jsonObject = JSON.parseObject(signRequest);
        SignInfoDTO signInfoDTO1 = JSON.parseObject(signInfoDTO, SignInfoDTO.class);
        String saasId = jsonObject.getString("saasId");
        String deviceId = jsonObject.getString("deviceId");
        String customerId = jsonObject.getString("customerId");
        try {
            SignInfoDTO sign = remoteCustomerService.sign(saasId, deviceId, customerId);
            log.info(">>Actual--List<SignInfoDTO>{}", JSON.toJSONString(sign));
            CheckUtils.compare(sign, signInfoDTO1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @那么("Customer-校验产生的CustomerOpenAuthDO{}")
    public void customer校验产生的CustomerOpenAuthDOCustomerOpenAuthDO(String customerOpenAuthDO) {
        HashMap<String, CustomerOpenAuthDO> customerDOMap = BusinessOP.customerOpenAuthDOMap;
        List<CustomerOpenAuthDO> CustomerDOSAct = customerDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<CustomerOpenAuthDO>{}", JSON.toJSONString(CustomerDOSAct));
        List<CustomerOpenAuthDO> CustomerDOSExp = JSON.parseArray(customerOpenAuthDO, CustomerOpenAuthDO.class);
        log.info(">>Expect--List<CustomerOpenAuthDO>{}", JSON.toJSONString(CustomerDOSExp));
        CheckUtils.compare(CustomerDOSAct, CustomerDOSExp, new String[]{"bindTime"});
    }

    @并且("notifyServiceReference.getUserSig为空")
    public void notifyservicereferenceGetUserSig为空() {
        notifyServiceReferenceMock.getUserSigNull();
    }

    @并且("bind-收到bind请求{}")
    public void bind收到bind请求ImBindRequest(String imBindRequest) throws CustomerException {
        ImBindRequest imBindRequest1 = JSON.parseObject(imBindRequest, ImBindRequest.class);
        try {
            remoteCustomerService.bind(imBindRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("Customer-收到用户的快速注册请求{}")
    public void customer收到用户的快速注册请求CustomerRegisterRequest(String customerRegisterRequest) {
        List<CustomerRegisterRequest> customerRegisterRequestList = JSON.parseArray(customerRegisterRequest, CustomerRegisterRequest.class);
        customerRegisterRequestList.forEach(v -> {
            try {
                remoteCustomerService.quickRegister(v);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @当("收到用户的quickLogin请求{}结果为{}")
    public void 收到用户的quicklogin请求CustomerLoginRequest结果为CustomerDTO(String customerLoginRequest, String customerDTO) {
        List<CustomerLoginRequest> customerLoginRequests = JSON.parseArray(customerLoginRequest, CustomerLoginRequest.class);
        CustomerDTO customerDTOExp = JSON.parseObject(customerDTO, CustomerDTO.class);
        customerLoginRequests.forEach(v -> {
            try {
                CustomerDTO customerDTOAct = remoteCustomerService.login(v);
                log.info(">>Actual--List<CustomerDTO>{}", JSON.toJSONString(customerDTOAct));
                CheckUtils.compare(customerDTOAct, customerDTOExp);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @假如("decide-收到用户的风控请求{}")
    public void decide收到用户的风控请求CreateBlockRequest(String createBlockRequest) {
        CreateBlockRequest createBlockRequest1 = JSON.parseObject(createBlockRequest, CreateBlockRequest.class);
        try {
            remoteBlockBusinessService.decide(createBlockRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @那么("Customer-校验产生的CustomerBlockBusinessDO{}")
    public void customer校验产生的CustomerBlockBusinessDOCustomerBlockBusinessDO(String customerBlockBusinessDO) {
        HashMap<String, CustomerBlockBusinessDO> customerDOMap = BusinessOP.customerBlockBusinessDOMap;
        List<CustomerBlockBusinessDO> CustomerDOSAct = customerDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<CustomerBlockBusinessDO>{}", JSON.toJSONString(CustomerDOSAct));
        List<CustomerBlockBusinessDO> CustomerDOSExp = JSON.parseArray(customerBlockBusinessDO, CustomerBlockBusinessDO.class);
        log.info(">>Expect--List<CustomerBlockBusinessDO>{}", JSON.toJSONString(CustomerDOSExp));
        CheckUtils.compare(CustomerDOSAct, CustomerDOSExp, new String[]{"created", "modified", "startTime"});
    }

    @假如("verify-收到用户的风控决策请求{}结果为{}")
    public void verify收到用户的风控决策请求SecurityVerifyRequest结果为Res(String securityVerifyRequest, String res) {
        try {
            SecurityVerifyRequest securityVerifyRequest1 = JSON.parseObject(securityVerifyRequest, SecurityVerifyRequest.class);
            SecurityVerifyResponse verify = remoteBlockBusinessService.verify(securityVerifyRequest1);
            log.info("SecurityVerifyResponse:{}", verify.toString());
            Assert.assertEquals(res, String.valueOf(verify.isSuccess()));
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @并且("unblock-时存在CustomerBlockBusinessDO{}")
    public void unblock时存在CustomerBlockBusinessDOCustomerBlockBusinessDO(String customerBlockBusinessDO) {
        List<CustomerBlockBusinessDO> customerBlockBusinessDOS = JSON.parseArray(customerBlockBusinessDO, CustomerBlockBusinessDO.class);
        customerBlockBusinessDOS.forEach(v -> BusinessOP.customerBlockBusinessDOMap.put(v.getCustomerId(), v));
    }

    @假如("unblock-收到用户的解除用户封禁请求{}")
    public void unblock收到用户的解除用户封禁请求UnblockCustomerRequest(String unblockCustomerRequest) {
        try {
            UnblockCustomerRequest unblockCustomerRequest1 = JSON.parseObject(unblockCustomerRequest, UnblockCustomerRequest.class);
            remoteBlockBusinessService.unblock(unblockCustomerRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("block-收到用户的风控请求{}")
    public void block收到用户的风控请求BlockCustomerRequest(String blockCustomerRequest) {
        BlockCustomerRequest blockCustomerRequest1 = JSON.parseObject(blockCustomerRequest, BlockCustomerRequest.class);
        try {
            UpdateBlockResponse block = remoteCustomerBlockService.block(blockCustomerRequest1);
            log.info("UpdateBlockResponse:{}", block.toString());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("Block-unblock-收到用户的解除用户封禁请求{}")
    public void blockUnblock收到用户的解除用户封禁请求UnblockCustomerRequest(String unblockCustomerRequest) {
        try {
            UnblockCustomerRequest unblockCustomerRequest1 = JSON.parseObject(unblockCustomerRequest, UnblockCustomerRequest.class);
            remoteCustomerBlockService.unblock(unblockCustomerRequest1);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("getSdkAccessToken-收到用户的请求{}结果为{}")
    public void getsdkaccesstoken收到用户的请求KycTokenRequest结果为Val(String kycTokenRequest, String res) {
        try {
            JSONObject jsonObject = JSON.parseObject(kycTokenRequest, JSONObject.class);
            KycTokenRequest build = KycTokenRequest.builder()
                    .customerId(jsonObject.getString("customerId"))
                    .kycPlatform(KycPlatform.fromCode(jsonObject.getInteger("kycPlatform")))
                    .saasId(jsonObject.getString("saasId"))
                    .metaInfo(jsonObject.getString("metaInfo")).build();
            CustomerCommonResponse sdkAccessToken = remoteCustomerIdentityService.getSdkAccessToken(build);
            log.info("CustomerCommonResponse:{}", sdkAccessToken.toString());
            Assert.assertEquals(res, String.valueOf(sdkAccessToken.getResult()));
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @那么("Customer-校验产生的CustomerIdentityDO{}")
    public void customer校验产生的CustomerIdentityDOCustomerIdentityDO(String customerIdentityDO) {
        HashMap<String, CustomerIdentityDO> customerDOMap = BusinessOP.customerIdentityDOMap;
        List<CustomerIdentityDO> CustomerDOSAct = customerDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<CustomerIdentityDO>{}", JSON.toJSONString(CustomerDOSAct));
        List<CustomerIdentityDO> CustomerDOSExp = JSON.parseArray(customerIdentityDO, CustomerIdentityDO.class);
        log.info(">>Expect--List<CustomerIdentityDO>{}", JSON.toJSONString(CustomerDOSExp));
        CheckUtils.compare(CustomerDOSAct, CustomerDOSExp, new String[]{"faceCompareTaskTime", "ocrTaskTime", "inviteeId", "createTime", "code", "historyCodes", "inviterId", "rootInviterId", "registerTime", "reviewTime", "kyc2CreateTime"});
    }

    @并且("kyc-时存在CustomerIdentityDO{}")
    public void kyc时存在CustomerIdentityDOCustomerIdentityDO(String customerIdentityDO) {
        List<CustomerIdentityDO> customerIdentityDOS = JSON.parseArray(customerIdentityDO, CustomerIdentityDO.class);
        customerIdentityDOS.forEach(v -> BusinessOP.customerIdentityDOMap.put(v.getCustomerId(), v));
    }

    @并且("kyc-设置redis值为{}")
    public void kyc设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:ONFIDO:SDKTOKEN:25b4f807-e140-40b1-b149-442a2cec2007", val);
    }

    @并且("删除redis的值")
    public void 删除redis的值() {
        redisTemplate.delete("CUSTOMER:ONFIDO:SDKTOKEN:25b4f807-e140-40b1-b149-442a2cec2007");
    }

    @假如("uploadOcrImage-收到用户的请求{}结果为{}")
    public void uploadocrimage收到用户的请求KycRequest结果为Res(String kycRequest, String res) {
        try {
            KycRequest kycRequest1 = JSON.parseObject(kycRequest, KycRequest.class);
            CustomerCommonResponse customerCommonResponse = remoteCustomerIdentityService.uploadOcrImage(kycRequest1);
            log.info("CustomerCommonResponse:{}", customerCommonResponse.toString());
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), String.valueOf(customerCommonResponse.getCode()));
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(customerCommonResponse.getKey()));
            Assert.assertEquals(jsonObject.getBoolean("success"), customerCommonResponse.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("uploadFaceCompare-收到用户的请求{}结果为{}")
    public void uploadfacecompare收到用户的请求KycRequest结果为Res(String kycRequest, String res) {
        try {
            JSONObject kycJson = JSON.parseObject(kycRequest, JSONObject.class);
            String saasId = kycJson.getString("saasId");
            String customerId = kycJson.getString("customerId");
            String certifyId = kycJson.getString("certifyId");
            String businessInfo = kycJson.getString("businessInfo");
            CustomerCommonResponse customerCommonResponse = remoteCustomerIdentityService.uploadFaceCompare(saasId, customerId, certifyId, businessInfo);
            log.info("CustomerCommonResponse:{}", customerCommonResponse.toString());
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), String.valueOf(customerCommonResponse.getCode()));
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(customerCommonResponse.getKey()));
            Assert.assertEquals(jsonObject.getBoolean("success"), customerCommonResponse.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("getIdentityResult-收到用户的请求{}结果为{}")
    public void getidentityresult收到用户的请求KycRequest结果为Res(String kycRequest, String res) {
        try {
            JSONObject kycJson = JSON.parseObject(kycRequest, JSONObject.class);
            String saasId = kycJson.getString("saasId");
            String customerId = kycJson.getString("customerId");
            CustomerCommonResponse customerCommonResponse = remoteCustomerIdentityService.getIdentityResult(saasId, customerId);
            log.info("CustomerCommonResponse:{}", customerCommonResponse.toString());
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), String.valueOf(customerCommonResponse.getCode()));
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(customerCommonResponse.getKey()));
            Assert.assertEquals(jsonObject.getBoolean("success"), customerCommonResponse.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("getFullIdentityResult-收到用户的请求{}结果为{}")
    public void getfullidentityresult收到用户的请求KycRequest结果为Res(String kycRequest, String res) {
        try {
            JSONObject kycJson = JSON.parseObject(kycRequest, JSONObject.class);
            String saasId = kycJson.getString("saasId");
            String customerId = kycJson.getString("customerId");
            CustomerCommonResponse customerCommonResponse = remoteCustomerIdentityService.getFullIdentityResult(saasId, customerId);
            log.info("CustomerCommonResponse:{}", customerCommonResponse.toString());
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), String.valueOf(customerCommonResponse.getCode()));
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(customerCommonResponse.getKey()));
            Assert.assertEquals(jsonObject.getBoolean("success"), customerCommonResponse.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("manualRejectKyc-收到用户的请求{}结果为{}")
    public void manualrejectkyc收到用户的请求KycRequest结果为Res(String kycRequest, String res) {
        try {
            JSONObject kycJson = JSON.parseObject(kycRequest, JSONObject.class);
            String saasId = kycJson.getString("saasId");
            String customerId = kycJson.getString("customerId");
            CustomerIdentityConstants.ReviewReason reviewReason = CustomerIdentityConstants.ReviewReason.valueOf(kycJson.getString("reviewReason"));
            boolean b = remoteCustomerIdentityService.manualRejectKyc(saasId, customerId, reviewReason);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("manualApproveKyc-收到用户的请求{}结果为{}")
    public void manualapprovekyc收到用户的请求KycRequest结果为Res(String kycRequest, String res) {
        try {
            JSONObject kycJson = JSON.parseObject(kycRequest, JSONObject.class);
            String saasId = kycJson.getString("saasId");
            String customerId = kycJson.getString("customerId");
            boolean b = remoteCustomerIdentityService.manualApproveKyc(saasId, customerId);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("modifyCustomerAddress-收到用户的请求{}结果为{}")
    public void modifycustomeraddress收到用户的请求Request结果为Res(String request, String res) {
        try {
            UpdateCustomerAddressRequest updateCustomerAddressRequest = JSON.parseObject(request, UpdateCustomerAddressRequest.class);
            boolean b = remoteCustomerIdentityService.modifyCustomerAddress(updateCustomerAddressRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("modifyCustomerAddressAndPassport-收到用户的请求{}结果为{}")
    public void modifycustomeraddressandpassport收到用户的请求Request结果为Res(String request, String res) {
        try {
            UpdateCustomerAddressRequest updateCustomerAddressRequest = JSON.parseObject(request, UpdateCustomerAddressRequest.class);
            boolean b = remoteCustomerIdentityService.modifyCustomerAddressAndPassport(updateCustomerAddressRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("modifyCustomerKyc2Address-收到用户的请求{}结果为{}")
    public void modifycustomerkyc2Address收到用户的请求Request结果为Res(String request, String res) {
        try {
            UpdateCustomerKyc2AddressRequest updateCustomerKyc2AddressRequest = JSON.parseObject(request, UpdateCustomerKyc2AddressRequest.class);
            boolean b = remoteCustomerIdentityService.modifyCustomerKyc2Address(updateCustomerKyc2AddressRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("updateKycReviewInfo-收到用户的请求{}结果为{}")
    public void updatekycreviewinfo收到用户的请求Request结果为Res(String request, String res) {
        try {
            ReviewKycInfoRequest reviewKycInfoRequest = JSON.parseObject(request, ReviewKycInfoRequest.class);
            boolean b = remoteCustomerIdentityService.updateKycReviewInfo(reviewKycInfoRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("kyc2Apply-收到用户的请求{}结果为{}")
    public void kyc2Apply收到用户的请求Request结果为Res(String request, String res) {
        try {
            Kyc2ApplyRequest kyc2ApplyRequest = JSON.parseObject(request, Kyc2ApplyRequest.class);
            remoteCustomerIdentityService.kyc2Apply(kyc2ApplyRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("submitKyc2Info-收到用户的请求{}结果为{}")
    public void submitkycInfo收到用户的请求Request结果为Res(String request, String res) {
        try {
            CustomerIdentityDTO customerIdentityDTO = JSON.parseObject(request, CustomerIdentityDTO.class);
            remoteCustomerIdentityService.submitKyc2Info(customerIdentityDTO);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("createCheck-收到用户的请求{}结果为{}")
    public void createcheck收到用户的请求Request结果为Res(String request, String res) {
        try {
            KycCheckRequest kycCheckRequest = JSON.parseObject(request, KycCheckRequest.class);
            remoteCustomerIdentityService.createCheck(kycCheckRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("webhook-收到用户的请求{}结果为{}")
    public void webhook收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject = JSON.parseObject(request, JSONObject.class);
            WebhookRequest webhookRequest = WebhookRequest.builder().signature(jsonObject.getString("signature"))
                    .body(jsonObject.getString("body"))
                    .build();
            remoteCustomerIdentityService.webhook(webhookRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("inviterByCustomerId-收到用户的请求{}结果为{}")
    public void inviterbycustomerid收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject.getString("saasId");
            String customerId = jsonObject.getString("customerId");
            remoteCustomerInviteService.inviterByCustomerId(saasId, customerId);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("inviterByInviteCode-收到用户的请求{}结果为{}")
    public void inviterbyinvitecode收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject.getString("saasId");
            String inviteCode = jsonObject.getString("inviteCode");
            remoteCustomerInviteService.inviterByInviteCode(saasId, inviteCode);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("insertCustomerInvite-收到用户的请求{}结果为{}")
    public void insertcustomerinvite收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject.getString("saasId");
            String inviterEmail = jsonObject.getString("inviterEmail");
            String inviteeId = jsonObject.getString("inviteeId");
            remoteCustomerInviteService.insertCustomerInvite(saasId, inviterEmail, inviteeId);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("bindRelation-收到用户的请求{}结果为{}")
    public void bindrelation收到用户的请求Request结果为Res(String request, String res) {
        try {
            BindInviteRelationRequest bindInviteRelationRequest = JSON.parseObject(request, BindInviteRelationRequest.class);
            remoteCustomerInviteService.bindRelation(bindInviteRelationRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("setPassword-收到用户的请求{}结果为{}")
    public void setpassword收到用户的请求Request结果为Res(String request, String res) {
        try {
            SetPwdRequest setPwdRequest = JSON.parseObject(request, SetPwdRequest.class);
            remoteCustomerOpService.setPassword(setPwdRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("setPassword-设置redis值为{}")
    public void setpassword设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:6:PWD", val);
    }

    @假如("bindEmail-收到用户的请求{}结果为{}")
    public void bindemail收到用户的请求Request结果为Res(String request, String res) {
        try {
            BindEmailRequest bindEmailRequest = JSON.parseObject(request, BindEmailRequest.class);
            remoteCustomerOpService.bindEmail(bindEmailRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("bindEmail-设置redis值为{}")
    public void bindemail设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:EMAIL:<EMAIL>", val);
    }

    @假如("replaceBindEmail-收到用户的请求{}结果为{}")
    public void replacebindemail收到用户的请求Request结果为Res(String request, String res) {
        try {
            ReplaceBindEmailRequest replaceBindEmailRequest = JSON.parseObject(request, ReplaceBindEmailRequest.class);
            remoteCustomerOpService.replaceBindEmail(replaceBindEmailRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("replaceBindEmail-设置redis值为{}")
    public void replaceBindEmail设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:EMAIL:<EMAIL>", val);
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:11:<EMAIL>", "20230928165459851765140000");
    }

    @假如("unBindEmail-收到用户的请求{}结果为{}")
    public void unbindemail收到用户的请求Request结果为Res(String request, String res) {
        try {
            UnBindRequest unBindRequest = JSON.parseObject(request, UnBindRequest.class);
            remoteCustomerOpService.unBindEmail(unBindRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("unBindEmail-设置redis值为{}")
    public void unbindemail设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:9:<EMAIL>", val);
    }

    @假如("bindPhone-收到用户的请求{}结果为{}")
    public void bindphone收到用户的请求Request结果为Res(String request, String res) {
        try {
            BindPhoneRequest bindPhoneRequest = JSON.parseObject(request, BindPhoneRequest.class);
            remoteCustomerOpService.bindPhone(bindPhoneRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("bindPhone-设置redis值为{}")
    public void bindphone设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:PHONE:32143214", val);
    }

    @假如("replaceBindPhone-收到用户的请求{}结果为{}")
    public void replacebindphone收到用户的请求Request结果为Res(String request, String res) {
        try {
            ReplaceBindPhoneRequest replaceBindPhoneRequest = JSON.parseObject(request, ReplaceBindPhoneRequest.class);
            remoteCustomerOpService.replaceBindPhone(replaceBindPhoneRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("replaceBindPhone-设置redis值为{}")
    public void replacebindphone设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:PHONE:***********", val);
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:12:12345678", "20230928165459851765140000");
    }

    @假如("unBindPhone-收到用户的请求{}结果为{}")
    public void unbindphone收到用户的请求Request结果为Res(String request, String res) {
        try {
            UnBindRequest unBindRequest = JSON.parseObject(request, UnBindRequest.class);
            remoteCustomerOpService.unBindPhone(unBindRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("unBindPhone-设置redis值为{}")
    public void unbindphone设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:10:12345678", val);
    }

    @假如("bindGoogle-收到用户的请求{}结果为{}")
    public void bindgoogle收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject.getString("saasId");
            String customerId = jsonObject.getString("customerId");
            CustomerCommonResponse customerCommonResponse = remoteCustomerOpService.generateGoogleKey(saasId, customerId);
            BindGoogleRequest bindGoogleRequest = JSON.parseObject(request, BindGoogleRequest.class);
            bindGoogleRequest.setGoogleKey(customerCommonResponse.getResult().toString());
            remoteCustomerOpService.bindGoogle(bindGoogleRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("unBindGoogle-收到用户的请求{}结果为{}")
    public void unbindgoogle收到用户的请求Request结果为Res(String request, String res) {
        try {
            UnBindRequest unBindRequest = JSON.parseObject(request, UnBindRequest.class);
            remoteCustomerOpService.unBindGoogle(unBindRequest);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("unBindGoogle-设置redis值为{}")
    public void unbindgoogle设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:16:12345678", val);
    }

    @假如("RemoteDeviceInfoService.add-收到用户的请求{}结果为{}")
    public void remotedeviceinfoserviceAdd收到用户的请求Request结果为Res(String request, String res) {
        try {
            DeviceInfoDTO deviceInfoDTO = JSON.parseObject(request, DeviceInfoDTO.class);
            remoteDeviceInfoService.add(deviceInfoDTO);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @那么("Customer-校验产生的deviceInfoDO{}")
    public void customer校验产生的deviceInfoDODeviceInfoDO(String deviceInfoDO) {
        HashMap<String, DeviceInfoDO> deviceInfoDOMap = BusinessOP.deviceInfoDOMap;
        List<DeviceInfoDO> deviceInfoDOSACT = deviceInfoDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<DeviceInfoDO>{}", JSON.toJSONString(deviceInfoDOSACT));
        List<DeviceInfoDO> deviceInfoDOSExp = JSON.parseArray(deviceInfoDO, DeviceInfoDO.class);
        log.info(">>Expect--List<DeviceInfoDO>{}", JSON.toJSONString(deviceInfoDOSExp));
        CheckUtils.compare(deviceInfoDOSACT, deviceInfoDOSExp, new String[]{"created", "modified", "operations", "lastLoginTime"});
    }

    @并且("login-时存在deviceInfoDO{}")
    public void login时存在deviceInfoDODeviceInfoDO(String deviceInfoDO) {
        DeviceInfoDO deviceInfoDO1 = JSON.parseObject(deviceInfoDO, DeviceInfoDO.class);
        BusinessOP.deviceInfoDOMap.put(deviceInfoDO1.getDeviceId(), deviceInfoDO1);
    }

    @假如("getSmsCode-收到用户的请求{}结果为{}")
    public void getsmscode收到用户的请求Request结果为Res(String request, String res) {
        try {

            SmsCodeRequest smsCodeRequest = JSON.parseObject(request, SmsCodeRequest.class);
            CustomerCommonResponse smsCode = remoteVerifyService.getSmsCode(smsCodeRequest);
            log.info("SMS Code: {}" + smsCode);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), String.valueOf(smsCode.getCode()));
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(smsCode.getMessageEnum()));
            Assert.assertEquals(jsonObject.getBoolean("success"), smsCode.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("getSmsCode-设置redis值为{}")
    public void getsmscode设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VT:PHONE:+***********", val);
    }

    @那么("Customer-校验产生的smsMessageDTO{}")
    public void customer校验产生的smsMessageDTOSmsMessageDTO(String smsMessageDTO) {
        List<SmsMessageDTO> smsMsgsACT = BusinessOP.smsMsgs;
        log.info(">>Actual--List<SmsMessageDTO>{}", JSON.toJSONString(smsMsgsACT));
        List<SmsMessageDTO> smsMsgsEXP = JSON.parseArray(smsMessageDTO, SmsMessageDTO.class);
        log.info(">>Expect--List<SmsMessageDTO>{}", JSON.toJSONString(smsMsgsEXP));
        CheckUtils.compare(smsMsgsACT, smsMsgsEXP, new String[]{"parameterMap", "code"});
    }

    @假如("getEmailCode-设置redis值为{}")
    public void getemailcode设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VT:EMAIL:<EMAIL>", val);
    }

    @假如("getEmailCode-收到用户的请求{}结果为{}")
    public void getemailcode收到用户的请求Request结果为Res(String request, String res) {
        try {
            EmailCodeRequest emailCodeRequest = JSON.parseObject(request, EmailCodeRequest.class);
            CustomerCommonResponse emailCode = remoteVerifyService.getEmailCode(emailCodeRequest);
            log.info("SMS Code: {}" + emailCode);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), emailCode.getCode());
            Assert.assertEquals(jsonObject.getString("key"), emailCode.getMessageEnum());
            Assert.assertEquals(jsonObject.getBoolean("success"), emailCode.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @那么("Customer-校验产生的emailMessageDTO{}")
    public void customer校验产生的emailMessageDTOEmailMessageDTO(String emailMessageDTO) {
        List<EmailMessageDTO> emailMsgsACT = BusinessOP.emailmsgs;
        log.info(">>Actual--List<EmailMessageDTO>{}", JSON.toJSONString(emailMsgsACT));
        List<EmailMessageDTO> emailMsgsEXP = JSON.parseArray(emailMessageDTO, EmailMessageDTO.class);
        log.info(">>Expect--List<EmailMessageDTO>{}", JSON.toJSONString(emailMsgsEXP));
        CheckUtils.compare(emailMsgsACT, emailMsgsEXP, new String[]{"parameterMap", "code", "content", "verificationCode"});
    }

    @假如("verifySmsCode-收到用户的请求{}结果为{}")
    public void verifysmscode收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject1 = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject1.getString("saasId");
            String mobile = jsonObject1.getString("mobile");
            String verifyCode = jsonObject1.getString("verifyCode");
            BusinessType businessType = jsonObject1.getObject("businessType", BusinessType.class);
            SmsType smsType = jsonObject1.getObject("smsType", SmsType.class);
            CustomerCommonResponse emailCode = remoteVerifyService.verifySmsCode(saasId, mobile, verifyCode, businessType, smsType);
            log.info("SMS Code: {}" + emailCode);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), emailCode.getCode());
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(emailCode.getMessageEnum()));
            Assert.assertEquals(jsonObject.getBoolean("success"), emailCode.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }

    }

    @假如("verifySmsCode-设置redis值为{}")
    public void verifysmscode设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:PHONE:+***********", val);
    }

    @假如("verifyEmailCode-设置redis值为{}")
    public void verifyemailcode设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:EMAIL:<EMAIL>", val);
    }


    @假如("verifyEmailCode-收到用户的请求{}结果为{}")
    public void verifyemailcode收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject1 = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject1.getString("saasId");
            String email = jsonObject1.getString("email");
            String verifyCode = jsonObject1.getString("verifyCode");
            BusinessType businessType = jsonObject1.getObject("businessType", BusinessType.class);
            CustomerCommonResponse emailCode = remoteVerifyService.verifyEmailCode(saasId, email, verifyCode, businessType);
            log.info("emailCode: {}" + emailCode);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), emailCode.getCode());
            Assert.assertEquals(jsonObject.getString("key"), emailCode.getMessageEnum());
            Assert.assertEquals(jsonObject.getBoolean("success"), emailCode.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("verifyGoogleCode-设置redis值为{}")
    public void verifygooglecode设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:EMAIL:<EMAIL>", val);
    }

    @假如("verifyGoogleCode-收到用户的请求{}结果为{}")
    public void verifygooglecode收到用户的请求Request结果为Res(String request, String res) {
        try {

            VerifyGoogleRequest verifyGoogleRequest = JSON.parseObject(request, VerifyGoogleRequest.class);
            CustomerCommonResponse googleCode = remoteVerifyService.verifyGoogleCode(verifyGoogleRequest);
            log.info("googleCode: {}" + googleCode);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), String.valueOf(googleCode.getCode()));
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(googleCode.getMessageEnum()));
            Assert.assertEquals(jsonObject.getBoolean("success"), googleCode.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("verifyFullCode-设置redis值为{}")
    public void verifyfullcode设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VC:EMAIL:<EMAIL>", val);
        redisTemplate.opsForValue().set("CUSTOMER:VC:PHONE:+***********", val);
    }


    @假如("verifyFullCode-收到用户的请求{}结果为{}")
    public void verifyfullcode收到用户的请求Request结果为Res(String request, String res) {
        try {

            VerifyFullCodeRequest verifyFullCodeRequest = JSON.parseObject(request, VerifyFullCodeRequest.class);
            CustomerCommonResponse fullCode = remoteVerifyService.verifyFullCode(verifyFullCodeRequest);
            log.info("fullCode: {}" + fullCode);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), fullCode.getCode());
            Assert.assertEquals(jsonObject.getString("key"), fullCode.getMessageEnum());
            Assert.assertEquals(jsonObject.getBoolean("success"), fullCode.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("verifyFullCode-收到用户的请求{}传参一项结果{}")
    public void verifyfullcode收到用户的请求Request传参一项结果Res(String request, String res) {
        try {
            VerifyFullCodeRequest verifyFullCodeRequest = JSON.parseObject(request, VerifyFullCodeRequest.class);
            CustomerCommonResponse fullCode = remoteVerifyService.verifyFullCode(verifyFullCodeRequest);
            log.info("fullCode: {}" + fullCode);
            if (res != "") {
                JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
                Assert.assertEquals(jsonObject.getString("code"), fullCode.getCode());
                Assert.assertEquals(jsonObject.getString("key"), String.valueOf(fullCode.getMessageEnum()));
                Assert.assertEquals(jsonObject.getBoolean("success"), fullCode.isSuccess());
            }
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("switchVerify-收到用户的请求{}结果为{}")
    public void switchverify收到用户的请求Request结果为Res(String request, String res) {
        try {

            SwitchVerifyRequest switchVerifyRequest = JSON.parseObject(request, SwitchVerifyRequest.class);
            CustomerCommonResponse switchVerify = remoteVerifyService.switchVerify(switchVerifyRequest);
            log.info("switchVerify: {}" + switchVerify);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), switchVerify.getCode());
            Assert.assertEquals(jsonObject.getString("key"), String.valueOf(switchVerify.getMessageEnum()));
            Assert.assertEquals(jsonObject.getBoolean("success"), switchVerify.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("switchVerify-设置redis值为{}")
    public void switchverify设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:-1:131321312", val);
    }

    @假如("tokenVerify-收到用户的请求{}结果为{}")
    public void tokenverify收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject1 = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject1.getString("saasId");
            String verifyToken = jsonObject1.getString("verifyToken");
            String customerId = jsonObject1.getString("customerId");
            BusinessType businessType = jsonObject1.getObject("businessType", BusinessType.class);
            boolean b = remoteVerifyService.tokenVerify(saasId, verifyToken, customerId, businessType);
            Assert.assertEquals(res, String.valueOf(b));
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("tokenVerify-设置redis值为{}")
    public void tokenverify设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:28:121323", val);
    }

    @假如("setVerifyToken-设置redis值为{}")
    public void setverifytoken设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:VERIFY:TOKEN:28:121323", val);
    }


    @假如("setVerifyToken-收到用户的请求{}结果为{}")
    public void setverifytoken收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject1 = JSON.parseObject(request, JSONObject.class);
            String account = jsonObject1.getString("account");
            BusinessType businessType = jsonObject1.getObject("businessType", BusinessType.class);
            String s = remoteVerifyService.setVerifyToken(businessType, account);
            Assert.assertNotNull(s);

        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("assetPwdVerify-收到用户的请求{}结果为{}")
    public void assetpwdverify收到用户的请求Request结果为Res(String request, String res) {
        try {
            JSONObject jsonObject1 = JSON.parseObject(request, JSONObject.class);
            String saasId = jsonObject1.getString("saasId");
            String assetPwd = jsonObject1.getString("assetPwd");
            String customerId = jsonObject1.getString("customerId");
            CustomerCommonResponse customerCommonResponse = remoteVerifyService.assetPwdVerify(saasId, customerId, assetPwd);
            log.info("customerCommonResponse: {}" + customerCommonResponse);
            JSONObject jsonObject = JSON.parseObject(res, JSONObject.class);
            Assert.assertEquals(jsonObject.getString("code"), customerCommonResponse.getCode());
            Assert.assertEquals(jsonObject.getString("key"), customerCommonResponse.getMessageEnum());
            Assert.assertEquals(jsonObject.getBoolean("success"), customerCommonResponse.isSuccess());
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("assetPwdVerify-设置redis值为{}")
    public void assetpwdverify设置redis值为Val(String val) {
        redisTemplate.opsForValue().set("CUSTOMER:EC:AP:20230928165459851765140000", val);
    }

    @并且("inviterByCustomerId-时存在customerInviteDOMap{}")
    public void inviterbycustomerid时存在customerInviteDOMapCustomerInviteDO(String customerInviteDO) {
        List<CustomerInviteDO> customerDOS = JSON.parseArray(customerInviteDO, CustomerInviteDO.class);
        customerDOS.forEach(v -> BusinessOP.customerInviteDOMap.put(v.getInviteeId(), v));
    }

    @当("执行bind_address{}")
    public void bindAddress(String addressBindRequest) {
        List<AddressBindRequest> requests = JSON.parseArray(addressBindRequest, AddressBindRequest.class);
        requests.forEach(r -> {
            try {
                boolean success = remoteCustomerService.bindAddress(r);
                bindAddressRes.add(success);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @同时("Customer-已存在customer_auth_address{}")
    public void exist_customer_auth_address(String existCustomerAuthAddressDOS) {
        List<CustomerAuthAddressDO> customerAuthAddressDOS = JSON.parseArray(existCustomerAuthAddressDOS, CustomerAuthAddressDO.class);
        BusinessOP.customerAuthAddressDOList.addAll(customerAuthAddressDOS);
        log.info("已存在List<CustomerAuthAddressDO>:{}", BusinessOP.customerAuthAddressDOList);
    }

    @当("Customer-用户绑定Wallet{}")
    public void bindWallet(String authBindRequest) {
        List<AuthBindRequest> requests = JSON.parseArray(authBindRequest, AuthBindRequest.class);
        requests.forEach(r -> {
            try {
                remoteCustomerService.bindAuth(r);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @当("Customer-用户Wallet登录{}")
    public void walletLogin(String authLoginRequest) {
        List<AuthLoginRequest> requests = JSON.parseArray(authLoginRequest, AuthLoginRequest.class);
        requests.forEach(r -> {
            try {
                CustomerDTO customerDTO = remoteCustomerService.loginAuth(r);
                customerDTOMap.put(customerDTO.getId(), customerDTO);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @当("Customer-用户bind第三方{}")
    public void bindOauth(String addressBindRequest) {
        List<AddressBindRequest> requests = JSON.parseArray(addressBindRequest, AddressBindRequest.class);
        requests.forEach(r -> {
            try {
                remoteCustomerService.bindAddress(r);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @那么("Customer-检查生成customer{}")
    public void checkCustomer(String customerDO) {
        HashMap<String, CustomerDO> customerDOMap = BusinessOP.customerDOMap;
        List<CustomerDO> CustomerDOSAct = customerDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<CustomerDO>{}", JSON.toJSONString(CustomerDOSAct));

        List<CustomerDO> CustomerDOSExp = JSON.parseArray(customerDO, CustomerDO.class);
        log.info(">>Expect--List<CustomerDO>{}", JSON.toJSONString(CustomerDOSExp));
        CheckUtils.compare(CustomerDOSAct, CustomerDOSExp, new String[]{"id", "openId", "updateTime", "locale", "region"});
    }

    @那么("Customer-检查customer_auth_address{}")
    public void checkOTS_customer_auth_address(String customerAuthAddressDOS) {
        List<CustomerAuthAddressDO> customerAuthAddressDOEXP = JSON.parseArray(customerAuthAddressDOS, CustomerAuthAddressDO.class);
        log.info(">>Expect--List<CustomerAuthAddressDO>{}", JSON.toJSONString(customerAuthAddressDOEXP));

        log.info(">>Actual--List<CustomerAuthAddressDO>{}", JSON.toJSONString(BusinessOP.customerAuthAddressDOList));

        CheckUtils.compare(BusinessOP.customerAuthAddressDOList, customerAuthAddressDOEXP, new String[]{"bindTime", "modified", "customerId"});
    }

    @那么("Customer-检查walletLogin响应{}")
    public void checkWalletLoginResponse(String customerDTOS) {
        List<CustomerDTO> customerDTOSEXP = JSON.parseArray(customerDTOS, CustomerDTO.class);
        log.info(">>Expect--List<CustomerDTO>{}", JSON.toJSONString(customerDTOSEXP));
        List<CustomerDTO> customerDTOSACT = new ArrayList<>(customerDTOMap.values());
        log.info(">>Actual--List<CustomerDTO>{}", JSON.toJSONString(customerDTOSACT));
        CheckUtils.compare(customerDTOSACT, customerDTOSEXP, new String[]{"openId", "locale", "region"});
    }

    @假如("Customer-收到第三方平台用户的注册请求{}")
    public void customer收到第三方平台用户的注册请求CustomerRegisterRequest(String customerDTO) {
        List<CustomerDTO> customerDTOList = JSON.parseArray(customerDTO, CustomerDTO.class);
        customerDTOList.forEach(c -> {
            try {
                remoteCustomerBindService.register(c);
            } catch (Exception e) {
                BusinessOP.exceptions.add(e);
                log.error(">>异常捕获{}", e);
            }
        });
    }

    @并且("Customer-校验产生的customerBindDO{}")
    public void customer校验产生的customerBindDOCustomerBindDO(String customerDTO) {
        HashMap<String, CustomerBindDO> customerBindDOMap = BusinessOP.customerBindDOMap;
        List<CustomerBindDO> customerBindDOSACT = customerBindDOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual--List<CustomerBindDO>{}", JSON.toJSONString(customerBindDOSACT));
        List<CustomerBindDO> customerBindDOSEXP = JSON.parseArray(customerDTO, CustomerBindDO.class);
        log.info(">>Expect--List<CustomerBindDO>{}", JSON.toJSONString(customerBindDOSEXP));
        CheckUtils.compare(customerBindDOSACT, customerBindDOSEXP,new String[]{"uid"});
    }

    @假如("Customer-customerBindBuilder.insert报错")
    public void customerCustomerBindBuilderInsert报错() {
        customerBindBuilderMock.insertThrowException();
    }

    @假如("Customer-已存在customerBindDO{}")
    public void customer已存在customerBindDOCustomerBindDO(String customerBindDO) {
        List<CustomerBindDO> customerBindDOS = JSON.parseArray(customerBindDO, CustomerBindDO.class);
        customerBindDOS.forEach(c -> BusinessOP.customerBindDOMap.put(c.getCid(), c));
    }

    @假如("Customer-收到第三方平台用户的bindSocial请求{}")
    public void customer收到第三方平台用户的bindSocial请求BindSocial(String bindSocial) throws CustomerException {
        JSONObject jsonObject = JSON.parseObject(bindSocial);
        String saasId = jsonObject.getString("saasId");
        String uid = jsonObject.getString("uid");
        String app = jsonObject.getString("app");
        String socialId = jsonObject.getString("socialId");
        String socialPlatform = jsonObject.getString("socialPlatform");
        Long twitterCreateTime = jsonObject.getLong("twitterCreateTime");
        try {
            remoteCustomerBindService.bindSocial(saasId, uid, socialPlatform, socialId, app, twitterCreateTime);
        } catch (Exception e) {
            BusinessOP.exceptions.add(e);
            log.error(">>异常捕获{}", e);
        }
    }

    @假如("Customer-customerBindService.update报错")
    public void customerCustomerBindServiceUpdate报错() {
        customerBindBuilderMock.updateThrowException();
    }

    @假如("Customer-并发收到用户的注册两个请求{}")
    public void customer并发收到用户的注册两个请求CustomerRegisterRequest(String customerRegisterRequest) {
        List<CustomerRegisterRequest> req = JSON.parseArray(customerRegisterRequest, CustomerRegisterRequest.class);
        int len = req.size();
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch mainLatch = new CountDownLatch(len);
        for (int i = 0; i < len; i++) {
            int finalI = i;
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        endLatch.countDown();
                        endLatch.await();
                        remoteCustomerService.register(req.get(finalI));
                    } catch (Exception e) {
                        BusinessOP.exceptions.add(e);
                        log.error(">>异常捕获{}", e);
                    }
                    mainLatch.countDown();
                }
            }).start();
        }
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            mainLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }
}