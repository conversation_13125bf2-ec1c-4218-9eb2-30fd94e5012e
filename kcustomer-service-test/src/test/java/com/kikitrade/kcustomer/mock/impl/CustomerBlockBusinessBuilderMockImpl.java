package com.kikitrade.kcustomer.mock.impl;

import com.kikitrade.kcustomer.dal.builder.CustomerBlockBusinessBuilder;
import com.kikitrade.kcustomer.dal.model.CustomerBlockBusinessDO;
import com.kikitrade.kcustomer.mock.CustomerBlockBusinessBuilderMock;
import com.kikitrade.kcustomer.op.BusinessOP;
import com.kikitrade.kcustomer.util.BeanUtils;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class CustomerBlockBusinessBuilderMockImpl implements CustomerBlockBusinessBuilderMock {
    @Resource
    CustomerBlockBusinessBuilder customerBlockBusinessBuilder;

    @Override
    public void getById() {
        Mockito.doAnswer(c -> {
            String customerId = c.getArgument(0);
            String business = c.getArgument(1);
            Optional<CustomerBlockBusinessDO> first = BusinessOP.customerBlockBusinessDOMap.values().stream().filter(item ->
                    item.getCustomerId().equals(customerId) && item.getBusiness().equals(business)).findFirst();
            if(first.isPresent()){
                return BeanUtils.toBean(first.get());
            }
            return null;
        }).when(customerBlockBusinessBuilder).getById(Mockito.anyString(), Mockito.anyString());
    }

    @Override
    public void create() {
        Mockito.doAnswer(c -> {
            CustomerBlockBusinessDO customerBlockBusiness = c.getArgument(0);
            BusinessOP.customerBlockBusinessDOMap.put(customerBlockBusiness.getCustomerId(),customerBlockBusiness);
            return true;
        }).when(customerBlockBusinessBuilder).create(Mockito.any(CustomerBlockBusinessDO.class));
    }

    @Override
    public void deleteRow() {
        Mockito.doAnswer(c -> {
            CustomerBlockBusinessDO customerBlockBusinessDO = c.getArgument(0);
            BusinessOP.customerBlockBusinessDOMap.remove(customerBlockBusinessDO.getCustomerId());
            return true;
        }).when(customerBlockBusinessBuilder).deleteRow(Mockito.any(CustomerBlockBusinessDO.class));
    }
}
