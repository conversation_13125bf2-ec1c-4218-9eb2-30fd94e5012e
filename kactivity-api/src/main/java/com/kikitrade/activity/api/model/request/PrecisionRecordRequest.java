package com.kikitrade.activity.api.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/12 11:22
 * @description:
 */
@Data
public class PrecisionRecordRequest implements Serializable {

    private String cid;

    private String resourceId;

    private String resourceAuthorId;

    private String resourceAuthorName;

    private String operateType;

    private String metrics;

    private Integer smartFollowsCount;

    private String saasId;

}
