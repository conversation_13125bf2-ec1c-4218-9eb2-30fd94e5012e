package com.drex.customer.service.business;

import com.drex.customer.dal.tablestore.model.CustomerBind;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:15
 * @description:
 */
public interface CustomerBindService {
    boolean insert(CustomerBind customerBind);

    boolean delete(CustomerBind customerBind);

    CustomerBind findByCustomerId(String customerId, String socialPlatform);

    List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms);

    CustomerBind findBySocialUserIdOrName(String socialUserId, String socialUserName, String socialPlatform);


}
