package com.drex.customer.service.business.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.dal.tablestore.builder.CustomerBuilder;
import com.drex.customer.dal.tablestore.model.Customer;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.api.request.AddWaitListRequest;
import com.drex.customer.dal.tablestore.builder.CustomerWaitListBuilder;
import com.drex.customer.dal.tablestore.model.CustomerWaitList;
import com.drex.customer.service.business.CustomerService;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.mapstruct.CustomerMapperStruct;
import com.drex.customer.service.socialPlatform.constant.SocialConstant;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service("customerService")
public class CustomerServiceImpl implements CustomerService {

    @Autowired
    private CustomerBuilder customerBuilder;
    @Autowired
    private CustomerMapperStruct customerMapperStruct;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private OnsProperties onsProperties;
    @Resource
    private CustomerProperties customerProperties;
    @Resource
    private CustomerBindService customerBindService;
    @Resource
    private CustomerWaitListBuilder customerWaitListBuilder;

    @Override
    public CustomerDTO getById(String customerId) {
        return customerMapperStruct.toCustomerDTO(customerBuilder.getById(customerId));
    }

    @Override
    public CustomerDTO getByRegisterAddress(String registerAddress) {
        return customerMapperStruct.toCustomerDTO(customerBuilder.getByRegisterAddress(registerAddress));
    }

    @Override
    public CustomerDTO getByInviteCode(String inviteCode) {
        return customerMapperStruct.toCustomerDTO(customerBuilder.getByInviteCode(inviteCode));
    }

    public Boolean updateLevel(String customerId, String kycLevel) {
        return customerBuilder.updateLevel(customerId, kycLevel);
    }

    @Override
    public CustomerDTO register(CustomerDTO customerDTO) {
        Customer customer = customerMapperStruct.toCustomer(customerDTO);
        if(customerBuilder.insert(customer)){
            //发送customer mq消息
            Map<String, String> body = new HashMap<>();
            body.put("event", "register");
            body.put("data", JSON.toJSONString(customer));
            onsProducer.send(customerProperties.getCustomerRegisterTopic() + onsProperties.getEnv(), JSON.toJSONString(body));
            //绑定社媒
            if(SocialConstant.PlatformEnum.getEnumByCode(customerDTO.getAuthProvider()) != null){
                CustomerBind customerBind = new CustomerBind();
                customerBind.setCustomerId(customer.getCustomerId());
                customerBind.setSocialPlatform(SocialConstant.PlatformEnum.getEnumByCode(customerDTO.getAuthProvider()).name());
                customerBind.setSocialHandleName(customerDTO.getUsername());
                customerBindService.insert(customerBind);
            }
            return customerMapperStruct.toCustomerDTO(customer);
        }
        return null;
    }

    @Override
    public boolean addWaitList(AddWaitListRequest addWaitListRequest) {
        CustomerWaitList customerWaitList = customerMapperStruct.toCustomerWaitList(addWaitListRequest);
        return customerWaitListBuilder.insertOrUpdate(customerWaitList);
    }
}
