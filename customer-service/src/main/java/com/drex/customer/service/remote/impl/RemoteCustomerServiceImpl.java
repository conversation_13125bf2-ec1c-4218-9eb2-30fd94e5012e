package com.drex.customer.service.remote.impl;

import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.request.CustomerReferralDTO;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.service.business.CustomerService;
import com.drex.customer.service.business.InviteService;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;


@DubboService
public class RemoteCustomerServiceImpl implements RemoteCustomerService {

    @Resource
    private CustomerService customerService;
    @Resource
    private InviteService inviteService;

    @Override
    public Response<CustomerDTO> getById(String customerId) {
        return Response.success(customerService.getById(customerId));
    }

    @Override
    public Response bindInviteCode(String customerId, String inviteCode) {
        CustomerReferralDTO customerReferralDTO = new CustomerReferralDTO();
        customerReferralDTO.setInviteCode(inviteCode);
        customerReferralDTO.setCustomerId(customerId);
        return inviteService.bindInviteCode(customerReferralDTO)
                ? Response.success(null)
                : Response.error(ErrorCode.INVITE_CODE_INVALID.getCode(), ErrorCode.INVITE_CODE_INVALID.getMessage());
    }

    @Override
    public Response<Long> countByReferrerId(String customerId) {
        return Response.success(inviteService.countByReferrerId(customerId));
    }

    @Override
    public Response<Boolean> updateLevel(String customerId, String kycLevel) {
        Boolean res = customerService.updateLevel(customerId, kycLevel);
        return Response.success(res);
    }
}
