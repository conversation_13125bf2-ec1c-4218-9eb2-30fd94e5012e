package com.drex.customer.service.remote.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.drex.customer.api.RemoteAuthService;
import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.ThirdAuthRequest;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.response.ThirdAuthDTO;
import com.drex.customer.api.response.ThirdBindingsDTO;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.AuthService;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.CustomerService;
import com.drex.customer.service.business.InviteService;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.service.mapstruct.AuthMapperStruct;
import com.drex.customer.service.socialPlatform.model.Token;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.customer.service.socialPlatform.service.SocialPlatformServiceFactory;
import com.drex.model.CustomerException;
import com.drex.model.auth.EmbeddedWalletInfo;
import com.drex.model.auth.Web3Account;
import com.drex.model.auth.Web3UserInfo;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

@DubboService
@Slf4j
public class RemoteAuthServiceImpl implements RemoteAuthService {

    @Autowired
    private CustomerService customerService;
    @Resource
    private InviteService inviteService;
    @Resource
    private AuthService authService;
    @Resource
    private AuthMapperStruct authMapperStruct;
    @Autowired
    private CustomerBindService customerBindService;
    @Resource
    private SocialPlatformServiceFactory platformServiceFactory;

    @Override
    public Response<CustomerDTO> login(AuthLoginRequest request) {
        try {
            log.info("login request:{}", request);
            CustomerDTO customerDTO = customerService.getByRegisterAddress(request.getEoaAddress());
            if (customerDTO != null) {
                return Response.success(customerDTO);
            }
            customerDTO = register(request);
            customerDTO.setIsNewUser(true);
            return Response.success(customerDTO);
        } catch (Exception e) {
            log.error("login error", e);
            return Response.error(ErrorCode.UNKNOWN_ERROR.getCode(), ErrorCode.UNKNOWN_ERROR.getMessage());
        }
    }

    @Override
    public Response<ThirdAuthDTO> thirdAuth(ThirdAuthRequest request) {
        try {
            Token token = authService.auth(request.getPlatform(), request.getCode(), request.getCustomerId());
            ThirdAuthDTO thirdAuthResponse = authMapperStruct.toThirdAuthDTO(token);
            return Response.success(thirdAuthResponse);
        } catch (CustomerException e) {
            return Response.error(e.getCode().getCode(), e.getCode().getMessage());
        }
    }

    @Override
    public Response<List<ThirdBindingsDTO>> thirdBindings(String customerId) {
        log.info("[RemoteAuthService] thirdBindings customerId: {}", customerId);
        // 获取支持的社交平台列表
        List<String> socialPlatforms = platformServiceFactory.getSupportedPlatforms();

        // 查询用户已绑定的社交平台
        List<CustomerBind> customerBinds = customerBindService.findByCustomerId(customerId, socialPlatforms);

        // 创建结果列表
        List<ThirdBindingsDTO> result = new ArrayList<>();

        // 处理每个社交平台
        for (String platform : socialPlatforms) {
            ThirdBindingsDTO bindingsDTO = new ThirdBindingsDTO();
            bindingsDTO.setPlatform(platform);

            // 查找当前平台是否已绑定
            CustomerBind existingBind = customerBinds.stream()
                    .filter(bind -> platform.equals(bind.getSocialPlatform()))
                    .findFirst()
                    .orElse(null);

            if (existingBind != null) {
                // 已绑定
                bindingsDTO.setStatus("1");
                bindingsDTO.setConnectUrl(null);
                bindingsDTO.setSocialHandleName(existingBind.getSocialHandleName());
                bindingsDTO.setSocialProfileImage(existingBind.getSocialProfileImage());
            } else {
                // 未绑定
                bindingsDTO.setStatus("0");

                // 使用对应平台的服务生成授权URL
                SocialPlatformService platformService = platformServiceFactory.getService(platform);
                if (platformService != null) {
                    bindingsDTO.setConnectUrl(platformService.generateAuthUrl());
                } else {
                    log.warn("No service found for platform: {}", platform);
                }
            }
            result.add(bindingsDTO);
        }
        return Response.success(result);
    }

    private CustomerDTO register(AuthLoginRequest request) {
        if(StringUtils.isBlank(request.getExtraCrypto())){
            //认为是钱包登录
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setRegisterAddress(request.getEoaAddress());
            customerDTO.setEoaAddress(request.getEoaAddress());
            customerDTO.setAuthProvider("wallet");
            customerDTO.setInviteCode(inviteService.generateInviteCode());
            return customerService.register(customerDTO);
        }else{
            EmbeddedWalletInfo embeddedWalletInfo = JSON.parseObject(request.getExtraCrypto(), EmbeddedWalletInfo.class, JSONReader.Feature.IgnoreNullPropertyValue);
            Web3UserInfo web3UserInfo = embeddedWalletInfo.getUser();
            CustomerDTO customerDTO = new CustomerDTO();
            if(web3UserInfo != null){
                customerDTO.setUsername(web3UserInfo.getUsername() == null ? web3UserInfo.getEmail() : web3UserInfo.getUsername());
                customerDTO.setEmail(web3UserInfo.getEmail());
                customerDTO.setChainId(web3UserInfo.getChainId());
                if(web3UserInfo.getAccounts() != null){
                    //设置smartAccount地址
                    Optional<Web3Account> web3SmartAccount = embeddedWalletInfo.getUser().getAccounts().stream().filter(account -> "smartAccount".equals(account.getType())).findFirst();
                    web3SmartAccount.ifPresent(account -> customerDTO.setSmartAccountAddress(account.getAddress()));
                    //设置eoa地址
                    Optional<Web3Account> web3EoaAccount = embeddedWalletInfo.getUser().getAccounts().stream().filter(account -> "eoa".equals(account.getType())).findFirst();
                    web3EoaAccount.ifPresent(web3Account -> customerDTO.setEoaAddress(web3Account.getAddress()));

                    customerDTO.setAllAccount(JSON.toJSONString(web3UserInfo.getAccounts()));
                }else{
                    customerDTO.setEoaAddress(request.getEoaAddress());
                }
            }
            customerDTO.setRegisterAddress(request.getEoaAddress());

            //设置登录方式
            customerDTO.setAuthProvider(request.getPlatform());
            customerDTO.setInviteCode(inviteService.generateInviteCode());
            return customerService.register(customerDTO);
        }
    }
}
