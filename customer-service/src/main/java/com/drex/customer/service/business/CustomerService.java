package com.drex.customer.service.business;

import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.request.AddWaitListRequest;

public interface CustomerService {

    /**
     * 根据客户ID获取客户数据传输对象（DTO）。
     *
     * @param customerId 客户的唯一标识符。
     * @return 返回与指定客户ID对应的客户数据传输对象（DTO）。如果找不到对应的客户，则返回null。
     */
    CustomerDTO getById(String customerId);


    /**
     * 根据EOA地址获取客户数据传输对象（DTO）。
     *
     * @param eoaAddress 客户的EOA地址，用于唯一标识客户。
     * @return 返回与给定EOA地址对应的客户数据传输对象（DTO）。
     *         如果找不到对应的客户，则返回null。
     */
    CustomerDTO getByRegisterAddress(String eoaAddress);

    /**
     * 根据邀请码获取客户数据传输对象（DTO）。
     * @param inviteCode
     * @return
     */
    CustomerDTO getByInviteCode(String inviteCode);


    /**
     * 注册新客户。
     *
     * @param customerDTO 包含客户信息的传输对象。
     * @return 注册后的客户信息传输对象。
     */
    CustomerDTO register(CustomerDTO customerDTO);

    Boolean updateLevel(String customerId, String kycLevel);
    boolean addWaitList(AddWaitListRequest addWaitListRequest);
}
