package com.drex.customer.service.socialPlatform.constant;

import lombok.Data;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/4/25 17:43
 * @description:
 */
@Data
public class SocialConstant {

    public enum PlatformEnum {
        X("x"),
        Discord("discord"),
        Google("google"),
        TikTok("tiktok"),
        Instagram("instagram"),
        ;

        private String code;

        PlatformEnum(String code) {
            this.code = code;
        }

        public static PlatformEnum getEnumByCode(String code) {
            return Arrays.stream(PlatformEnum.values()).filter(platformEnum -> platformEnum.code.equals(code)).findFirst().orElse(null);
        }
    }

    public static final String GLOBAL_APPID = "trex";

}
