package com.drex.customer.service.business.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.model.ActivityEventMessage;
import com.drex.model.CacheKey;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:15
 * @description:
 */
@Service
public class CustomerBindServiceImpl implements CustomerBindService {
    @Resource
    private CustomerBindBuilder customerBindBuilder;
    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private OnsProperties onsProperties;
    @Resource
    private CustomerProperties customerProperties;


    private final static String eventCodePrefix = "connect_";

    @Override
    public boolean insert(CustomerBind customerBind) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerBind.getCustomerId());
        redisTemplate.delete(cacheKey);
        if (customerBindBuilder.insert(customerBind)) {
            //发送mq消息异步触发verify任务
            String eventCode = eventCodePrefix + customerBind.getSocialPlatform().toLowerCase();
            Map<String, Object> body = new HashMap<>();
            body.put("appId", "trex");
            ActivityEventMessage msg = ActivityEventMessage.builder()
                    .eventCode(eventCode)
                    .customerId(customerBind.getCustomerId())
                    .eventTime(System.currentTimeMillis())
                    .body(body)
                    .build();
            onsProducer.send(customerProperties.getActivityEventTopic() + onsProperties.getEnv(), JSON.toJSONString(msg));
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean delete(CustomerBind customerBind) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerBind.getCustomerId());
        redisTemplate.delete(cacheKey);
        return customerBindBuilder.delete(customerBind);
    }

    @Override
    public CustomerBind findByCustomerId(String customerId, String socialPlatform) {
        return customerBindBuilder.findByCustomerId(customerId, socialPlatform);
    }

    @Override
    public List<CustomerBind> findByCustomerId(String customerId, List<String> socialPlatforms) {
        String cacheKey = String.format("%s:%s", CacheKey.CUSTOMER_BIND.getKey(), customerId);
        String cache = redisTemplate.opsForValue().get(cacheKey);
        if(StringUtils.isNotBlank(cache)){
            return JSON.parseArray(cache, CustomerBind.class).stream().filter(customerBind -> socialPlatforms.contains(customerBind.getSocialPlatform())).toList();
        }
        List<CustomerBind> customerBinds = customerBindBuilder.findByCustomerId(customerId, socialPlatforms);
        if(!customerBinds.isEmpty()){
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(customerBinds), 3, TimeUnit.DAYS);
        }
        return customerBinds;
    }


    @Override
    public CustomerBind findBySocialUserIdOrName(String socialUserId, String socialUserName, String socialPlatform) {
       return customerBindBuilder.findBySocialUserIdOrName(socialUserId, socialUserName, socialPlatform);
    }
}
