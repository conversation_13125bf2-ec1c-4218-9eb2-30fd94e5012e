syntax = "proto3";
import "google/protobuf/timestamp.proto";
package com.kikitrade.kpay.facade;
option java_package = "com.kikitrade.kpay.facade";
option java_multiple_files = true;

enum AddressType {
  GENERAL = 0;//普通
  MEMO_TEXT = 1; //文本memo
  MEMO_NUM_INCR = 2; //数字memo
  SAME_WITH_MAIN_CHAIN = 3; //和主链一致
  RESET_EVERY_TIME = 4; //每次生成新的
}

enum AssetStatus {
  DISABLE = 0;
  ENABLE = 1;
  INNER_TEST = 2;
}

enum WithdrawFeeType {
  FIXED = 0;
  PERCENTAGE = 1;
  MIXED = 2;
}

enum FeeLevel {
  LOW = 0;
  MEDIUM = 1;
  HIGH = 2;
  CUSTOM = 3;
}

message PayAsset {
  string id = 1;
  string currency = 2;
  string chainName = 3;
  string chainType = 4;
  AddressType addressType = 5;
  string memoLabel = 6;
  int32 precision = 7;
  int32 showPrecision = 8;
  int32 depositNetwork = 9;
  int32 availableNetwork = 10;
  string feeAssetId = 11;
  string minMergeAmount = 12;
  string remainMergeAmount = 13;
  string networkFee = 14;
  string unitFee = 15;
  string gasPrice = 16;
  string gasLimit = 17;
  FeeLevel feeLevel = 18;
  double speedupRate = 19;
  bool enableWithdraw = 20;
  bool enableDeposit = 21;
  string minDepositAmount = 22;
  string calculateCurrency = 23;
  string onceMinWithdrawAmount = 24;
  string onceMaxWithdrawAmount = 25;
  string dailyMaxWithdrawAmount = 26;
  string monthlyMaxWithdrawAmount = 27;
  string unauthorizedOnceMinWithdrawAmount = 28;
  string unauthorizedOnceMaxWithdrawAmount = 29;
  string unauthorizedDailyMaxWithdrawAmount = 30;
  WithdrawFeeType withdrawFeeType = 31;
  string withdrawFeeAmount = 32;
  double withdrawFeeRate = 33;
  bool enableAutoMerge = 34;
  string autoMergeThreshold = 35;
  bool enableAutoWithdraw = 36;
  string autoWithdrawThreshold = 37;
  int64 autoWithdrawDelayTime = 38;
  AssetStatus status = 39;
  string saasId = 40;
  google.protobuf.Timestamp created = 41;
  google.protobuf.Timestamp modified = 42;
  string icon = 43;
  string url = 44;
  string kyc2OnceMinWithdrawAmount = 45;
  string kyc2OnceMaxWithdrawAmount = 46;
  string kyc2DailyMaxWithdrawAmount = 47;
  string kyc2MonthlyMaxWithdrawAmount = 48;
}


message PayAssetResponse{
  bool success = 1;
  string message = 2;
  PayAsset asset = 3;
}

message AssetLimitRequest{
  string asset = 1;
  repeated AssetLimit assetLimit = 2;
  string quotaCurrency = 3;
}
message AssetLimit{
  string level = 1;
  string singleLimit = 2;
  string dailyLimit = 3;
}
message CommonResponse{
  bool success = 1;
  string message = 2;
}

message ChainTypeResponse{
  repeated string chainType = 1 ;
}

message empty {
}

service PayAssetFacade {
  rpc upsertPayAsset(PayAsset) returns (PayAssetResponse);

  rpc upsertAssetLimit(AssetLimitRequest) returns (CommonResponse);

  rpc chainTypeList(empty)  returns (ChainTypeResponse);

}
