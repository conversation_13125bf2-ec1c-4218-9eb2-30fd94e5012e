syntax = "proto3";
import "google/protobuf/timestamp.proto";
package com.kikitrade.kpay.facade;
option java_package = "com.kikitrade.kpay.facade";
option java_multiple_files = true;

enum Type {
  UNKNOWN = 0;//未知
  DEPOSIT = 1; //1充币
  WITHDRAW = 2; //2提币
  DEPOSIT_HIDDEN = 3; //3充币太小，隐藏不入账
}

enum Category {
  NORMAL = 0;//普通
  INNER = 1; //内部转账
}

enum Status {
  ALL = 0;
  WITHDRAW_APPLY = 1; //(1, "customer apply"),
  WITHDRAW_APPROVED = 2; //(2, "user approve"),
  WITHDRAW_REJECTED = 3; //(3, "user reject"),
  WITHDRAW_COMPLETED = 4; //(4, "completed"),
  WITHDRAW_FAILED = 5; //(5, "failed"),
  WITHDRAW_NOT_IN_CHAIN = 6; //(6, "not in chain"),//未上链
  WITHDRAW_CANCELED = 7;//(7, "customer cancel")

  // Deposit status start with 10
  DEPOSIT_INITIAL = 8; //(10, "initial"),
  DEPOSIT_APPLY = 9; //(11, "unconfirmed"),
  DEPOSIT_FINISHED = 10; //(12, "finished"),
  DEPOSIT_FAILED = 11; //(13, "failed"),;
}

enum TransactionStatus {
  CREATED = 0;
  QUEUING = 1;
  SENT = 2;
  MINED = 3;
  CONFIRMED = 4;
  FAILED = 5;
  EXPIRED = 6;
  CANCEL = 7;
  AVAILABLE = 8;
}

enum OperateType {
  APPROVAL = 0;
  REJECT = 1;
}

message DmTransaction {
  string saasId = 1;
  //订单号
  string id = 2;
  // 业务ID
  string businessId = 3;
  string customerId = 4;
  string channelId = 5;
  Type type = 6;
  string amount = 7;
  string fee = 8;
  Status status = 9;
  string currency = 10;
  string address = 11;
  string memo = 12;
  Category category = 13;
  string txId = 14;
  //链上状态
  TransactionStatus transactionStatus = 15;
  string usdtPrice = 16;
  string remark = 17;
  string rejectReason = 18;
  string operatorId = 19;
  string transactionFee = 20;
  google.protobuf.Timestamp created = 21;
  google.protobuf.Timestamp modified = 22;
}

message QueryDmTransactionRequest{
  string saasId = 1;
  string id = 2;
  string customerId = 3;
  string currency = 4;
  google.protobuf.Timestamp startTime = 5;
  google.protobuf.Timestamp endTime = 6;
  Status status = 7;
  Type type = 8;
  int32 offset = 9;
  int32 limit = 10;
  string approvalType=11;
}

message DmTransactionResponse{
  bool success = 1;
  string message = 2;
  repeated DmTransaction dmTransactions = 3;
  int32 total = 4;
}

message ProcessWithdrawRequest{
  string saasId = 1;
  string dmTransactionId = 2;
  OperateType operateType = 3;
  string operatorId = 4;
}

message CryptoTransactionRequest{
  string saasId = 1;
  string txId = 2;
  string currency = 3;
}

message AppendTransactionRequest{
  string saasId = 1;
  string txId = 2;
  string currency = 3;
  string address = 4;
  string memo = 5;
  //扣除的手续费
  string fee = 6;
  //入账金额
  string amount = 7;
  //需要先归集
  bool afterMerge = 8;
}


service DmTransactionFacade {
  rpc queryDmTransaction(QueryDmTransactionRequest) returns (DmTransactionResponse);
  rpc processWithdraw(ProcessWithdrawRequest) returns (DmTransactionResponse);
  rpc queryByTxHash(CryptoTransactionRequest) returns (DmTransactionResponse);
  rpc appendDmTransaction(AppendTransactionRequest) returns (DmTransactionResponse);
}
