<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kikitrade.member.dal.mysql.dao.EquityTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.member.dal.mysql.model.EquityDO">
        <id column="equity_id" jdbcType="SMALLINT" property="equityId"/>
        <result column="scope" jdbcType="VARCHAR" property="scope"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="hyperlink" jdbcType="VARCHAR" property="hyperlink"/>
        <result column="application" jdbcType="VARCHAR" property="application"/>
        <result column="behavior" jdbcType="VARCHAR" property="behavior"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="params" jdbcType="VARCHAR" property="params"/>
        <result column="member_visible" jdbcType="BOOLEAN" property="memberVisible"/>
        <result column="props_visible" jdbcType="BOOLEAN" property="propsVisible"/>
        <result column="weight" jdbcType="BIGINT" property="weight"/>
        <result column="created" jdbcType="TIMESTAMP" property="created"/>
        <result column="modified" jdbcType="TIMESTAMP" property="modified"/>
        <result column="saas_id" jdbcType="VARCHAR" property="saasId"/>
        <result column="name_i18n" jdbcType="VARCHAR" property="namei18n"/>
        <result column="remark_i18n" jdbcType="VARCHAR" property="remarki18n"/>
    </resultMap>

    <update id="createTableIfNotExists">
        CREATE TABLE IF NOT EXISTS equity (
            `equity_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权益ID',
            `name` varchar(100) NOT NULL COMMENT '权益名称',
            `level` varchar(20) DEFAULT NULL COMMENT 'vip等级',
            `remark` varchar(2000) DEFAULT NULL COMMENT '权益描述',
            `status` varchar(10) NOT NULL COMMENT '状态',
            `icon` varchar(1000) DEFAULT NULL COMMENT '图标',
            `hyperlink` varchar(1000) DEFAULT NULL COMMENT '超链',
            `application` varchar(50) NOT NULL COMMENT '权益所属模块',
            `behavior` varchar(100) NOT NULL COMMENT '行为',
            `type` varchar(100) NOT NULL COMMENT '权益类型',
            `params` varchar(1000) DEFAULT NULL COMMENT '权益参数',
            `member_visible` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'VIP可见',
            `props_visible` tinyint(1) NOT NULL DEFAULT '0' COMMENT '道具可见',
            `weight` bigint(20) NOT NULL COMMENT '排序',
            `created` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
            `saas_id` varchar(10) DEFAULT NULL COMMENT 'saasId',
            `scope` varchar(255) NOT NULL COMMENT '权益范围',
            `namespace` varchar(255) NOT NULL DEFAULT '0' comment '权益归属的明星，平台权益为0',
            `name_i18n` varchar(100) NOT NULL DEFAULT '' COMMENT '权益名称国际化',
            `remark_i18n` varchar(100) NOT NULL DEFAULT '' COMMENT '权益描述国际化',
            PRIMARY KEY (`equity_id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='权益表'
        ;
    </update>

    <insert id="init">
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (1,'{"hk":"加密貨幣提幣限額150%","en":"cryptocurrency withdrawal","zh":"加密货币提币限额150%"}',null,'{"hk":"LV1提币限额150%","en":"LV1 increased by 150%","zh":"LV1提币限额150%"}','Active','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220908164510244927__w:166__h:166.png','','kpay','withdraw','WITHDRAW_QUOTA_PROMOTION','150',1,0,1,'2022-09-08 09:06:52','2022-09-08 09:06:52','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (2,'{"hk":"更優惠槓桿借貸利率30%","en":"More favorable leveraged loan interest rate of 30%","zh":"更优惠杠杆借贷利率30%"}',null,'{"hk":"更優惠槓桿借貸利率30%","en":"More favorable leveraged loan interest rate of 30%","zh":"更优惠杠杆借贷利率30%"}','Active','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220908164527736931__w:166__h:166.png','','ktrade','margin_interest','MARGIN_INTEREST_DISCOUNT','30',1,0,2,'2022-09-08 08:45:27','2022-09-08 08:45:27','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (3,'{"hk":"VIP1身份勳章","en":"VIP1 Status Medal","zh":"VIP1身份勋章"}',null,'{"hk":"VIP1身份勳章","en":"VIP1 Status Medal","zh":"VIP1身份勋章"}','Disable','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220829162742474889__w:182__h:88.png','','none','none','UN_QUANTIFIED','',1,0,3,'2022-09-08 08:50:41','2022-09-08 08:50:41','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (4,'{"hk":"VIP2身份勳章","en":"VIP2 Status Medal","zh":"VIP2身份勋章"}',null,'{"hk":"VIP2身份勳章","en":"VIP2 Status Medal","zh":"VIP2身份勋章"}','Disable','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220829162926119599.png','','none','none','UN_QUANTIFIED','',1,0,4,'2022-09-08 08:50:07','2022-09-08 08:50:07','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (5,'{"hk":"加密貨幣提幣限額300%","en":"cryptocurrency withdrawal 300%","zh":"加密货币提币限额300%"}',null,'{"hk":"加密貨幣提幣限額300%","en":"cryptocurrency withdrawal 300%","zh":"加密货币提币限额300%"}','Active','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220908164621050914__w:166__h:166.png','','kpay','withdraw','WITHDRAW_QUOTA_PROMOTION','300',1,0,5,'2022-09-08 09:07:11','2022-09-08 09:07:11','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (6,'{"hk":"更優惠槓桿借貸利率50%","en":"More favorable leveraged loan interest rate of 50%","zh":"更优惠杠杆借贷利率50%"}',null,'{"hk":"更優惠槓桿借貸利率50%","en":"More favorable leveraged loan interest rate of 50%","zh":"更优惠杠杆借贷利率50%"}','Active','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220908164639678032.png','','ktrade','margin_interest','MARGIN_INTEREST_DISCOUNT','50',1,0,6,'2022-09-08 08:46:39','2022-09-08 08:46:39','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (7,'{"hk":"生日禮","en":" birthday present","zh":"生日礼"}',null,'{"hk":"生日禮","en":" birthday present","zh":"生日礼"}','Active','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220908164655197831__w:166__h:166.png','www.kikitrade.com','none','none','UN_QUANTIFIED','',1,0,7,'2022-09-08 08:46:56','2022-09-08 08:46:56','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (8,'{"hk":"獨家投研報告","en":"Exclusive investment research report","zh":"独家投研报告"}',null,'{"hk":"獨家投研報告","en":"Exclusive investment research report","zh":"独家投研报告"}','Active','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220908164716032295.png','www.kikitrade.com','none','none','UN_QUANTIFIED','',1,0,8,'2022-09-19 12:07:02','2022-09-19 12:07:02','kiki','platform');
        INSERT INTO `equity` (`equity_id`,`name`,`level`,`remark`,`status`,`icon`,`hyperlink`,`application`,`behavior`,`type`,`params`,`member_visible`,`props_visible`,`weight`,`created`,`modified`,`saas_id`,`scope`) VALUES (9,'{"hk":"加密貨幣提幣限額200%","en":"200% cryptocurrency withdrawal","zh":"加密货币提币限额200%"}',null,'{"hk":"加密貨幣提幣限額200%","en":"200% cryptocurrency withdrawal","zh":"加密货币提币限额200%"}','Disable','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/Benefits/20220908164732440243__w:166__h:166.png','','kpay','withdraw','WITHDRAW_QUOTA_PROMOTION','200',1,0,20,'2022-09-08 09:04:36','2022-09-08 09:04:36','kiki','platform');
    </insert>

</mapper>