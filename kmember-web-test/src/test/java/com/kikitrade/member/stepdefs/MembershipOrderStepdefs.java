package com.kikitrade.member.stepdefs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.asset.dal.mysql.operate.model.AssetDO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.api.RemoteMembershipOrderService;
import com.kikitrade.member.dal.mysql.dao.*;
import com.kikitrade.member.dal.tablestore.model.MembershipDO;
import com.kikitrade.member.dal.tablestore.model.MembershipOrderDO;
import com.kikitrade.member.mock.MembershipBuilderIndexImplMock;
import com.kikitrade.member.mock.MembershipOrderBuilderImplMock;
import com.kikitrade.member.mock.OnsProducerMock;
import com.kikitrade.member.mock.RemoteCustomerServiceDubboMock;
import com.kikitrade.member.model.MemberEventNotifyMsg;
import com.kikitrade.member.model.exception.MemberException;
import com.kikitrade.member.model.request.MembershipPurchaseRequest;
import com.kikitrade.member.model.request.ToogleMembershipOption;
import com.kikitrade.member.model.response.MembershipPurchaseResponse;
import com.kikitrade.member.model.response.ToogleMembershipOptionResponse;
import com.kikitrade.member.op.BusinessOP;
import com.kikitrade.member.op.CheckAll;
import com.kikitrade.member.service.membership.MembershipOrderService;
import com.kikitrade.member.service.membership.MembershipService;
import com.kikitrade.member.service.membership.listener.MemberNotifyListener;
import com.kikitrade.member.service.membership.tcc.impl.MembershipOrderPurchaseTccServiceImpl;
import com.kikitrade.member.util.CheckUtils;
import io.cucumber.java.zh_cn.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import static org.junit.jupiter.api.Assertions.*;

import jakarta.annotation.Resource;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;


@Slf4j
public class MembershipOrderStepdefs {

    @Resource
    private BusinessOP businessOP;
    @Resource
    private MembershipService membershipService;
    @Resource
    private MembershipOrderService membershipOrderService;
    @Resource
    private RemoteMembershipOrderService remoteMembershipOrderService;
    @Resource
    private AssetTestDao assetTestDao;
    @Resource
    private CheckAll checkAll;
    @Resource
    MembershipOrderBuilderImplMock membershipOrderBuilderMock;
    @Resource
    RemoteCustomerServiceDubboMock remoteCustomerServiceDubboMock;
    @Resource
    MembershipBuilderIndexImplMock membershipBuilderIndexImplMock;
    @Resource
    private MembershipOrderPurchaseTccServiceImpl membershipOrderPurchaseTccService;
    @Resource
    OnsProducerMock onsProducerMock;
    @Resource
    RemoteMemberService remoteMemberService;
    @Resource
    MemberNotifyListener memberNotifyListener;

    public static String customerId;

    @假如("初始化用户积分账户为{}")
    public void 初始化用户积分账户为(String assetDO) {

        List<AssetDO> assetDOS = JSON.parseArray(assetDO, AssetDO.class);
        assetDOS.stream().forEach(c -> {
            assetTestDao.insert(c);
        });
    }

    @同时("初始化用户{}的vip信息为{}")
    public void 初始化用户的vip信息为(String customer, String membershipDO) {
        List<MembershipDO> membershipDOS = JSON.parseArray(membershipDO, MembershipDO.class);
        businessOP.addMembershipDO(membershipDOS);
    }

    @当("membership-order-用户购买会员{}操作结果为{}")
    public void membershipOrder用户购买会员MembershipPurchase(String membershipPurchase, String purchaseResult) throws MemberException {

        try {
            MembershipPurchaseRequest req = JSON.parseObject(membershipPurchase, MembershipPurchaseRequest.class);
            MembershipPurchaseResponse membershipPurchaseResponse = membershipOrderService.purchase(req);
            customerId = req.getCustomerId();
            JSONObject jsonObject = JSON.parseObject(purchaseResult);
            assertEquals(jsonObject.getString("code"), membershipPurchaseResponse.getCode().name());

        } catch (Exception e) {
            String eer = JSON.toJSONString(e);
            log.error("---捕获到错误:{}", eer);
            List<String> exceptionList = BusinessOP.exceptionList;
            exceptionList.add(eer);
        }
    }

    @当("membership-order-用户升级会员{}操作结果为{}")
    public void membershipOrder用户升级会员MembershipUpgrade操作结果为PurchaseResult(String membershipUpgrade, String purchaseResult) {
        try {
            MembershipPurchaseRequest req = JSON.parseObject(membershipUpgrade, MembershipPurchaseRequest.class);
            MembershipPurchaseResponse membershipPurchaseResponse = remoteMembershipOrderService.upgrade(req);
            customerId = req.getCustomerId();
            JSONObject jsonObject = JSON.parseObject(purchaseResult);
            assertEquals(jsonObject.getString("code"), membershipPurchaseResponse.getCode().name());

        } catch (Exception e) {
            String eer = JSON.toJSONString(e);
            log.error("---捕获到错误:{}", eer);
            List<String> exceptionList = BusinessOP.exceptionList;
            exceptionList.add(eer);
        }
    }

    @假如("存在用户{}的vip信息{}即将到期续费")
    public void 存在用户customer的vip信息membershipDO即将到期续费(String customer, String membershipDO) {
        List<MembershipDO> membershipDOS = JSON.parseArray(membershipDO, MembershipDO.class);
        businessOP.addMembershipDO(membershipDOS);
    }

    @同时("初始化用户的customer信息为{}")
    public void 初始化用户的customer信息为CustomerDTO(String customerDTO) {
        List<CustomerDTO> customerDTOS = JSON.parseArray(customerDTO, CustomerDTO.class);
        businessOP.addCustomerDTO(customerDTOS);

    }

    @当("系统执行用户vip续费通知")
    public void 系统执行用户vip续费通知() {
        try {
            membershipService.renewalNotify();
        } catch (Exception e) {
            String exceptions = JSON.toJSONString(e);
            log.error("---捕获到错误:{}", exceptions);
            List<String> exceptionList = BusinessOP.exceptionList;
            exceptionList.add(exceptions);
        }
    }

    @当("系统执行用户vip自动续费")
    public void 系统执行用户vip自动续费() {
        try {
            membershipService.renewal();
        } catch (Exception e) {
            String eer = JSON.toJSONString(e);
            log.error("---捕获到错误:{}", eer);
            List<String> exceptionList = BusinessOP.exceptionList;
            exceptionList.add(eer);
        }
    }

    @并且("{}为成功时更新用户vip信息为{}")
    public void 更新用户vip信息为(String purchaseResult, String updateMembershipDO) {
        checkAll.checkMembership(purchaseResult, updateMembershipDO);
    }

    @并且("用户vip购买订单为{}")
    public void 用户vip购买订单为(String membershipOrderDO) throws Exception {
        checkAll.checkMembershipOrderDO(membershipOrderDO);
    }

    @那么("membership-order-校验asset为{}")
    public void membershipOrder校验asset为Asset(String asset) {
        checkAll.checkAsset(asset);
    }

    @并且("membership-order-校验asset_freeze_ledger为{}")
    public void membershipOrder校验asset_freeze_ledger为Asset_freeze_ledger(String asset_freeze_ledger) {
        checkAll.checkAssetFreezeLedger(asset_freeze_ledger);
    }

    @并且("membership-order-校验asset_ledger为{}")
    public void membershipOrder校验asset_ledger为Asset_ledger(String asset_ledger) {
        checkAll.checkAssetLedger(asset_ledger);
    }

    @并且("membership-order-校验asset_operate为{}")
    public void membershipOrder校验asset_operate为Asset_operate(String asset_operate) {
        checkAll.checkAssetOperate(asset_operate);
    }

    @并且("membership-order-mockTCC异常")
    public void membershipOrderMembershipOrderBuilderMockCreateNotExistThrowException() {
        membershipOrderBuilderMock.createNotExistThrowException();
    }

    @并且("membership-order-mock-tryUpdateVip异常")
    public void membershipOrderMocktryUpdateVip异常() {
        remoteCustomerServiceDubboMock.tryUpdateVipThrowException();
    }

    @当("membership-order-多个用户同时购买会员{}操作结果为{}")
    public void membershipOrder多个用户同时购买会员MembershipPurchase操作结果为PurchaseResult(String membershipPurchase, String purchaseResult) {
        List<MembershipPurchaseRequest> req = JSON.parseArray(membershipPurchase, MembershipPurchaseRequest.class);

        int len = 2;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch mainLatch = new CountDownLatch(len);

        for (int i = 0; i < len; i++) {
            int finalI = i;
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        endLatch.countDown();
                        endLatch.await();
                        MembershipPurchaseResponse membershipPurchaseResponse = membershipOrderService.purchase(req.get(finalI));
                        Thread.sleep(3000);
                        log.info("-----membershipPurchaseResponse:{}", JSON.toJSONString(membershipPurchaseResponse));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    mainLatch.countDown();
                }
            }).start();
        }
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            mainLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }


    @当("membership-order-一个用户同时购买三笔会员请求{}操作结果为{}")
    public void membershipOrder一个用户同时购买三笔会员请求MembershipPurchase操作结果为PurchaseResult(String membershipPurchase, String purchaseResult) {
        List<MembershipPurchaseRequest> req = JSON.parseArray(membershipPurchase, MembershipPurchaseRequest.class);

        int len = 3;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch mainLatch = new CountDownLatch(len);

        for (int i = 0; i < len; i++) {
            int finalI = i;
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        endLatch.countDown();
                        endLatch.await();
                        MembershipPurchaseResponse membershipPurchaseResponse = membershipOrderService.purchase(req.get(finalI));
                        Thread.sleep(5000);
                        log.info("-----membershipPurchaseResponse:{}", JSON.toJSONString(membershipPurchaseResponse));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    mainLatch.countDown();
                }
            }).start();
        }
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            mainLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    @当("membership-order-六个用户同时购买会员{}操作结果为{}")
    public void membershipOrder六个用户同时购买会员MembershipPurchase操作结果为PurchaseResult(String membershipPurchase, String purchaseResult) {
        List<MembershipPurchaseRequest> req = JSON.parseArray(membershipPurchase, MembershipPurchaseRequest.class);

        int len = 6;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch mainLatch = new CountDownLatch(len);

        for (int i = 0; i < len; i++) {
            int finalI = i;
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        endLatch.countDown();
                        endLatch.await();
                        MembershipPurchaseResponse membershipPurchaseResponse = membershipOrderService.purchase(req.get(finalI));
                        log.info("-----membershipPurchaseResponse:{}", JSON.toJSONString(membershipPurchaseResponse));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    mainLatch.countDown();
                }
            }).start();
        }
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            mainLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @并且("membership-order的账务信息为{}")
    public void membershipOrder的账务信息为Exceptions(String exceptions) {
        BusinessOP.exceptions = exceptions;
    }


    @并且("membership-order正在执行操作为{}")
    public void membershipOrder正在执行操作为Stage(String stage) {
        BusinessOP.stage = stage;
    }

    @并且("发送的message为{}")
    public void 发送的message为Message(String message) {
        checkAll.checkMsg(message);
    }

    @并且("membership-order-getRowrawStateException异常")
    public void membershipOrderGetRowrawStateException异常() {
        membershipOrderBuilderMock.getRowrawStateException();
    }

    @并且("membership-order-getRowrawStateNotExist异常")
    public void membershipOrderGetRowrawStateNotExist异常() {
        membershipOrderBuilderMock.getRowrawStateNotExist();
    }

    @并且("membership-order-updateRowFalse，更新未成功")
    public void membershipOrderUpdateRowFalse更新未成功() {
        membershipOrderBuilderMock.updateRowFalse();
    }


    @并且("控制用户{}在membership-order-mock-tryUpdateVip异常")
    public void 控制用户CustomerList在membershipOrderMockTryUpdateVip异常(String customerList) {
        List<String> list = new ArrayList<>(Arrays.asList(customerList.split(",")));
        log.info("customerList{}", list);
        remoteCustomerServiceDubboMock.tryUpdateVipThrowException(list);
    }

    @并且("membership-order-modifyThrowException异常")
    public void membershipOrderModifyThrowException异常() {
        membershipBuilderIndexImplMock.modifyThrowException();
    }

    @并且("membership-order-modify恢复正常")
    public void membershipOrderModify恢复正常() {
        membershipBuilderIndexImplMock.modify();

    }

    @并且("重新去调confirmOrder")
    public void 重新去调confirmorder() {
        MembershipOrderDO membershipOrderDO = BusinessOP.membershipOrderDOMap.values().stream().findFirst().get();
        try {
            membershipOrderPurchaseTccService.confirmOrder(membershipOrderDO, "purchase");
        } catch (Exception e) {
            String eer = JSON.toJSONString(e);
            log.error("---捕获到错误:{}", eer);
        }
    }

    @并且("membership-order-updateVipThrowException异常")
    public void membershipOrderUpdateVipThrowException异常() {
        remoteCustomerServiceDubboMock.updateVipThrowException();
    }

    @并且("membership-order-updateVip恢复正常")
    public void membershipOrderUpdateVip恢复正常() {
        remoteCustomerServiceDubboMock.updateVip();
    }

    @并且("membership-order-sendThrowException异常")
    public void membershipOrderSendThrowException异常() {
        onsProducerMock.sendThrowException();

    }

    @并且("membership-order-send恢复正常")
    public void membershipOrderSend恢复正常() {
        onsProducerMock.send();
    }

    @并且("重新去调cancelOrder")
    public void 重新去调cancelorder() {
        MembershipOrderDO membershipOrderDO = BusinessOP.membershipOrderDOMap.values().stream().findFirst().get();
        membershipOrderPurchaseTccService.cancelOrder(membershipOrderDO, "purchase");
    }

    @并且("membership-order-updateRow恢复正常")
    public void membershipOrderUpdateRow恢复正常() {
        membershipOrderBuilderMock.updateRow();
    }

    @同时("初始化mock信息为tryUpdateVipCustomer")
    public void 初始化mock信息为tryupdatevipcustomer() {
        remoteCustomerServiceDubboMock.tryUpdateVipCustomer();
    }

    @并且("控制所有用户中{}用户{}在membership-order的账务信息为{}")
    public void 控制所有用户中MembershipPurchase用户CustomerList在membershipOrder的账务信息为Exceptions(String membershipPurchase, String customerList, String exceptions) {
        List<String> list = new ArrayList<>(Arrays.asList(customerList.split(",")));
        list.forEach(d ->BusinessOP.customerList.add(d));
//        List<MembershipPurchaseRequest> req = JSON.parseArray(membershipPurchase, MembershipPurchaseRequest.class);
//        req.forEach(f ->  BusinessOP.customerAllList.add(f.getCustomerId()));
//            String customerId = d.getCustomerId();
//            if (list.contains(customerId)) {
//                BusinessOP.exceptions = exceptions;


    }

    @当("操作自动续费开关{}结果为{}")
    public void 操作自动续费开关Option结果为Res(String option,String res) {
        ToogleMembershipOption toogleMembershipOption = JSON.parseObject(option, ToogleMembershipOption.class);
        ToogleMembershipOptionResponse toogleResponse = remoteMemberService.toggleMembershipOption(toogleMembershipOption);
        JSONObject jsonObject = JSON.parseObject(res);
        assertEquals(toogleResponse.getCode().toString(),jsonObject.getString("code"));
    }

    @当("监听到消息{}要去更新customer表")
    public void 监听到消息MemberNotify要去更新customer表(String memberNotify) {
        MemberEventNotifyMsg msg = JSON.parseObject(memberNotify, MemberEventNotifyMsg.class);
        Message message = new Message();
        String body = JSON.toJSONString(msg);
        message.setBody(body.getBytes(StandardCharsets.UTF_8));
        memberNotifyListener.consume(message, new ConsumeContext());
    }

    @那么("最后校验customer为{}")
    public void 最后校验customer为CustomerDO(String customerDO) {
        List<CustomerDTO> contractACT = BusinessOP.customerDTOMap.values().stream().collect(Collectors.toList());
        log.info(">>Actual-- List<CustomerDTO>{}", JSON.toJSONString(contractACT));
        List<CustomerDTO> contractEXP = JSON.parseArray(customerDO, CustomerDTO.class);
        log.info(">>Expect-- List<CustomerDTO>{}", JSON.toJSONString(contractEXP));
        CheckUtils.compare(contractEXP, contractACT);
    }

    @并且("初始化用户信息为{}")
    public void 初始化用户信息为Customers(String customers) {
        List<CustomerDTO> customerDTOS = JSON.parseArray(customers, CustomerDTO.class);
        customerDTOS.forEach(d ->BusinessOP.customerDTOMap.put(d.getId(),d));
    }

    @那么("校验发送的msg为{}")
    public void 校验发送的msg为(String msg) {
        log.info(">>Actual-- List<msg>{}", JSON.toJSONString(BusinessOP.getMsgs()));
        List<String> contractEXP = JSON.parseArray(msg, String.class);
        log.info(">>Expect-- List<msg>{}", JSON.toJSONString(contractEXP));
        CheckUtils.compare(BusinessOP.getMsgs(), contractEXP,new String[]{"expireTime","orderId","timestamp"});
    }
}
