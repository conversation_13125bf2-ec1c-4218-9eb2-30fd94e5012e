package com.kikitrade.member.mock;

public interface MembershipOrderBuilderImplMock {

    /**
     * 保存数据
     */
    void createNotExist();

    /**
     * 查询订单数据
     */
    void query();

    /**
     *  修改
     */
    void modify();


    void createNotExistThrowException();


    void getRow();

    void getRowrawStateException();


    void updateRow();

    void updateRowFalse();

    void getRowrawStateNotExist();
}
