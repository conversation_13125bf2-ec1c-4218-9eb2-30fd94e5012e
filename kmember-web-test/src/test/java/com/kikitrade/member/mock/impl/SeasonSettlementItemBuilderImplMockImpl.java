package com.kikitrade.member.mock.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.asset.dal.tablestore.builder.impl.SeasonSettlementItemBuilderImpl;
import com.kikitrade.asset.dal.tablestore.model.SeasonSettlementItem;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.member.dal.tablestore.model.QuestsPointRankingDO;
import com.kikitrade.member.mock.SeasonSettlementItemBuilderImplMock;
import jakarta.annotation.Resource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

import static com.kikitrade.member.op.BusinessOP.seasonSettlementItemsList;
import static org.mockito.ArgumentMatchers.*;

@Component
public class SeasonSettlementItemBuilderImplMockImpl implements SeasonSettlementItemBuilderImplMock {

    @Resource
    SeasonSettlementItemBuilderImpl seasonSettlementItemBuilder;

    @Override
    public void getById() {
        Mockito.doAnswer(c -> {
            String saasId = c.getArgument(0);
            String season = c.getArgument(1);
            String customerId = c.getArgument(2);
            SeasonSettlementItem copy = seasonSettlementItemsList.stream().filter(a -> a.getSaasId().equals(saasId) && a.getSeason().equals(season) && a.getCustomerId().equals(customerId)).findFirst().orElse(null);
            return BeanUtil.copyProperties(copy, SeasonSettlementItem::new);
        }).when(seasonSettlementItemBuilder).getById(anyString(), anyString(), anyString());
    }

    @Override
    public void getByLimit(){
        Mockito.doAnswer(c ->{
            List<SeasonSettlementItem> collect;
            String saasId = c.getArgument(0);
            String season = c.getArgument(1);
            Boolean isSettlement = c.getArgument(3);
            if (isSettlement == null){
                collect = seasonSettlementItemsList.stream().filter(a ->a.getSaasId().equals(saasId) && a.getSeason().equals(season) && a.getIsSettlement().equals(false)).collect(Collectors.toList());
                return Page.with(collect, 0, 100, collect.size());
            }
            return null;
        }).when(seasonSettlementItemBuilder).getByLimit(anyString(),anyString(),Mockito.any(),Mockito.any());
    }


    @Override
    public void update() {
        Mockito.doAnswer(c -> {
            SeasonSettlementItem argument = c.getArgument(0);
            String saasId = argument.getSaasId();
            String season = argument.getSeason();
            String cid = argument.getCustomerId();
            if (seasonSettlementItemsList.stream().filter(a -> a.getSaasId().equals(saasId) && a.getSeason().equals(season) && a.getCustomerId().equals(cid)).findFirst().isEmpty()) {
                return false;
            }
            seasonSettlementItemsList.stream().filter(b -> b.getSaasId().equals(saasId)).forEach(d -> d.setIsSettlement(argument.getIsSettlement()));
            return true;
        }).when(seasonSettlementItemBuilder).update(any());
    }

}
