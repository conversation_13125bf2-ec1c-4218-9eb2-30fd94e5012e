package com.kikitrade.member.mock.impl;

import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import com.kikitrade.member.mock.SeqClientMock;
import com.kikitrade.member.util.SeqUtil;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class SeqClientMockImpl implements SeqClientMock {
    @Resource
    SeqClient seqClient;

    @Override
    public void next() {
        Mockito.doAnswer(c -> {
            //20221011163927061 69012 15100
            String params = c.getArgument(1);
            String prefix = SeqUtil.prefix();
            return prefix + "69012" + params;
        }).when(seqClient).next(Mockito.any(SeqRule.class), anyString());

        Mockito.doAnswer(c -> {
            //20221011074020809 69012 0000 28779
            String seq = c.getArgument(1);
            String currencyId = c.getArgument(2);
            String prefix = SeqUtil.prefix();
            double r = Math.random();
            int num = (int) (r*100000000 + 1);
            String id = prefix + num + currencyId + seq;
            return id;
        }).when(seqClient).next(Mockito.any(SeqRule.class), anyString(), anyString());
    }






}
