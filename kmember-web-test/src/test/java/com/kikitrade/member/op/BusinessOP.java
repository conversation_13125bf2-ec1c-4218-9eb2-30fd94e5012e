package com.kikitrade.member.op;

import com.alibaba.fastjson.JSON;
import com.kikitrade.asset.dal.tablestore.model.AssetReadonlyDO;
import com.kikitrade.asset.dal.tablestore.model.SeasonSettlementItem;
import com.kikitrade.asset.dal.tablestore.model.SeasonSettlementSummary;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.DingTalkMessageDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.member.dal.mysql.dao.AssetFreezeLedgerTestDao;
import com.kikitrade.member.dal.mysql.dao.AssetLedgerTestDao;
import com.kikitrade.member.dal.mysql.dao.AssetOperationTestDao;
import com.kikitrade.member.dal.mysql.dao.AssetTestDao;
import com.kikitrade.member.dal.tablestore.model.MembershipDO;
import com.kikitrade.member.dal.tablestore.model.MembershipOrderDO;
import com.kikitrade.member.dal.tablestore.model.QuestsPointRankingDO;
import com.kikitrade.member.model.MemberEventNotifyMsg;

import com.kikitrade.member.model.TopLadderDTO;
import com.kikitrade.member.model.response.QuestLadderResponse;
import com.kikitrade.member.service.common.KmemberProperties;
import com.kikitrade.member.util.DateUtil;
import com.kikitrade.order.dal.tablestore.model.MemberBusinessOrderDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.util.*;

@Component
@Slf4j
public class BusinessOP {

    @Resource
    AssetTestDao assetTestDao;
    @Resource
    AssetFreezeLedgerTestDao assetFreezeLedgerTestDao;
    @Resource
    AssetLedgerTestDao assetLedgerTestDao;
    @Resource
    AssetOperationTestDao assetOperationTestDao;
    @Resource
    KmemberProperties kmemberProperties;

    //存储错误信息
    public static List<String> exceptionList = new ArrayList<>();

    public static List<String> customerList = new ArrayList<>();

//    public static List<String> customerAllList = new ArrayList<>();

    public static Map<String, MembershipOrderDO> membershipOrderDOMap = new HashMap<>();

    public static Map<String, MembershipDO> membershipDOMap = new HashMap<>();

    public static Map<String, MemberBusinessOrderDO> memberBusinessOrderDOMap = new HashMap<>();

    public static Map<String, CustomerDTO> customerDTOMap = new HashMap<>();

    public static String exceptions = null;

    public static String stage = null;

    private static List<String> msgs = new ArrayList<>();

    public static List<String> getMsgs() {
        return msgs;
    }

    public static void setMsgs(List<String> msgs) {
        BusinessOP.msgs = msgs;
    }

    public static List<QuestLadderResponse> questLadderResponseList = new ArrayList<>();

    public static List<QuestsPointRankingDO> questsPointRankingDOList = new ArrayList<>();

    public static Map<String, AssetReadonlyDO> assetReadonlyDOMap = new HashMap<>();

    public static Map<String, TCustomerDTO> tcustomerDTOMap = new HashMap<>();

    public static Map<String, CustomerBindDTO> customerBindDTOMap = new HashMap<>();

    public static List<SeasonSettlementItem> seasonSettlementItemsList = new ArrayList<>();

    public static List<SeasonSettlementSummary> seasonSettlementSummaryList = new ArrayList<>();

    public static List<DingTalkMessageDTO> msgList = new ArrayList<>();

    public static List<TopLadderDTO> topLadderDTOList = new ArrayList<>();

    //初始化
    public void init() {
        clearLocalCache();
        clearDB();
        //addLocalCache();
    }

    /**
     * 清空缓存
     */
    private void clearLocalCache() {
        membershipDOMap.clear();
        membershipOrderDOMap.clear();
        msgs.clear();
        exceptions = null;
        customerDTOMap.clear();
        stage = null;
        customerList.clear();
//        customerAllList.clear();
        questLadderResponseList.clear();
        questsPointRankingDOList.clear();
        assetReadonlyDOMap.clear();
        tcustomerDTOMap.clear();
        seasonSettlementItemsList.clear();
        seasonSettlementSummaryList.clear();
        msgList.clear();
        topLadderDTOList.clear();
        customerBindDTOMap.clear();
        System.out.println(">>>init end");
    }

    /**
     * 清空mysql：asset、asset_freeze_ledger、asset_ledger、asset_operation
     */
    private void clearDB() {
        assetTestDao.deleteAll();
        assetFreezeLedgerTestDao.truncateTableAssetFreeze(DateUtil.getToday());
        assetLedgerTestDao.truncateTableAssetLedger(DateUtil.getToday());
        assetOperationTestDao.truncateTableAssetOperation(DateUtil.getToday());
    }

    public void addMyLadder(List<QuestsPointRankingDO> myLadderDOS) {
        log.info("初始化myladder数据 -->{}", myLadderDOS);
        questsPointRankingDOList.addAll(myLadderDOS);
        log.info(">>已添加myladder数据--List<QuestsPointRankingDO>{}", JSON.toJSONString(questsPointRankingDOList));
    }

    public void addAsset(List<AssetReadonlyDO> assetReadonlyDOS) {
        log.info("初始化assetreadOnly数据 -->{}", assetReadonlyDOS);
        assetReadonlyDOS.stream().forEach((assetReadonlyDO -> {
            assetReadonlyDOMap.put(assetReadonlyDO.getId(), assetReadonlyDO);
        }));
        log.info(">>已添加assetreadOnly数据--Map<AssetReadonlyDO>{}", JSON.toJSONString(assetReadonlyDOMap));
    }

    public void addTCustomerDTO(List<TCustomerDTO> tcustomerDTOS) {
        log.info("初始化Tcustomer数据 -->{}", tcustomerDTOS);
        tcustomerDTOS.stream().forEach((tcustomerDTO -> {
            tcustomerDTOMap.put(tcustomerDTO.getUid(), tcustomerDTO);
        }));
        log.info(">>已添加Tcustomer信息--Map<String, CustomerDTO>{}", JSON.toJSONString(tcustomerDTOMap));

    }

    public void addCustomerBindDTO(List<CustomerBindDTO> customerDTOS) {
        log.info("初始化Tcustomer数据 -->{}", customerDTOS);
        customerDTOS.stream().forEach((tcustomerDTO -> {
            customerBindDTOMap.put(tcustomerDTO.getUid(), tcustomerDTO);
        }));
        log.info(">>已添加Tcustomer信息--Map<String, CustomerDTO>{}", JSON.toJSONString(customerBindDTOMap));

    }

    public void addSeasonSettlementItems(List<SeasonSettlementItem> seasonSettlementDOS) {
        log.info("初始化SeasonSettlementItems数据 -->{}", seasonSettlementDOS);
        seasonSettlementItemsList.addAll(seasonSettlementDOS);
        log.info(">>已添加SeasonSettlementItems信息--List<SeasonSettlementItem>{}", JSON.toJSONString(seasonSettlementItemsList));

    }

    /**
     * 添加用户vip到缓存
     */
    public void addMembershipDO(List<MembershipDO> membershipDOS) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        //calendar.set(Calendar.HOUR_OF_DAY,kmemberProperties.getRenewalNotifyHour() + 2);
        calendar.set(Calendar.HOUR_OF_DAY, kmemberProperties.getNotify().getExpirationHourOfDay() + 2);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date expireTime = calendar.getTime();
        //确保vip未失效且notifyTime在系统通知时间段内
        membershipDOS.stream().forEach((membershipDO -> {
            if (membershipDO.getSaasId().equals("aspen")) {
                membershipDO.setExpireTime(DateUtils.addDays(expireTime, +100));
                membershipDO.setNotifyTime(DateUtils.addDays(expireTime, +99));
            } else {
                membershipDO.setExpireTime(expireTime);
                membershipDO.setNotifyTime(DateUtils.addDays(expireTime, -1));
            }
            membershipDOMap.put(membershipDO.getCustomerId(), membershipDO);
        }));
        log.info(">>已添加vip会员信息--Map<String, MembershipDO>{}", JSON.toJSONString(membershipDOMap));
    }

    /**
     * 添加用户信息到缓存
     */
    public void addCustomerDTO(List<CustomerDTO> customerDTOS) {
        //确保vip未失效且notifyTime在系统通知时间段内
        customerDTOS.stream().forEach((customerDTO -> {
            customerDTOMap.put(customerDTO.getId(), customerDTO);
        }));
        log.info(">>已添加customer信息--Map<String, CustomerDTO>{}", JSON.toJSONString(customerDTOMap));
    }

}
