<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kmember</artifactId>
        <groupId>com.kikitrade</groupId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>kmember-web-test</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <!-- 指定项目是JDK1.7版本 -->
        <java-version>17</java-version>
        <!--指定Maven用什么编码来读取源码及文档 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.property.path>../</project.property.path>
        <!--指定Maven用什么编码来呈现 -->
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <version>3.0.3</version>
        </dependency>
        <dependency>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>brave-instrumentation-spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kmember-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!--测试的起步依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-bdd-testing</artifactId>
            <type>pom</type>
            <version>2.3.0-SNAPSHOT</version>
            <scope>import</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-junit-jupiter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.3.1</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.3.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.wix</groupId>
            <artifactId>wix-embedded-mysql</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!--        <dependency>
                    <groupId>org.junit.platform</groupId>
                    <artifactId>junit-platform-suite</artifactId>
                    <version>1.8.2</version>
                    <scope>test</scope>
                </dependency>-->
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>5.6.3</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <argLine>
                        --add-opens=java.base/java.lang=ALL-UNNAMED
                        --add-opens=java.base/java.math=ALL-UNNAMED
                        --add-opens=java.base/java.util=ALL-UNNAMED
                        --add-opens=java.base/java.text=ALL-UNNAMED
                        --add-opens=java.base/java.nio=ALL-UNNAMED
                        --add-opens=java.base/sun.reflect.annotation=ALL-UNNAMED
                    </argLine>
                    <parallel>classes</parallel>
                    <!--                    <forkCount>1C</forkCount>--><!--进程数 vms-->
                    <!--                    <reuseForks>true</reuseForks>--><!--进程执行完后是否继续执行其他case-->
                    <!--                    <threadCount>1</threadCount>&lt;!&ndash;线程数&ndash;&gt;-->
                    <threadCountClasses>3</threadCountClasses>
                    <useUnlimitedThreads>false</useUnlimitedThreads>
                    <includes>
                        <include>RunCucumberTest.class</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>