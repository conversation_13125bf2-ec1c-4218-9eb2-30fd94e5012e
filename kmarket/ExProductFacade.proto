syntax = "proto3";
import "google/protobuf/timestamp.proto";
package com.kikitrade.kmarket.facade.currency;
option java_package = "com.kikitrade.kmarket.facade.currency";
option java_multiple_files = true;

enum ProductIssueStateEnum {
  DRAFT = 0;
  PUBLISHED = 1;
  SUSPENDED = 2;
  DELISTED = 3;
}

enum WithdrawFeeType {
  NONE = 0;
  PERCENTAGE = 1;
  FIXED = 2;
  MIXED = 3;
}

enum ProductStatusEnum {
  NORMAL = 0; //发行状态
  DELETED = 1; // 软删除状态，不进行交易
  INNER_TEST = 2; //内测状态，内测用户可见，其余用户不可见
}

message ExProduct {
  ////////////////////基本信息////////////////////
  int64 id = 1;  // 币种ID
  string name = 2; // 币种名称，不能重复
  string coinCode = 3; // 币的代码,不能为空，不能重复
  int32 sort = 4; // 币种排序，不能重复，交易所内排名
  google.protobuf.Timestamp modified = 5; // 修改时间
  repeated string zoneKey = 6; // 所属板块
  string picturePath = 7; // 图片路径

  ////////////////////币种简介////////////////////
  int32 ranking = 8; // 币种排名
  string marketValue = 9; // 市值
  string circulatingSupply = 10; // 流通供给量，管理端python框架限制，int类型长度会超过最大长度
  string maxSupply = 11;// 最大发行量，管理端python框架限制，int类型长度会超过最大长度
  string totalSupply = 12;// 总供给量，管理端python框架限制，int类型长度会超过最大长度
  string officialWebsite = 13;// 官方网站
  google.protobuf.Timestamp issueTime = 14;// 发行时间
  string blockBrowser = 15;// 区块浏览器
  string whitePaper = 16; // 白皮书
  string introductionZh = 17; //中文介绍
  string introductionEn = 18; //英文介绍
  string introductionHk = 19; //粤语介绍

  ////////////////////业务配置////////////////////
  ProductIssueStateEnum issueState = 20; // 产品发行状态
  bool fiat = 21; // 是否是法币
  bool stable = 22;// 是否稳定币
  bool baseCurrency = 23; // 是否为定价币
  string baseCurrencySort = 24; // 定价币排序
  int32 showKeepDecimalForCoin = 25;// 币种前端展示精度
  int32 keepDecimalForCoin = 26;// 币种精度
  ProductStatusEnum status = 27; // 产品状态
  bool customCoin = 28; // 是否自定义币种
  string queryParam = 29; // 币种信息查询参数
  bool enabledConvert = 30; //是否开启闪兑，true-允许，false-禁止，默认：false
  string bufferSpread = 31; //闪兑buffer
}

//////////////////////以下服务定义
message ModifyExProductResponse {
  bool success = 1;
  string message = 2;
  ExProduct product = 3;
}


message FindExProductResponse {
  bool success = 1;
  string message = 2;
  repeated ExProduct product = 3;

}

message IssueStateRequest{
  repeated ProductIssueStateEnum issueState = 1;
}

message CoinCodeRequest {
  repeated string coinCode = 1;
}

message PublishRequest{
  int64 id = 1;
}

service ExProductManageFacade {
  // 新增或者修改product
  rpc UpsertExProduct (ExProduct) returns (ModifyExProductResponse);
  // 根据issueState 查找ExProduct
  rpc findExProductByIssueState (IssueStateRequest) returns (FindExProductResponse);
  // publish product
  rpc publishProduct(PublishRequest) returns (ExProduct);
  // unlist product
  rpc unlistProduct(PublishRequest) returns (ExProduct);
  // query exProducts by coinCodes
  rpc findExProductByCoinCodes (CoinCodeRequest) returns (FindExProductResponse);
}