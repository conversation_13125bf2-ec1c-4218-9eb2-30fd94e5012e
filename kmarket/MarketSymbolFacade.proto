syntax = "proto3";
import "google/protobuf/empty.proto";
package com.kikitrade.kmarket.facade.symbol;
option java_package = "com.kikitrade.kmarket.facade.symbol";
option java_multiple_files = true;

enum AgentStatus {
  NORMAL = 0; // 正常状态，目前没有这个状态的盘口
  AGENT = 1; // 代理盘口，该盘口的部分行情数据来自外部exchange
  AGENT_CONVERT = 2; // 代理转换，该盘口通过外部代理盘口的行情数据来计算该盘口的行情数据
}

enum KlineMode {
    Mixed = 0;  // 混合kline: 优先使用自治kline，缺失时使用代理kline。新增symbol时取该值作为默认值
    Autonomy = 1;        // 自治kline
    Agent = 2; // 代理kline
}

message MarketSymbol {
  int64 id = 1;
  string currency = 2; // 交易币种
  string quoteCurrency = 3; // 定价币种
  int32 symbolPrecision = 4; //app，行情展示精度
  int32 pricePrecision = 5; //后端，交易币价格精度
  int32 volumePrecision = 6; //app，精度(包括手續費精度/利息精度/錢包）
  int32 tradePrecision = 7; //app，交易价格输入精度
  int32 tradeCalPrecision = 8; //后端，定价币价格精度
  int32 tradeVolumePrecision = 9; // app，成交量精度，用户订单和委托展示
  int32 tradeInputPrecision = 10; // app，输入交易币数量精度
  enum SymbolStat {
    PENDING = 0; // 准备上线状态
    NORMAL = 1; // 正常状态
    INNER_TEST = 2; // 内测状态
    OFFLINE= 3; // 下线状态
  }
  SymbolStat state = 11;
  string orderMin = 12; // 订单最小限额
  string orderMax = 13; // 订单最大限额
  int32 recommendIndex = 14; // 盘口排序
  string marketFreezeBuffer = 15; // 市价单冻结比例
  AgentStatus agentStatus = 16; // 代理状态
  string agentMapping = 17; // 行情计算代理盘口
  //  string agentMappingRate = 20;
  bool top = 18; // 是否热搜盘口
  bool favorite = 19; // 是否自选盘口推介
  string topIndex = 20; // 热搜盘口的顺序
  string favoriteIndex = 21; // 自选推介盘口顺序
  bool allowTrade = 22; // 是否允许交易，true-允许，false-禁止，默认：true
  KlineMode klineMode = 23; // kline模式
}

message MarketSymbolQryReply {
  repeated MarketSymbol symbols = 1;
}

message MarketSymbolQryReq {
  repeated string symbols = 1;
}

message AddSymbolReply {
  bool success = 1;
  string message = 2;
}

message UpdateSymbolReply {
  bool success = 1;
  string message = 2;
}

service MarketSymbolFacade {
  // 新增盘口
  rpc add(MarketSymbol) returns (AddSymbolReply);
  // 修改盘口
  rpc update(MarketSymbol) returns (UpdateSymbolReply);
  // 查询接口
  rpc query(MarketSymbolQryReq) returns (MarketSymbolQryReply);
}
