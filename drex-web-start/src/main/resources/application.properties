# Server configuration
server.port=8082

# Dubbo configuration
dubbo.application.name=drex-web
dubbo.application.id=drex-web
dubbo.application.checkSerializable=false
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20880
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.provider.timeout=10000
dubbo.consumer.timeout=10000
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.application.parameters.router=traffic
dubbo.provider.parameters.traffic=${TRAFFIC_TAG}
dubbo.provider.prefer-serialization=hessian2,fastjson2
dubbo.provider.serialization=hessian2
dubbo.application.serialize-check-status=WARN
dubbo.application.check-serializable=false
dubbo.application.metadata-type=remote
dubbo.tracing.enabled=true

management.metrics.tags.application=drex-web
management.health.db.enabled=true
management.health.solr.enabled=false
management.health.mongo.enabled=true
management.health.cassandra.enabled=false
management.health.elasticsearch.enabled=false
management.health.influxdb.enabled=false
management.health.neo4j.enabled=false
management.server.port=9090
management.health.defaults.enabled=false
management.endpoint.health.show-details=always
management.endpoints.migrate-legacy-ids=true
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.prometheus.metrics.export.enabled=true
dubbo.metrics.protocol=prometheus
dubbo.metrics.aggregation.enabled=true
dubbo.metrics.prometheus.exporter.enabled=true
management.endpoints.web.exposure.include=env,health,info,httptrace,metrics,heapdump,threaddump,prometheus,dubbo,druid
spring.config.import=metrics.properties

# Logging configuration
logging.level.root=INFO
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n
logging.level.com.kikitrade=DEBUG

springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.use-root-path=true
springdoc.swagger-ui.display-request-duration=false
springdoc.swagger-ui.groups-order=asc
springdoc.swagger-ui.operations-sorter=method
springdoc.api-docs.version=openapi_3_0
springdoc.show-actuator=false
springdoc.api-docs.groups.enabled=true


springdoc.group-configs[0].group=Customer
springdoc.group-configs[0].paths-to-match=/v1/customers/**, /v1/auth/**
springdoc.group-configs[1].group=Activity
springdoc.group-configs[1].paths-to-match=/v1/task/**
springdoc.group-configs[2].group=Core
springdoc.group-configs[2].paths-to-match=/v1/rexys/**, /v1/news/**, /v1/notice/**

