package com.drex.activity.task.model.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/23 11:31
 * @description:
 */
public class ActivityConstant {

    @Getter
    public enum CommonStatus {

        ACTIVE(0),
        DISABLE(1),
        DELETED(-1),
        //灰度
        GRAY(2);

        private int code;

        CommonStatus(int code) {
            this.code = code;
        }
    }

    public enum TaskStatusEnum {
        NOT_STARTED,
        APPENDING,
        DOING,//进行中
        DONE,//已完成
        FAIL;
    }

    @Getter
    public enum TaskCompleteStatus {
        DONE(1),
        APPENDING(0);

        private int status;

        TaskCompleteStatus(int status){
            this.status = status;
        }
    }

    @Getter
    public enum VipLevelEnum {
        NORMAL(0),
        L1(1);

        private Integer level;

        VipLevelEnum(Integer level) {
            this.level = level;
        }
    }


    @Getter
    public enum SideEnum {
        INVITER,
        INVITEE
    }

    public enum AwardTypeEnum {
        POINT,
        MAIZE,
        MAIZE_KERNEL;

        public static AwardTypeEnum getType(String type) {
            try {
                return AwardTypeEnum.valueOf(type.toUpperCase());
            } catch (Exception ex) {
                return POINT;
            }
        }

    }
}
