package org.opensocial.search.web.converter;

import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.ksocial.api.model.relation.RelationResponse;
import com.kikitrade.ksocial.common.constants.relation.RelationEnum;
import org.apache.commons.lang3.StringUtils;
import org.opensocial.common.constants.SocialConstants;
import org.opensocial.search.opeanapi.model.Dispatcher;
import org.opensocial.search.opeanapi.model.Profile;
import org.opensocial.search.opeanapi.model.RelationRoleEnum;
//import org.opensocial.index.opeanapi.model.*;

/**
 * @Author: ZhangPengfei
 * @Date: 2023/5/10 7:44 PM
 */
public class ProfileResponseConverter {

    /*public static ProfileResponse convertUserResponse(com.kikitrade.ksocial.api.model.user.UserResponse userResponse) {
        Profile user = convertUser(userResponse);
        ProfileResponse response = new ProfileResponse();
        response.data(user);
        return response;
    }


    public static ProfilePaginationResponse convertUserPagination(List<com.kikitrade.ksocial.api.model.user.UserResponse> userResponses) {
        ProfilePaginationResponse response = new ProfilePaginationResponse();
        ProfilePagination ofObj = new ProfilePagination();
        List<Profile> users = new ArrayList<>();
        if (null != userResponses && userResponses.size() > 0) {
            users = userResponses.stream().map(item -> convertUser(item)).collect(Collectors.toList());
        }
        ofObj.rows(users);
        response.data(ofObj);
        return response;
    }*/


    public static Profile convertUser(com.kikitrade.ksocial.api.model.user.UserResponse userResponse) {
        if (null != userResponse) {
            Profile user = convertSimpleUser(userResponse);
            return user;
        }
        return null;
    }


    public static Profile convertSimpleUser(com.kikitrade.ksocial.api.model.user.SimpleUserResponse userResponse) {
        if (null != userResponse) {
            Profile user = new Profile();
            BeanUtil.copyProperties(userResponse, user);
            user.setHandle(userResponse.getUniqueName());
            user.setOwner(userResponse.getOwnerId());
            user.setAddress(userResponse.getAddress());
            user.setProfileId(userResponse.getProfileId());
            user.setBio(userResponse.getIntro());
            user.setUserId(userResponse.getId());

            user.setFollowing(SocialConstants.FollowStatus.NONE.getCode());
            if (null!=userResponse.getRelations()){
                RelationResponse followRelation = userResponse.getRelations().get(RelationEnum.FOLLOW);
                if (null == followRelation){
                    user.setFollowing(SocialConstants.FollowStatus.NONE.getCode());
                }else if(null==followRelation.getTokenIds() || followRelation.getTokenIds().size()==0){
                    user.setFollowing(SocialConstants.FollowStatus.OFF_CHAIN.getCode());
                }else{
                    user.setFollowing(SocialConstants.FollowStatus.ON_CHAIN.getCode());
                }
            }

            if (StringUtils.isNotBlank(userResponse.getDispatcher())) {
                user.setDispatcher(JSON.parseObject(userResponse.getDispatcher(), Dispatcher.class));
            }
            user.setFollowNftAddress(userResponse.getFollowNft());
            if (StringUtils.isNotBlank(userResponse.getFollowModule())) {
                user.setFollowCondition(userResponse.getFollowModule());
            }
            if (userResponse.getJoiningRole() != null){
                user.setJoiningRole(RelationRoleEnum.valueOf(userResponse.getJoiningRole().name()));
            }
            return user;
        }
        return null;
    }

}
