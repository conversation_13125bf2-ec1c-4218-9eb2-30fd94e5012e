package org.opensocial.search.web.converter;

import lombok.Getter;

import java.util.Arrays;

/**
 * 用户关系表角色
 */
public enum RelationRoleEnum {
    //目前需要排序的是OWNER和MEMBER，其他类型先定义一个编码；以后要是其他类型也需要一起排序就能用，要是产品需要的排序和当前定义不一样，可以按照新的编号重新刷新老数据
    OWNER(10),
    ADMIN(20),
    MODS(25),
    MEMBER(30),
    ;

    @Getter
    private int code;

    RelationRoleEnum() {

    }

    RelationRoleEnum(int code) {
        this.code = code;
    }

    public static RelationRoleEnum getByCode(Integer role) {
        if (role == null) {
            return null;
        }
        return Arrays.stream(RelationRoleEnum.values()).filter(relationRoleEnum -> relationRoleEnum.getCode() == role).findFirst().orElse(null);
    }
}
