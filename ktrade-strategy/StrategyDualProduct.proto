syntax = "proto3";
import "google/protobuf/timestamp.proto";
package com.kikitrade.kstrategy.grpc.service;
option java_package = "com.kikitrade.kstrategy.grpc.service.structured";
option java_multiple_files = true;


enum StructuredProductStatusEnum{
  // 内测
  INNER_TEST = 0;
  // 激活
  ACTIVE = 1;
  // 隐藏
  HIDDEN = 2;
  // 停止
  STOPPED = 3;
}

// 产品类型
enum TypeEnum {
  BuyLow = 0;
  SellHigh = 1;
}

// 业务类型
enum BusinessTypeEnum {
  DUAL = 0;
  ACCUMULATOR = 1;
  SNOWBALL = 2;
}

message Decimal {
  string value = 1;
}

message StructuredProduct {
  // 产品id
  string productId = 1;
  // 产品名称
  string name = 2;
  // 状态
  StructuredProductStatusEnum status = 3;
  // 业务类型
  BusinessTypeEnum businessType = 4;
  // 类型
  TypeEnum type = 5;
  // 标的币种
  string underlying = 6;
  // 申购币种
  string subscription = 7;
  // 目标币种
  string quote = 8;
  // 最小申购额
  Decimal subscriptionMin = 9;
  // 最大申购饿
  Decimal subscriptionMax = 10;
  // 递增额度
  Decimal tickSize = 11;
  // 结算具体延迟时间 分钟
  int32 settleMinuteBuffer = 12;
  string created = 13;
  string modified = 14;
  string saasId = 15;
}

message UpsertStructuredProductResponse {
  bool success = 1;
  string message = 2;
  // 产品id
  string productId = 3;
}

message ListStructuredProductResponse {
  bool success = 1;
  string message = 2;
  repeated StructuredProduct structuredProductList = 3;
}

message ListStructuredProductRequest {
  // 业务类型
  BusinessTypeEnum businessType = 1;
}

service StrategyStructuredProductFacade {
  // 新增或修改双币产品
  rpc upsertStructuredProduct(StructuredProduct) returns (UpsertStructuredProductResponse);

  // 查询双币产品列表
  rpc listStructuredProduct(ListStructuredProductRequest) returns (ListStructuredProductResponse);
}