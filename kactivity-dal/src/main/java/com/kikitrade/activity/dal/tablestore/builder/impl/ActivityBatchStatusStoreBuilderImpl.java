package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Direction;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus.INDEX_ACTIVITY_BATCH_STATUS_DEFAULT_INDEX;


@Slf4j
@Repository
public class ActivityBatchStatusStoreBuilderImpl extends WideColumnStoreBuilder<ActivityBatchStatus> implements ActivityBatchStatusStoreBuilder {

    @PostConstruct
    public void init() {
        init(ActivityBatchStatus.class);
    }

    @Override
    public List<ActivityBatchStatus> findByStatus(String status, int limit) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("status", PrimaryKeyValue.fromString(status), PrimaryKeyValue.fromString(status)));
        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        return rangeQuery(INDEX_ACTIVITY_BATCH_STATUS_DEFAULT_INDEX, queryList, Direction.FORWARD,limit);
    }

    @Override
    public boolean updateStatusAndNum(String batchId, String status, Integer num) {
        ActivityBatchStatus activityBatchStatus = new ActivityBatchStatus();
        activityBatchStatus.setBatchId(batchId);
        activityBatchStatus.setStatus(status);
        activityBatchStatus.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        if(num != null){
            activityBatchStatus.setNum(num);
        }
        return updateRow(activityBatchStatus);
    }

    @Override
    public ActivityBatchStatus getById(String batchId){
        ActivityBatchStatus batchStatus = new ActivityBatchStatus();
        batchStatus.setBatchId(batchId);
        return super.getRow(batchStatus);
    }

    @Override
    public boolean insert(ActivityBatchStatus activityBatchStatus) {
        return putRow(activityBatchStatus, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }
}
