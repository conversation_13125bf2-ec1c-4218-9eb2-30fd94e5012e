package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ActivityLotteryDetailConfigDao extends BaseDao<ActivityLotteryDetailConfig, String> {

    @Insert("<script>" +
            "insert into activity_lottery_detail_config(`id`,`lottery_id`,`name`,`amount`,`currency`,`num`,`percent`,`is_low`,`remain_num`,`created`,`modified`,`saasId`,`award_type`) values " +
            " <foreach collection='configList' item='item' index='index' separator=','>" +
            "            <trim prefix='(' suffix=')' suffixOverrides=','>" +
            "                    #{item.id,jdbcType=VARCHAR}," +
            "                    #{item.lotteryId,jdbcType=VARCHAR}," +
            "                    #{item.name,jdbcType=VARCHAR}," +
            "                    #{item.amount,jdbcType=DECIMAL}," +
            "                    #{item.currency,jdbcType=VARCHAR}," +
            "                    #{item.num,jdbcType=INTEGER}," +
            "                    #{item.percent,jdbcType=DECIMAL}," +
            "                    #{item.isLow,jdbcType=TINYINT}," +
            "                    #{item.remainNum,jdbcType=INTEGER}," +
            "                    #{item.created,jdbcType=TIMESTAMP}," +
            "                    #{item.modified,jdbcType=TIMESTAMP}," +
            "                    #{item.saasId,jdbcType=VARCHAR}," +
            "                    #{item.awardType,jdbcType=VARCHAR}," +
            "            </trim>" +
            "        </foreach>" +
            "</script>")
    boolean batchInsert(@Param("configList") List<ActivityLotteryDetailConfig> configList);

    @Update("update activity_lottery_detail_config set status = -1, modified = now() where lottery_id = #{lotteryId}")
    int deleteByLotteryId(String lotteryId);

    @Update("update activity_lottery_detail_config set status = -1, modified = now() where id = #{id}")
    int deleteById(String id);

    @Update("update activity_lottery_detail_config set remain_num = remain_num - 1, modified = now() where id = #{id} and remain_num >= 1")
    int decreaseStoreNum(String id);

    @Update("update activity_lottery_detail_config set remain_num = remain_num + #{num}, modified = now() where id = #{id}")
    int increaseStoreNum(String id, int num);
}
