package com.kikitrade.activity.dal.tablestore.builder;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
public interface LuckFortuneReleaseItemBuilder {

    String getTableName();

    /**
     * 插入一个发放的红包
     * @param luckFortuneReleaseItem
     * @return
     */
    boolean insert(LuckFortuneReleaseItem luckFortuneReleaseItem);

    /**
     * 更新发放的红包状态到领取中
     * @param luckFortuneReleaseItem
     * @return
     */
    boolean update(LuckFortuneReleaseItem luckFortuneReleaseItem);

    /**
     * 更新发放的红包状态到领取中
     * @param luckFortuneReleaseItem
     * @return
     */
    boolean updateStatusToDrawing(LuckFortuneReleaseItem luckFortuneReleaseItem);

    /**
     * 更新发放的红包状态到无效
     * @param luckFortuneReleaseItem
     * @return
     */
    boolean updateStatusToInvalid(LuckFortuneReleaseItem luckFortuneReleaseItem);

    /**
     * 更新发放的红包状态到领取完成
     * @param luckFortuneReleaseItem
     * @return
     */
    boolean updateStatusToDrew(LuckFortuneReleaseItem luckFortuneReleaseItem);

    /**
     * 更新未退款中
     * @param luckFortuneReleaseItem
     * @return
     */
    boolean updateToRefunding(LuckFortuneReleaseItem luckFortuneReleaseItem);

    /**
     * 更新发放的红包状态到已过期
     * @param luckFortuneReleaseItem
     * @return
     */
    boolean updateStatusToExpire(LuckFortuneReleaseItem luckFortuneReleaseItem);

    /**
     * 根据红包id，查询红包
     * @param id
     * @return
     */
    LuckFortuneReleaseItem findById(String id);

    /**
     * 根据用户id，查询红包
     * @param customerId
     * @return
     */
    Page<LuckFortuneReleaseItem> findByCustomerId(String customerId, String type, String startTime, String endTime, int offset, int limit);

    /**
     * 根据用户id，查询红包
     * @param endTime
     * @return
     */
    Page<LuckFortuneReleaseItem> findByExpiredTime(Integer status, String endTime, int offset);

    /**
     * 发放的红包总数
     * @param customerId
     * @return
     */
    long countRelease(String customerId);

    /**
     * 历史发放的空投总价值
     * @param customerId
     * @return
     */
    BigDecimal sumAmountRelease(String customerId);

    /**
     * 根据红包id，查询红包
     * @param releaseCode
     * @return
     */
    LuckFortuneReleaseItem findByReleaseCode(String releaseCode);
}
