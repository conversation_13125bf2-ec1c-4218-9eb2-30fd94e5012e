package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.kikitrade.activity.dal.tablestore.builder.PrecisionPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.PrecisionPool;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

@Service
public class PrecisionPoolBuilderImpl  extends WideColumnStoreBuilder<PrecisionPool>  implements PrecisionPoolBuilder {


    @Override
    public String getTableName(){
        return "precision_pool";
    }

    @PostConstruct
    public void init() {
        init(PrecisionPool.class);
    }

    @Override
    public boolean insert(PrecisionPool precisionPool) {
        return super.putRow(precisionPool);
    }

    @Override
    public boolean update(PrecisionPool precisionPool) {
        return super.updateRow(precisionPool);
    }

    @Override
    public PrecisionPool findById(String id) {
        PrecisionPool param = new PrecisionPool();
        param.setId(id);
        return super.getRow(param);
    }
}
