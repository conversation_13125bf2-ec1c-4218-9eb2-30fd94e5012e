<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.SchedLogDao">


    <select id="findByBatch" resultType="com.kikitrade.activity.dal.mysql.model.SchedLog">
        SELECT *
        FROM sched_log
        where date_format(batch_pt, '%Y%m%d')  = #{batch_pt} and job_nm=#{job_nm} and is_deleted='N'
         order by end_time desc
         LIMIT 0,1
    </select>


    <select id="findByLatestBatch" resultType="com.kikitrade.activity.dal.mysql.model.SchedLog">
        SELECT *
        FROM sched_log
        where  job_nm=#{job_nm} and is_deleted='N'
         order by batch_pt desc
         LIMIT 0,1
    </select>

</mapper>
