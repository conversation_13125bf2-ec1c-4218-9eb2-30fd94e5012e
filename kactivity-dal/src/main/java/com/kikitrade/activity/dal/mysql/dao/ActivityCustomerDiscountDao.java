package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityCustomerDiscount;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityCustomerDiscountDao extends BaseDao<ActivityCustomerDiscount, String> {

    List<ActivityCustomerDiscount> findAll(@Param("discountDate") String discountDate, @Param("offset") Integer offset, @Param("limit") Integer limit);

    int updateStatus(@Param("discountDate") String discountDate, @Param("customerId") String customerId, @Param("investmentProductId") String investmentProductId, @Param("status") Integer status);

    long countByStatus(@Param("discountDate") String discountDate, @Param("status") int status);
}
