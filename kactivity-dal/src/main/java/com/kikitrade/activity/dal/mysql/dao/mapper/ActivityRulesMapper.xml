<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityRulesDao">

    <!-- 增加 -->
    <insert id="insert" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityRules">
        INSERT INTO activity_rules VALUES (
            #{id}, NOW(), NOW(),
            #{rule_name},
            #{desc},
            #{priority},
            #{status},
            #{params}
        )
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityRules">
        update activity_rules ar
        <set>
            ar.update_time=NOW(),
            <if test="rule_name!=null">
                ar.rule_name=#{rule_name},
            </if>
            <if test="desc!=null">
                ar.desc=#{desc},
            </if>
            <if test="priority!=null">
                ar.priority=#{priority},
            </if>
            <if test="status!=null">
                ar.status=#{status},
            </if>
            <if test="params!=null">
                ar.params=#{params}
            </if>
        </set>
        where ar.id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM activity_rules
        WHERE id = #{id}
    </delete>


    <update id="updateStatus">
        UPDATE activity_rules
        SET update_time = NOW(), status = #{status}
        WHERE id = #{id}
    </update>


    <select id="findById" resultType="com.kikitrade.activity.dal.mysql.model.ActivityRules">
        SELECT *
        FROM activity_rules
        WHERE id = #{id}
    </select>


    <select id="findAll" resultType="com.kikitrade.activity.dal.mysql.model.ActivityRules">
        SELECT *
        FROM activity_rules
    </select>

</mapper>
