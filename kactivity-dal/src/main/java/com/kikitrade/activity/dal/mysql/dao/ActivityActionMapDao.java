package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityActionMap;
import com.kikitrade.framework.mybatis.BaseDao;

import java.util.List;

public interface ActivityActionMapDao extends BaseDao<ActivityActionMap, String> {

    int insert(ActivityActionMap activityActionMap);

    int deleteById(Integer activity_id);

    List<ActivityActionMap> findByActivityId(Integer activity_id);

    List<ActivityActionMap> findAll(Integer offset, Integer limit);

    int batchInsert(List<ActivityActionMap> activityActionMapList);
}
