package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Table(name = "activity_lottery_pool_config")
public class ActivityLotteryPoolConfig extends BaseModel {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    @Column(name = "lottery_id")
    private String lotteryId;

    @Column(name = "draw_id")
    private String drawId;

    @Column(name = "pool_no")
    private String poolNo;

    @Column(name = "remain_num")
    private Integer remainNum;
}