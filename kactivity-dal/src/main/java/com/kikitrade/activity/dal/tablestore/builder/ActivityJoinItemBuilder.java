package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityJoinItem;
import com.kikitrade.activity.dal.tablestore.param.ActivityBatchParam;
import com.kikitrade.framework.common.model.PageResult;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 19:34
 */
public interface ActivityJoinItemBuilder {

    boolean insert(ActivityJoinItem activityJoinItem);

    boolean update(ActivityJoinItem activityJoinItem);

    ActivityJoinItem queryById(String userName, String activityCode);
}
