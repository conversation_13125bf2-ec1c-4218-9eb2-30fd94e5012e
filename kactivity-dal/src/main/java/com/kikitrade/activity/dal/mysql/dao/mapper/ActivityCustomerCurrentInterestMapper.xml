<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityCustomerCurrentInterestDao">


    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityCustomerCurrentInterest">
        <result column="trans_date" property="transDate" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="activity_id" property="activityId" javaType="java.lang.Integer" jdbcType="BIGINT"/>
        <result column="activity_type" property="activityType" javaType="java.lang.Integer" jdbcType="BIGINT"/>
        <result column="customer_id" property="customerId" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="balance" property="balance" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result column="rate" property="rate" javaType="java.lang.Double" jdbcType="DOUBLE"/>
        <result column="currency" property="currency" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="interest" property="interest" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result column="status" property="status" javaType="java.lang.Integer" jdbcType="SMALLINT"/>
    </resultMap>


    <select id="findAll" resultMap="BaseResultMap">
       SELECT *
        FROM activity_customer_current_interest
        WHERE trans_date=#{transDate} order by customer_id
        LIMIT #{offset}, #{limit}
    </select>


    <update id="updateStatus">
        UPDATE activity_customer_current_interest
        SET  status = #{status}
        WHERE trans_date= #{transDate} and customer_id= #{customerId}
         and currency = #{currency} and status=0
    </update>

    <select id="countByStatus" resultType="java.lang.Long">
       SELECT COUNT(*)
        FROM activity_customer_current_interest
        WHERE trans_date= #{transDate} and status = #{status}
    </select>

</mapper>
