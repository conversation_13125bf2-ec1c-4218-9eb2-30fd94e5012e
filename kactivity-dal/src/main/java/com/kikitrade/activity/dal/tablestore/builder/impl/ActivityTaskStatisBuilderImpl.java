package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskStatisBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskStatis;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/23 20:51
 */
@Component
public class ActivityTaskStatisBuilderImpl extends WideColumnStoreBuilder<ActivityTaskStatis> implements ActivityTaskStatisBuilder {
    @Override
    public String getTableName() {
        return "activity_task_statis";
    }

    @Override
    public boolean insert(ActivityTaskStatis activityTaskStatis) {
        return super.putRow(activityTaskStatis, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public Long incrementProgress(ActivityTaskStatis activityTaskStatis, int incr) {
        return super.incrementAndReturn(activityTaskStatis, incr, "progress");
    }

    @Override
    public ActivityTaskStatis findDetail(ActivityTaskStatis statis) {
        return getRow(statis);
    }

    @PostConstruct
    public void init() {
        super.init(ActivityTaskStatis.class);
    }
}
