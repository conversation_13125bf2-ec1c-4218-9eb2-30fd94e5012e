package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.framework.common.model.PageResult;

public interface ActivityLotteryItemBuilder {

    String getTableName();

    boolean insert(ActivityLotteryItem activityLotteryItem);

    boolean update(ActivityLotteryItem activityLotteryItem);

    long countByCustomer(String customerId);

    long countByCustomer(String customerId, String code, Long startTime, Long endTime);

    long cumulateCountByCustomer(String customerId, String code);

    PageResult findByCustomer(String customerId, int offset, int limit);

    ActivityLotteryItem findById(String customerId, String id);
}
