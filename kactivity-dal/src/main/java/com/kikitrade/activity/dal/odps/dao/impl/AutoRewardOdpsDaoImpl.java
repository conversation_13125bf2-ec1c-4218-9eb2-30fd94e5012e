package com.kikitrade.activity.dal.odps.dao.impl;

import com.aliyun.odps.Instance;
import com.kikitrade.activity.dal.odps.dao.AutoRewardOdpsDao;
import com.kikitrade.frameworks.odps.OdpsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.StringWriter;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-08 10:00
 */
@Component
@Slf4j
public class AutoRewardOdpsDaoImpl implements AutoRewardOdpsDao {

    @Resource
    private OdpsTemplate odpsTemplate;

    @Override
    public Instance execute(String templateCode, String sql, Map<String, Object> param) {
        try{
            log.info("fsql:{},{},{}", templateCode, sql, param);
            String fsql = parseSql(templateCode, sql, param);
            if(fsql == null){
                return null;
            }
            log.info("fsql:{}", fsql);
            Instance execute = odpsTemplate.execute(fsql);
            log.info("fsql result:{}", execute);
            return execute;
        }catch (Exception ex){
            log.error("fsql error", ex);
            return null;
        }
    }

    private String parseSql(String templateCode, String sql, Map<String, Object> map){
        VelocityContext velocityContext = new VelocityContext();
        for(Map.Entry<String, Object> entity : map.entrySet()){
            velocityContext.put(entity.getKey(), entity.getValue());
        }
        return renderer(velocityContext, templateCode, sql);
    }

    /**
     * 渲染
     * @param context
     * @param tag
     * @return
     */
    private String renderer(VelocityContext context, String tag ,String sql){
        StringWriter writer = new StringWriter();
        Velocity.evaluate(context, writer, tag, sql);
        int i = 0;
        while (writer.toString().contains("${") && i++ < 3){
            sql = writer.toString();
            writer = new StringWriter();
            Velocity.evaluate(context, writer, tag, sql);
        }
        if(writer.toString().contains("${")){
            log.error("sql解析失败：{}", writer.toString());
            return null;
        }
        return writer.toString();
    }
}
