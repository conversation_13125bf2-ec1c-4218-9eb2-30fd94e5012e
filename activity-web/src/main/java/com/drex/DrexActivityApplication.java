package com.drex;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @date 2025/4/22 14:24
 * @description: ${description}
 */
@EnableDubbo(scanBasePackages = {"com.drex.activity"})
@SpringBootConfiguration
@SpringBootApplication(scanBasePackages = {"com.drex"})
public class DrexActivityApplication {

    public static void main(String[] args) {
        SpringApplication.run(DrexActivityApplication.class, args);
    }

}