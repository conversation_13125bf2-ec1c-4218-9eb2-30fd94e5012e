package com.drex.web.common.interceper;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.io.PrintWriter;

@Slf4j
public class AbstractInterceptor implements HandlerInterceptor {

    public void interceptRespond(HttpServletResponse response, String jsonString) throws IOException {
        log.info("urlresult:{}", jsonString);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");

        PrintWriter printWriter = response.getWriter();

        printWriter.write(jsonString);
        printWriter.flush();
    }
}
