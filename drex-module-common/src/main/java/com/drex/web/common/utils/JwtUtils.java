package com.drex.web.common.utils;

import com.drex.web.common.JwtToken;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;

import java.util.Base64;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
public class JwtUtils {
    // 假设的密钥，实际使用中应妥善保管
    private static final String SECRET_KEY = "W1H1TduSGA1xsZwduKS09a6YNmZUz6IIo/+1so99fOHlHnlzgT36/caDPmUa6sr9AJlMxGXAGZ8CXUbXam+T8Q==";

    /**
     * 生成 accessToken 和 refreshToken 对
     *
     * @param accessSubject 令牌的主题，可以是用户ID等信息
     * @return 包含 accessToken 和 refreshToken 的 Map
     */
    public static JwtToken generateTokenPair(String accessSubject, String refreshSubject) {
        // 生成 accessToken，1天过期
        String accessToken = generateToken(accessSubject, TimeUnit.DAYS.toMillis(1));
        // 生成 refreshToken，90天过期
        String refreshToken = generateToken(refreshSubject, TimeUnit.DAYS.toMillis(90));
        return JwtToken.builder().accessToken(accessToken).refreshToken(refreshToken).build();
    }

    /**
     * 验证 accessToken 是否有效
     *
     * @param accessToken 待验证的 accessToken
     * @return 如果 token 有效返回 true，否则返回 false
     */
    public static Jws<Claims> getAccessToken(String accessToken) {
        try {
            log.info("Access Token: {}", accessToken);
            return Jwts.parser().setSigningKey(Base64.getDecoder().decode(SECRET_KEY)).parseClaimsJws(accessToken);
        } catch (ExpiredJwtException e) {
            log.error("Access Token Expired:{}", e.getMessage(), e);
        } catch (UnsupportedJwtException e) {
            log.error("Unsupported Access Token:{} ",e.getMessage(), e);
        } catch (MalformedJwtException e) {
            log.error("Malformed Access Token 格式:{}" ,e.getMessage(), e);
        } catch (SignatureException e) {
            log.error("Signature Access Token 签名:{}", e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            log.error("Access Token 为空:{}", e.getMessage(), e);
        }
        return null;
    }


    /**
     * 生成单个 JWT 令牌
     *
     * @param subject        令牌的主题
     * @param expirationTime 令牌的过期时间（毫秒）
     * @return 生成的 JWT 令牌
     */
    private static String generateToken(String subject, long expirationTime) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + expirationTime);

        return Jwts.builder()
                .setSubject(subject)
                .setIssuedAt(now)
                .setIssuer("trex")
                .setExpiration(expiration)
                .signWith(SignatureAlgorithm.HS512, SECRET_KEY)
                .compact();
    }

    public static void main(String[] args) {
        byte[] SECRET_KEY = Keys.secretKeyFor(SignatureAlgorithm.HS512).getEncoded();
        System.out.println(Base64.getEncoder().encodeToString(SECRET_KEY));
        String token = generateToken("1920337232644935680", 10);
        String token2 = generateToken("1920337232644935680", 100000000);
        System.out.println(token);
        System.out.println(token2);
    }
}
