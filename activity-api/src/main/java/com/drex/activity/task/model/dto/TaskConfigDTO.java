package com.drex.activity.task.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12 17:19
 * @description:
 */
@Data
public class TaskConfigDTO implements Serializable {

    private String taskId;

    private String appId;

    private String code;

    private String title;

    private String desc;

    private String url;

    private String connectUrl;

    private String status;

    private Integer btn;

    private Map<String, Object> link;

    private Map<String, Integer> limitMap;
    /**
     * 奖品
     */
    private Map<String, List<Award>> reward;

    private Integer rewardFrequency;

    private String icon;

    /**
     * 任务开始时间
     */
    private Long startTime;
    /**
     * 任务结束时间
     */
    private Long endTime;

    private String cycle;

    private String domain;

    private String ledgerTitle;

    private String attr;

    private Boolean showList;

    private Boolean showProgress;

    private Integer order;

}
