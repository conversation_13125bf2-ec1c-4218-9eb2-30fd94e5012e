package com.drex.activity.task.api;

import com.drex.activity.task.model.Token;
import com.drex.activity.task.model.request.TaskListRequest;
import com.drex.activity.task.model.response.TaskConfigResponse;
import com.drex.activity.task.model.response.VerifyResponse;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22 14:27
 * @description:
 */
public interface RemoteTaskService {

    Response<List<TaskConfigResponse>> taskList(TaskListRequest request);

    Response<TaskConfigResponse> getTaskByCode(String appId, String taskCode, String customerId);

    Response<VerifyResponse> verify(Token accessToken, String taskId, String appId);
}
