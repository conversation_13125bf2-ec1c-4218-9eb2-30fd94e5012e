package com.drex.activity.task.model;

public enum ActivityResponseCode {
    SUCCESS("0000", "success", true),

    INVALID_PARAMETER("30001", "invalid.parameter"),

    TASK_RETRY("30002", "task.retry"),
    TASK_EXPIRED("30003", "task.expired"),
    TASK_NOT_FOUND("30004", "task not found"),
    ACCESS_TOKEN_EXPIRE("30005", "access token expire"),


    BASKET_INSUFFICIENT_QUOTA("30006", "basket insufficient quota"),

    //系统错误
    SYSTEM_ERROR("9999", "system.error"),
    ;

    private final String msg;
    private final String code;
    private final boolean success;

    ActivityResponseCode(String code, String msg, boolean success) {
        this.msg = msg;
        this.code = code;
        this.success = success;
    }

    ActivityResponseCode(String code, String msg) {
        this.msg = msg;
        this.code = code;
        this.success = false;
    }

    public String getMsg() {
        return msg;
    }

    public String getCode() {
        return code;
    }

    public boolean isSuccess() {
        return success;
    }
}