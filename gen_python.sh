#!/usr/bin/env bash
TMP_PATH="operation/facade"
rm -rf operation
mkdir -p $TMP_PATH

cp *.proto $TMP_PATH
cp ./p2p/*.proto $TMP_PATH
files=$(ls $TMP_PATH)
SOURCES=""
TARGET=Base.proto
REPLACEMENT=$TMP_PATH/Base.proto
for f in $files
do
   if [ "$f" != "Base.proto" ]; then
       os=$(uname -a)
       if [[ $os == *"Darwin"* ]]; then
          sed -i "" "s|$TARGET|$REPLACEMENT|g" "$TMP_PATH/$f"
       else
         sed -i "s|$TARGET|$REPLACEMENT|g" "$TMP_PATH/$f"
       fi

   fi
   SOURCES+="$TMP_PATH/$f "
done
python -m grpc_tools.protoc --proto_path=./ --python_out=../../ --grpc_python_out=../../ $SOURCES
echo "delete tmp files"
rm -rf operation
echo "done"