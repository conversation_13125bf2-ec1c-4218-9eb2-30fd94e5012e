openapi: 3.0.1
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: MemberV3
    description: 会员相关接口描述
servers:
  - description: Generated server url
    url: http://api.quests.dev.dipbit.xyz
paths:
  /v3/member/bread/ladder:
    get:
      summary: 积分天梯排行榜
      description: 获取积分排行榜
      operationId: globalLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLadder'
      parameters:
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - name: season
          in: query
          description: ''
          required: true
          schema:
            type: string
            default: season0
        - schema:
            type: integer
            format: int32
          in: query
          name: level
          description: 徽章等级
          required: false
        - schema:
            default: 30
            format: int32
            type: integer
          in: query
          name: limit
          description: 排行榜数量,范围：[0,200]
          required: false
      tags:
        - MemberV3

  /v3/member/bread/ladder/history:
    get:
      summary: 积分历史排行榜
      description: 获取积分历史排行榜
      operationId: historyLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLadder'
      parameters:
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - name: season
          in: query
          description: 查询赛季（不传默认为当前赛季）
          required: true
          schema:
            type: string
            default: season0
        - name: cycle
          in: query
          description: 查询赛季周数（必传）
          required: true
          schema:
            type: string
        - schema:
            type: integer
            format: int32
          in: query
          name: level
          description: 徽章等级
          required: false
        - schema:
            default: 30
            format: int32
            type: integer
          in: query
          name: limit
          description: 排行榜数量,范围：[0,200]
          required: false
      tags:
        - MemberV3
  /v3/member/points/ledgers/{type}:
    get:
      summary: 积分流水
      description: 积分流水-已领取|已使用
      operationId: pointLedgersV3
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultPointLedgerList'
      parameters:
        - schema:
            type: string
            enum:
              - transfer_in
              - transfer_out
              - all
          in: path
          name: type
          description: 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分,all：查询全部流水信息)
          required: true
        - schema:
            default: 0
            format: int32
            type: integer
          in: query
          name: offset
          description: 分页offset，默认0
          required: true
        - schema:
            default: 10
            format: int32
            type: integer
          in: query
          name: limit
          description: 分页limit,默认10
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
        - schema:
            type: array
            items:
              type: string
          in: query
          name: businessTypes
          description: 业务类型
          required: false
        - schema:
            type: string
          in: query
          name: assetType
          description: 资产类型 默认POINT
          required: false
      tags:
        - MemberV3
  /v3/member/points/daily:
    get:
      summary: 每日积分
      description: 每日积分
      operationId: pointDaily
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultPointStaticList'
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
        - schema:
            type: string
          in: header
          name: idToken
          description: idToken
          required: true
        - schema:
            type: string
          in: query
          name: handleName
          required: false
          description: osp handleName
        - schema:
            type: string
          in: query
          name: appId
          required: false
          description: osp appId
        - schema:
            type: string
          in: query
          name: chainId
          required: false
          description: osp chainId
      tags:
        - MemberV3
  /v3/member/top/ladder:
    get:
      summary: 积分天梯top榜
      description: 积分天梯top榜
      operationId: topLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTopLadder'
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
        - schema:
            type: string
          in: header
          name: idToken
          description: idToken
          required: false
        - schema:
            type: string
          in: query
          name: handleName
          required: false
          description: osp handleName
        - schema:
            type: string
          in: query
          name: appId
          required: false
          description: osp appId
        - schema:
            type: string
          in: query
          name: chainId
          required: false
          description: osp chainId
        - schema:
            default: POINT
            type: string
          in: query
          name: assetType
          required: false
          description: 资产类型 默认 POINT
        - schema:
            default: 100
            format: int32
            type: integer
          in: query
          name: limit
          required: false
        - schema:
            default: unfold
            type: string
          in: query
          name: strategy
          required: false
          description: 排序策略，fold：相同分数排名相同， unfold(默认)：相同分数排名不同
      tags:
        - MemberV3
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Date:
      type: object
    WebResultTopLadder:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/LadderVO'
    LadderVO:
      properties:
        myLadder:
          $ref: '#/components/schemas/topLadder'
        ladders:
          type: array
          items:
            $ref: '#/components/schemas/topLadder'
    WebResultPointStaticList:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/PointStaticList'
    PointStaticList:
      type: object
      properties:
        todayReceived:
          description: 今日获得积分
          type: number
          default: 0
          example: 100
        history:
          description: 积分每日数据
          type: array
          items:
            $ref: '#/components/schemas/PointsDailyLedger'
    WebResultLadder:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/Ladder'
    topLadder:
      description: 排行榜数据
      type: object
      properties:
        customerId:

          description: 用户ID
          type: string
          example: '10023123123221111'
        rank:
          format: int32
          description: 排名
          type: integer
          example: 1
        point:
          description: 积分数量
          type: number
          example: 100
        level:
          description: 等级
          type: string
          x-field-extra-annotation: "@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)"
          example: 10
    Ladder:
      type: object
      required:
        - type
      properties:
        startTime:
          type: integer
          format: int64
          x-field-extra-annotation: "@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)"
        endTime:
          type: integer
          format: int64
        calculating:
          type: boolean
        cycle:
          type: integer
        myLadder:
          $ref: '#/components/schemas/LadderItem'
        globalLadder:
          description: 排行榜数据
          type: array
          items:
            $ref: '#/components/schemas/LadderItem'
    LadderItem:
      description: 排行榜数据
      type: object
      properties:
        level:
          description: 徽章等级
          type: integer
          example: 1 ～ 11
        customerId:
          description: 用户ID
          type: string
          example: '10023123123221111'
        nickName:
          type: string
        icon:
          type: string
        rank:
          format: int32
          description: 排名
          type: integer
          example: 1
        point:
          description: 积分数量
          type: number
          example: 100
    WebResultPointLedgerList:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/PointLedgerList'
    PointLedgerList:
      type: object
      required:
        - customerId
        - point
      properties:
        customerId:
          description: 用户id
          type: string
          example: '10023123123221111'
        point:
          description: 会员拥有的积分总数量
          type: number
          example: 100
        available:
          description: 加成后的积分
          type: number
          example: 100
        originalAvailable:
          description: 原始积分
          type: number
          example: 100
        todayPoint:
          description: 今日获取的积分
          type: number
          example: 100
        appPoint:
          description: 平台总积分
          type: number
          example: 100
        rank:
          description: 积分百分比排行
          type: string
          example: 10%
        rankValue:
          description: 积分实际排行
          type: string
          example: 10
        pointLedgers:
          description: 积分流水数据
          type: array
          items:
            $ref: '#/components/schemas/PointLedger'
    PointLedger:
      description: 积分流水数据
      type: object
      properties:
        amount:
          description: 积分流水ID
          type: number
          example: 90
        descI18n:
          description: 流水描述 ｜ ***国际化***
          type: string
        created:
          type: integer
          format: int64
        operateType:
          format: int32
          description: 操作类型 (4)转入 (5)转出
          type: integer
          example: 4
        id:
          description: 积分流水ID
          type: string
          example: '2020023232'
        businessType:
          type: string
          example: Post
        menu:
          description: menu
          type: string
    PointsDailyLedger:
      description: 积分日流水
      type: object
      properties:
        date:
          description: 数据发生日期
          type: integer
          format: int64
        increment:
          description: 当日增量
          type: number
          example: 100
