openapi: 3.0.1
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: MemberV2
    description: 会员相关接口描述
servers:
  - description: Generated server url
    url: http://api.quests.dev.dipbit.xyz
paths:
  /v2/member/asset/{type}:
    get:
      summary: 查询用户某个资产
      description: 查询用户某个资产
      operationId: memberAsset
      tags:
        - MemberV2
      parameters:
        - schema:
            type: string
          in: path
          name: type
          description: 查询用户某个资产, 传nft
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultAssetVO'
  /v2/member/{code}/expansion:
    post:
      summary: 城堡扩容
      description: 城堡扩容
      parameters:
        - schema:
            type: string
          in: path
          name: code
          description: 需要兑换黄金的业务code，本次传kingdom
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultOrderVO'
      tags:
        - MemberV2
  /v2/member/{code}/harvest:
    post:
      summary: 城堡harvest
      description: 城堡harvest
      parameters:
        - schema:
            type: string
          in: path
          name: code
          description: 需要兑换黄金的业务code，本次传kingdom
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultOrderVO'
      tags:
        - MemberV2
  /v2/member/upgrade:
    post:
      summary: 升级会员
      description: 升级会员操作，消耗对应等级的积分来获取会员身份
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
      operationId: upgrade
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultOrderVO'
      tags:
        - MemberV2
  /v2/member/order:
    get:
      summary: 查询订单信息
      description: 查询订单信息
      operationId: memberBusinessOrder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultMemberOrderVO'
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
        - schema:
            type: string
          in: query
          name: orderId
          description: orderId
          required: true
      tags:
        - MemberV2
  /v2/member/membership:
    get:
      summary: 会员明细信息
      description: 会员明细信息
      operationId: membership
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultMembershipVO'
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
      tags:
        - MemberV2
  /v2/member/points/ledgers/{type}:
    get:
      summary: 积分流水
      description: 积分流水-已领取|已使用
      operationId: pointLedgers
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultPointLedgerList'
      parameters:
        - schema:
            type: string
            enum:
              - transfer_in
              - transfer_out
          in: path
          name: type
          description: 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分)
          required: true
        - schema:
            default: 0
            format: int32
            type: integer
          in: query
          name: offset
          description: 分页offset，默认0
          required: true
        - schema:
            default: 10
            format: int32
            type: integer
          in: query
          name: limit
          description: 分页limit,默认10
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          description: saasId
          required: true
      tags:
        - MemberV2
  /v1/member/points/ledgers/{type}:
    get:
      summary: 积分流水
      description: 积分流水-已领取|已使用
      operationId: pointLedgersV1
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultPointLedgerList'
      parameters:
        - schema:
            type: string
            enum:
              - transfer_in
              - transfer_out
          in: path
          name: type
          description: 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分)
          required: true
        - schema:
            default: 0
            format: int32
            type: integer
          in: query
          name: offset
          description: 分页offset，默认0
          required: true
        - schema:
            default: 10
            format: int32
            type: integer
          in: query
          name: limit
          description: 分页limit,默认10
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          description: saas_id
          required: true
      tags:
        - MemberV2
  /v2/member/ladder:
    get:
      summary: 积分天梯排行榜
      description: 获取积分排行榜
      operationId: globalLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLadder'
      parameters:
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - name: season
          in: query
          description: ''
          required: true
          schema:
            type: string
            default: season0
        - schema:
            type: integer
            format: int32
          in: query
          name: level
          description: 徽章等级
          required: false
        - schema:
            default: 30
            format: int32
            type: integer
          in: query
          name: limit
          description: 排行榜数量,范围：[0,200]
          required: false
      tags:
        - MemberV2

  /v2/member/ladder/history:
    get:
      summary: 积分历史排行榜
      description: 获取积分历史排行榜
      operationId: historyLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLadder'
      parameters:
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - name: season
          in: query
          description: 查询赛季（不传默认为当前赛季）
          required: true
          schema:
            type: string
            default: season0
        - name: cycle
          in: query
          description: 查询赛季周数（必传）
          required: true
          schema:
            type: string
        - schema:
            type: integer
            format: int32
          in: query
          name: level
          description: 徽章等级
          required: false
        - schema:
            default: 30
            format: int32
            type: integer
          in: query
          name: limit
          description: 排行榜数量,范围：[0,200]
          required: false
      tags:
        - MemberV2
  /v2/member/transferout:
    post:
      summary: /member/transferout
      description: 消耗积分
      operationId: transferOut
      tags:
        - MemberV2
      parameters:
        - name: JWT_TOKEN
          in: header
          required: true
          description: JWT_TOKEN
          schema:
            type: string
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransferOutRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Date:
      type: object
    WebResultMemberOrderVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/MemberOrderVO'
    MemberOrderVO:
      type: object
      properties:
        status:
          type: string
        amount:
          type: string
    WebResultPointLedgerList:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/PointLedgerList'
    PointLedgerList:
      type: object
      required:
        - customerId
        - point
      properties:
        customerId:
          description: 用户id
          type: string
          example: '10023123123221111'
        point:
          description: 会员拥有的积分总数量
          type: number
          example: 100
        available:
          description: 加成后的积分
          type: number
          example: 100
        originalAvailable:
          description: 原始积分
          type: number
          example: 100
        todayPoint:
          description: 今日获取的积分
          type: number
          example: 100
        appPoint:
          description: 平台总积分
          type: number
          example: 100
        rank:
          description: 积分百分比排行
          type: string
          example: 10%
        rankValue:
          description: 积分实际排行
          type: string
          example: 10
        pointLedgers:
          description: 积分流水数据
          type: array
          items:
            $ref: '#/components/schemas/PointLedger'
    PointLedger:
      description: 积分流水数据
      type: object
      properties:
        amount:
          description: 积分流水ID
          type: number
          example: 90
        descI18n:
          description: 流水描述 ｜ ***国际化***
          type: string
        created:
          type: integer
          format: int64
        operateType:
          format: int32
          description: 操作类型 (4)转入 (5)转出
          type: integer
          example: 4
        id:
          description: 积分流水ID
          type: string
          example: '2020023232'
        businessType:
          type: string
          example: Post
        menu:
          description: menu
          type: string
    WebResultLadder:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/Ladder'
    Ladder:
      type: object
      required:
        - type
      properties:
        startTime:
          type: integer
          format: int64
          x-field-extra-annotation: "@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)"
        endTime:
          type: integer
          format: int64
        calculating:
          type: boolean
        cycle:
          type: integer
        myLadder:
          $ref: '#/components/schemas/LadderItem'
        globalLadder:
          description: 排行榜数据
          type: array
          items:
            $ref: '#/components/schemas/LadderItem'
    LadderItem:
      description: 排行榜数据
      type: object
      properties:
        level:
          description: 徽章等级
          type: integer
          example: 1 ～ 11
        customerId:
          description: 用户ID
          type: string
          example: '10023123123221111'
        nickName:
          type: string
        icon:
          type: string
        rank:
          format: int32
          description: 排名
          type: integer
          example: 1
        point:
          description: 积分数量
          type: number
          example: 100
    WebResultMembershipVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/MembershipVO'
    MembershipVO:
      properties:
        level:
          type: string
          description: 我的会员等级
        sort:
          type: integer
          description: 会员等级数字形式
        upgradePoint:
          type: integer
          description: 升级需要的积分
          example: 100
        upgradeSuccessRate:
          type: integer
          description: 升级成功概率
          example: 80 == 80%
        point:
          type: number
          description: 当前积分
        maxCapacity:
          type: integer
          format: int32
        capacity:
          type: integer
          format: int32
        productivity:
          type: string
          description: 黄金生产效率，返回值默认追加了单位
        expansionUsed:
          type: integer
          format: int32
          description: 扩容积分消耗
        expansionUnit:
          type: integer
          format: int32
          description: 每次扩容增量
    WebResultAssetVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/AssetVO'
    AssetVO:
      properties:
        total:
          type: integer
          default: 资产总数，大于0，表示拥有
    WebResultOrderVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/OrderVO'
    OrderVO:
      properties:
        orderId:
          type: string
          description: 订单id

    TransferOutRequest:
      description: 积分消耗请求
      type: object
      properties:
        businessType:
          $ref: '#/components/schemas/BusinessTypeEnum'
        businessId:
          description: 业务id
          type: string
        type:
          $ref: '#/components/schemas/AssetTypeEnum'
        amount:
          description: 数量
          type: string

    AssetTypeEnum:
      description: asset type enum
      enum:
        - POINT
        - EXPERIENCE
        - TICKET
        - VOUCHER
        - BOOST
      type: string
    BusinessTypeEnum:
      description: businessType enum
      enum:
        - USER_BET
        - PLAY_GAME
        - ACTIVITY_LOTTERY
      type: string