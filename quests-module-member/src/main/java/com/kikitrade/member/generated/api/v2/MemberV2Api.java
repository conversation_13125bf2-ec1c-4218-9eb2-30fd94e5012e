/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.member.generated.api.v2;

import com.kikitrade.member.generated.model.v2.TransferOutRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.member.generated.model.v2.WebResultAssetVO;
import com.kikitrade.member.generated.model.v2.WebResultLadder;
import com.kikitrade.member.generated.model.v2.WebResultMemberOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultMembershipVO;
import com.kikitrade.member.generated.model.v2.WebResultOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultPointLedgerList;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "MemberV2", description = "会员相关接口描述")
public interface MemberV2Api {

    default MemberV2ApiDelegate getDelegate() {
        return new MemberV2ApiDelegate() {};
    }

    /**
     * GET /v2/member/ladder : 积分天梯排行榜
     * 获取积分排行榜
     *
     * @param saasId  (required)
     * @param season  (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "globalLadder",
        summary = "积分天梯排行榜",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/member/ladder",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLadder> globalLadder(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "season", description = "", required = true) @Valid @RequestParam(value = "season", required = true, defaultValue = "season0") String season,
        @Parameter(name = "level", description = "徽章等级") @Valid @RequestParam(value = "level", required = false) Integer level,
        @Parameter(name = "limit", description = "排行榜数量,范围：[0,200]") @Valid @RequestParam(value = "limit", required = false, defaultValue = "30") Integer limit
    ) {
        return getDelegate().globalLadder(saasId, season, level, limit);
    }


    /**
     * GET /v2/member/ladder/history : 积分历史排行榜
     * 获取积分历史排行榜
     *
     * @param saasId  (required)
     * @param season 查询赛季（不传默认为当前赛季） (required)
     * @param cycle 查询赛季周数（必传） (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "historyLadder",
        summary = "积分历史排行榜",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/member/ladder/history",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLadder> historyLadder(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "season", description = "查询赛季（不传默认为当前赛季）", required = true) @Valid @RequestParam(value = "season", required = true, defaultValue = "season0") String season,
        @NotNull @Parameter(name = "cycle", description = "查询赛季周数（必传）", required = true) @Valid @RequestParam(value = "cycle", required = true) String cycle,
        @Parameter(name = "level", description = "徽章等级") @Valid @RequestParam(value = "level", required = false) Integer level,
        @Parameter(name = "limit", description = "排行榜数量,范围：[0,200]") @Valid @RequestParam(value = "limit", required = false, defaultValue = "30") Integer limit
    ) {
        return getDelegate().historyLadder(saasId, season, cycle, level, limit);
    }


    /**
     * GET /v2/member/asset/{type} : 查询用户某个资产
     * 查询用户某个资产
     *
     * @param type 查询用户某个资产, 传nft (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "memberAsset",
        summary = "查询用户某个资产",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultAssetVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/member/asset/{type}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultAssetVO> memberAsset(
        @Parameter(name = "type", description = "查询用户某个资产, 传nft", required = true) @PathVariable("type") String type
    ) {
        return getDelegate().memberAsset(type);
    }


    /**
     * GET /v2/member/order : 查询订单信息
     * 查询订单信息
     *
     * @param saasId saasId (required)
     * @param orderId orderId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "memberBusinessOrder",
        summary = "查询订单信息",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultMemberOrderVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/member/order",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultMemberOrderVO> memberBusinessOrder(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "orderId", description = "orderId", required = true) @Valid @RequestParam(value = "orderId", required = true) String orderId
    ) {
        return getDelegate().memberBusinessOrder(saasId, orderId);
    }


    /**
     * GET /v2/member/membership : 会员明细信息
     * 会员明细信息
     *
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "membership",
        summary = "会员明细信息",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultMembershipVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/member/membership",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultMembershipVO> membership(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().membership(saasId);
    }


    /**
     * GET /v2/member/points/ledgers/{type} : 积分流水
     * 积分流水-已领取|已使用
     *
     * @param type 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分) (required)
     * @param offset 分页offset，默认0 (required)
     * @param limit 分页limit,默认10 (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "pointLedgers",
        summary = "积分流水",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultPointLedgerList.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/member/points/ledgers/{type}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultPointLedgerList> pointLedgers(
        @Parameter(name = "type", description = "积分操作类型(transfer_in：获得积分,transfer_out：消耗积分)", required = true) @PathVariable("type") String type,
        @NotNull @Parameter(name = "offset", description = "分页offset，默认0", required = true) @Valid @RequestParam(value = "offset", required = true, defaultValue = "0") Integer offset,
        @NotNull @Parameter(name = "limit", description = "分页limit,默认10", required = true) @Valid @RequestParam(value = "limit", required = true, defaultValue = "10") Integer limit,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().pointLedgers(type, offset, limit, saasId);
    }


    /**
     * GET /v1/member/points/ledgers/{type} : 积分流水
     * 积分流水-已领取|已使用
     *
     * @param type 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分) (required)
     * @param offset 分页offset，默认0 (required)
     * @param limit 分页limit,默认10 (required)
     * @param saasId saas_id (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "pointLedgersV1",
        summary = "积分流水",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultPointLedgerList.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v1/member/points/ledgers/{type}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultPointLedgerList> pointLedgersV1(
        @Parameter(name = "type", description = "积分操作类型(transfer_in：获得积分,transfer_out：消耗积分)", required = true) @PathVariable("type") String type,
        @NotNull @Parameter(name = "offset", description = "分页offset，默认0", required = true) @Valid @RequestParam(value = "offset", required = true, defaultValue = "0") Integer offset,
        @NotNull @Parameter(name = "limit", description = "分页limit,默认10", required = true) @Valid @RequestParam(value = "limit", required = true, defaultValue = "10") Integer limit,
        @Parameter(name = "saas_id", description = "saas_id", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().pointLedgersV1(type, offset, limit, saasId);
    }


    /**
     * POST /v2/member/transferout : /member/transferout
     * 消耗积分
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId  (required)
     * @param transferOutRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "transferOut",
        summary = "/member/transferout",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/member/transferout",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<WebResult> transferOut(
        @Parameter(name = "JWT_TOKEN", description = "JWT_TOKEN", required = true) @RequestHeader(value = "JWT_TOKEN", required = true) String JWT_TOKEN,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "TransferOutRequest", description = "") @Valid @RequestBody(required = false) TransferOutRequest transferOutRequest
    ) {
        return getDelegate().transferOut(JWT_TOKEN, saasId, transferOutRequest);
    }


    /**
     * POST /v2/member/upgrade : 升级会员
     * 升级会员操作，消耗对应等级的积分来获取会员身份
     *
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "upgrade",
        summary = "升级会员",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultOrderVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/member/upgrade",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultOrderVO> upgrade(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().upgrade(saasId);
    }


    /**
     * POST /v2/member/{code}/expansion : 城堡扩容
     * 城堡扩容
     *
     * @param code 需要兑换黄金的业务code，本次传kingdom (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "v2MemberCodeExpansionPost",
        summary = "城堡扩容",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultOrderVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/member/{code}/expansion",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultOrderVO> v2MemberCodeExpansionPost(
        @Parameter(name = "code", description = "需要兑换黄金的业务code，本次传kingdom", required = true) @PathVariable("code") String code,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().v2MemberCodeExpansionPost(code, saasId);
    }


    /**
     * POST /v2/member/{code}/harvest : 城堡harvest
     * 城堡harvest
     *
     * @param code 需要兑换黄金的业务code，本次传kingdom (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "v2MemberCodeHarvestPost",
        summary = "城堡harvest",
        tags = { "MemberV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultOrderVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/member/{code}/harvest",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultOrderVO> v2MemberCodeHarvestPost(
        @Parameter(name = "code", description = "需要兑换黄金的业务code，本次传kingdom", required = true) @PathVariable("code") String code,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().v2MemberCodeHarvestPost(code, saasId);
    }

}
