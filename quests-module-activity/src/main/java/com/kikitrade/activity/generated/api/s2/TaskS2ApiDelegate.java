package com.kikitrade.activity.generated.api.s2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.s2.WebResultTaskFulfillVO;
import com.kikitrade.activity.generated.model.s2.WebResultVerifyVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link TaskS2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface TaskS2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /s2/tasks/fulfill : 做任务
     * 
     *
     * @param authorization  (required)
     * @param saasId saasId (required)
     * @param cid 用户id、address (required)
     * @param name 事件名称 (required)
     * @param contentId 当前事件对应的资源id (required)
     * @param eventTime 当前事件对应的时间戳 (required)
     * @param targetUserId 当前事件对应的资源的原资源用户id (optional)
     * @param targetContentId 当前事件对应的资源的原资源id (optional)
     * @param extendAttr 当前事件附加属性 (optional)
     * @return 成功 (status code 200)
     *         or 失败 (status code 400)
     * @see TaskS2Api#fulfillTask
     */
    default ResponseEntity<WebResult> fulfillTask(String authorization,
        String saasId,
        String cid,
        String name,
        String contentId,
        Long eventTime,
        String targetUserId,
        String targetContentId,
        String extendAttr) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /s2/tasks/fulfill/status : 任务状态查询
     * 
     *
     * @param authorization  (required)
     * @param saasId saasId (required)
     * @param cid 其他平台用户ID (required)
     * @param taskId 任务id (required)
     * @return 成功 (status code 200)
     * @see TaskS2Api#s2TasksFulfillStatusGet
     */
    default ResponseEntity<WebResultTaskFulfillVO> s2TasksFulfillStatusGet(String authorization,
        String saasId,
        String cid,
        String taskId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /s2/tasks/verify : 验证用户是否完成某个操作
     * 验证用户是否完成某个操作
     *
     * @param authorization  (required)
     * @param saasId saasId (required)
     * @param cid 其他平台用户ID (required)
     * @param scene 待验证的场景，目前只支持 follow_x (required)
     * @param ext 其他额外参数，json格式 (optional)
     * @return 成功 (status code 200)
     * @see TaskS2Api#s2TasksVerifyGet
     */
    default ResponseEntity<WebResultVerifyVO> s2TasksVerifyGet(String authorization,
        String saasId,
        String cid,
        String scene,
        String ext) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
