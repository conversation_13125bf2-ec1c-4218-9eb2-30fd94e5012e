/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.activity.generated.model.v2.UserAnswerVO;
import com.kikitrade.activity.generated.model.v2.WebResultQuestionVO;
import com.kikitrade.activity.generated.model.v2.WebResultSettleVO;
import com.kikitrade.activity.generated.model.v2.WebResultUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "QuestionV2", description = "the QuestionV2 API")
public interface QuestionV2Api {

    default QuestionV2ApiDelegate getDelegate() {
        return new QuestionV2ApiDelegate() {};
    }

    /**
     * GET /v2/question/acquire : Acquire a group of questions
     * Acquire a group of questions
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "v2QuestionAcquireGet",
        summary = "Acquire a group of questions",
        tags = { "QuestionV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultQuestionVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/question/acquire",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultQuestionVO> v2QuestionAcquireGet(
        @Parameter(name = "JWT_TOKEN", description = "JWT_TOKEN", required = true) @RequestHeader(value = "JWT_TOKEN", required = true) String JWT_TOKEN,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().v2QuestionAcquireGet(JWT_TOKEN, saasId);
    }


    /**
     * GET /v2/question/me : User sets details
     * User details
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "v2QuestionMeGet",
        summary = "User sets details",
        tags = { "QuestionV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultUserVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/question/me",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultUserVO> v2QuestionMeGet(
        @Parameter(name = "JWT_TOKEN", description = "JWT_TOKEN", required = true) @RequestHeader(value = "JWT_TOKEN", required = true) String JWT_TOKEN,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().v2QuestionMeGet(JWT_TOKEN, saasId);
    }


    /**
     * POST /v2/question/submit : Submit a group of questions
     * Submit a group of questions
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId saasId (required)
     * @param userAnswerVO  (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "v2QuestionSubmitPost",
        summary = "Submit a group of questions",
        tags = { "QuestionV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultSettleVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/question/submit",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<WebResultSettleVO> v2QuestionSubmitPost(
        @Parameter(name = "JWT_TOKEN", description = "JWT_TOKEN", required = true) @RequestHeader(value = "JWT_TOKEN", required = true) String JWT_TOKEN,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "UserAnswerVO", description = "", required = true) @Valid @RequestBody UserAnswerVO userAnswerVO
    ) {
        return getDelegate().v2QuestionSubmitPost(JWT_TOKEN, saasId, userAnswerVO);
    }

}
