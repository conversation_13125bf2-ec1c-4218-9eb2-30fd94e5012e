package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultSeriesTaskVOAllOfNodes
 */

@JsonTypeName("WebResultSeriesTaskVO_allOf_nodes")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultSeriesTaskVOAllOfNodes {

  @JsonProperty("index")
  private String index;

  @JsonProperty("isComplete")
  private Boolean isComplete;

  @JsonProperty("nodeLogo")
  private Integer nodeLogo;

  public WebResultSeriesTaskVOAllOfNodes index(String index) {
    this.index = index;
    return this;
  }

  /**
   * Get index
   * @return index
  */
  
  @Schema(name = "index", required = false)
  public String getIndex() {
    return index;
  }

  public void setIndex(String index) {
    this.index = index;
  }

  public WebResultSeriesTaskVOAllOfNodes isComplete(Boolean isComplete) {
    this.isComplete = isComplete;
    return this;
  }

  /**
   * Get isComplete
   * @return isComplete
  */
  
  @Schema(name = "isComplete", required = false)
  public Boolean getIsComplete() {
    return isComplete;
  }

  public void setIsComplete(Boolean isComplete) {
    this.isComplete = isComplete;
  }

  public WebResultSeriesTaskVOAllOfNodes nodeLogo(Integer nodeLogo) {
    this.nodeLogo = nodeLogo;
    return this;
  }

  /**
   * Get nodeLogo
   * @return nodeLogo
  */
  
  @Schema(name = "nodeLogo", required = false)
  public Integer getNodeLogo() {
    return nodeLogo;
  }

  public void setNodeLogo(Integer nodeLogo) {
    this.nodeLogo = nodeLogo;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultSeriesTaskVOAllOfNodes webResultSeriesTaskVOAllOfNodes = (WebResultSeriesTaskVOAllOfNodes) o;
    return Objects.equals(this.index, webResultSeriesTaskVOAllOfNodes.index) &&
        Objects.equals(this.isComplete, webResultSeriesTaskVOAllOfNodes.isComplete) &&
        Objects.equals(this.nodeLogo, webResultSeriesTaskVOAllOfNodes.nodeLogo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(index, isComplete, nodeLogo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultSeriesTaskVOAllOfNodes {\n");
    sb.append("    index: ").append(toIndentedString(index)).append("\n");
    sb.append("    isComplete: ").append(toIndentedString(isComplete)).append("\n");
    sb.append("    nodeLogo: ").append(toIndentedString(nodeLogo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

