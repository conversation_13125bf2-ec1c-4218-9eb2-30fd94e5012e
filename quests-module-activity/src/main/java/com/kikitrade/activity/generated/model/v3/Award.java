package com.kikitrade.activity.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * Award
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Award {

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("type")
  private String type;

  @JsonProperty("amount")
  private String amount;

  @JsonProperty("currency")
  private String currency;

  @JsonProperty("index")
  private Integer index;

  @JsonProperty("status")
  private Integer status;

  public Award desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * 奖品描述
   * @return desc
  */
  
  @Schema(name = "desc", description = "奖品描述", required = false)
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public Award type(String type) {
    this.type = type;
    return this;
  }

  /**
   * NFT,TOKEN,POINT
   * @return type
  */
  
  @Schema(name = "type", description = "NFT,TOKEN,POINT", required = false)
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public Award amount(String amount) {
    this.amount = amount;
    return this;
  }

  /**
   * 奖励金额
   * @return amount
  */
  
  @Schema(name = "amount", description = "奖励金额", required = false)
  public String getAmount() {
    return amount;
  }

  public void setAmount(String amount) {
    this.amount = amount;
  }

  public Award currency(String currency) {
    this.currency = currency;
    return this;
  }

  /**
   * Get currency
   * @return currency
  */
  
  @Schema(name = "currency", required = false)
  public String getCurrency() {
    return currency;
  }

  public void setCurrency(String currency) {
    this.currency = currency;
  }

  public Award index(Integer index) {
    this.index = index;
    return this;
  }

  /**
   * Get index
   * @return index
  */
  
  @Schema(name = "index", required = false)
  public Integer getIndex() {
    return index;
  }

  public void setIndex(Integer index) {
    this.index = index;
  }

  public Award status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * 1:已发放 0：未发放
   * @return status
  */
  
  @Schema(name = "status", description = "1:已发放 0：未发放", required = false)
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Award award = (Award) o;
    return Objects.equals(this.desc, award.desc) &&
        Objects.equals(this.type, award.type) &&
        Objects.equals(this.amount, award.amount) &&
        Objects.equals(this.currency, award.currency) &&
        Objects.equals(this.index, award.index) &&
        Objects.equals(this.status, award.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(desc, type, amount, currency, index, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Award {\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    currency: ").append(toIndentedString(currency)).append("\n");
    sb.append("    index: ").append(toIndentedString(index)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

