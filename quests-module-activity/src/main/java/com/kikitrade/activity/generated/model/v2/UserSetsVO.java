package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * UserSetsVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class UserSetsVO {

  @JsonProperty("userId")
  private String userId;

  @JsonProperty("usedSets")
  private Integer usedSets;

  @JsonProperty("availableSets")
  private Integer availableSets;

  @JsonProperty("rewardRemainTime")
  private Long rewardRemainTime;

  @JsonProperty("inviteSucceedCount")
  private Integer inviteSucceedCount;

  @JsonProperty("todayCheckIn")
  private Boolean todayCheckIn;

  @JsonProperty("seriesCheckIn")
  private Boolean seriesCheckIn;

  @JsonProperty("seriesCheckInCount")
  private Integer seriesCheckInCount;

  public UserSetsVO userId(String userId) {
    this.userId = userId;
    return this;
  }

  /**
   * Get userId
   * @return userId
  */
  
  @Schema(name = "userId", required = false)
  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public UserSetsVO usedSets(Integer usedSets) {
    this.usedSets = usedSets;
    return this;
  }

  /**
   * Get usedSets
   * @return usedSets
  */
  
  @Schema(name = "usedSets", required = false)
  public Integer getUsedSets() {
    return usedSets;
  }

  public void setUsedSets(Integer usedSets) {
    this.usedSets = usedSets;
  }

  public UserSetsVO availableSets(Integer availableSets) {
    this.availableSets = availableSets;
    return this;
  }

  /**
   * Get availableSets
   * @return availableSets
  */
  
  @Schema(name = "availableSets", required = false)
  public Integer getAvailableSets() {
    return availableSets;
  }

  public void setAvailableSets(Integer availableSets) {
    this.availableSets = availableSets;
  }

  public UserSetsVO rewardRemainTime(Long rewardRemainTime) {
    this.rewardRemainTime = rewardRemainTime;
    return this;
  }

  /**
   * Get rewardRemainTime
   * @return rewardRemainTime
  */
  
  @Schema(name = "rewardRemainTime", required = false)
  public Long getRewardRemainTime() {
    return rewardRemainTime;
  }

  public void setRewardRemainTime(Long rewardRemainTime) {
    this.rewardRemainTime = rewardRemainTime;
  }

  public UserSetsVO inviteSucceedCount(Integer inviteSucceedCount) {
    this.inviteSucceedCount = inviteSucceedCount;
    return this;
  }

  /**
   * Get inviteSucceedCount
   * @return inviteSucceedCount
  */
  
  @Schema(name = "inviteSucceedCount", required = false)
  public Integer getInviteSucceedCount() {
    return inviteSucceedCount;
  }

  public void setInviteSucceedCount(Integer inviteSucceedCount) {
    this.inviteSucceedCount = inviteSucceedCount;
  }

  public UserSetsVO todayCheckIn(Boolean todayCheckIn) {
    this.todayCheckIn = todayCheckIn;
    return this;
  }

  /**
   * Get todayCheckIn
   * @return todayCheckIn
  */
  
  @Schema(name = "todayCheckIn", required = false)
  public Boolean getTodayCheckIn() {
    return todayCheckIn;
  }

  public void setTodayCheckIn(Boolean todayCheckIn) {
    this.todayCheckIn = todayCheckIn;
  }

  public UserSetsVO seriesCheckIn(Boolean seriesCheckIn) {
    this.seriesCheckIn = seriesCheckIn;
    return this;
  }

  /**
   * Get seriesCheckIn
   * @return seriesCheckIn
  */
  
  @Schema(name = "seriesCheckIn", required = false)
  public Boolean getSeriesCheckIn() {
    return seriesCheckIn;
  }

  public void setSeriesCheckIn(Boolean seriesCheckIn) {
    this.seriesCheckIn = seriesCheckIn;
  }

  public UserSetsVO seriesCheckInCount(Integer seriesCheckInCount) {
    this.seriesCheckInCount = seriesCheckInCount;
    return this;
  }

  /**
   * Get seriesCheckInCount
   * @return seriesCheckInCount
  */
  
  @Schema(name = "seriesCheckInCount", required = false)
  public Integer getSeriesCheckInCount() {
    return seriesCheckInCount;
  }

  public void setSeriesCheckInCount(Integer seriesCheckInCount) {
    this.seriesCheckInCount = seriesCheckInCount;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UserSetsVO userSetsVO = (UserSetsVO) o;
    return Objects.equals(this.userId, userSetsVO.userId) &&
        Objects.equals(this.usedSets, userSetsVO.usedSets) &&
        Objects.equals(this.availableSets, userSetsVO.availableSets) &&
        Objects.equals(this.rewardRemainTime, userSetsVO.rewardRemainTime) &&
        Objects.equals(this.inviteSucceedCount, userSetsVO.inviteSucceedCount) &&
        Objects.equals(this.todayCheckIn, userSetsVO.todayCheckIn) &&
        Objects.equals(this.seriesCheckIn, userSetsVO.seriesCheckIn) &&
        Objects.equals(this.seriesCheckInCount, userSetsVO.seriesCheckInCount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(userId, usedSets, availableSets, rewardRemainTime, inviteSucceedCount, todayCheckIn, seriesCheckIn, seriesCheckInCount);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UserSetsVO {\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    usedSets: ").append(toIndentedString(usedSets)).append("\n");
    sb.append("    availableSets: ").append(toIndentedString(availableSets)).append("\n");
    sb.append("    rewardRemainTime: ").append(toIndentedString(rewardRemainTime)).append("\n");
    sb.append("    inviteSucceedCount: ").append(toIndentedString(inviteSucceedCount)).append("\n");
    sb.append("    todayCheckIn: ").append(toIndentedString(todayCheckIn)).append("\n");
    sb.append("    seriesCheckIn: ").append(toIndentedString(seriesCheckIn)).append("\n");
    sb.append("    seriesCheckInCount: ").append(toIndentedString(seriesCheckInCount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

