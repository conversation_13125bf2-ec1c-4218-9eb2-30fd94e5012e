package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import java.util.Map;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * GoodsDetailVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class GoodsDetailVO {

  @JsonProperty("goodsId")
  private String goodsId;

  @JsonProperty("outId")
  private String outId;

  @JsonProperty("name")
  private String name;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("type")
  private String type;

  @JsonProperty("blockchain")
  private String blockchain;

  @JsonProperty("price")
  private BigDecimal price;

  @JsonProperty("currency")
  private String currency;

  @JsonProperty("currencyType")
  private String currencyType;

  @JsonProperty("stock")
  private Integer stock;

  @JsonProperty("startTime")
  private Long startTime;

  @JsonProperty("endTime")
  private Long endTime;

  @JsonProperty("image")
  private Map image;

  @JsonProperty("labelName")
  private String labelName;

  @JsonProperty("labelColor")
  private String labelColor;

  @JsonProperty("status")
  private Integer status;

  @JsonProperty("param")
  private String param;

  public GoodsDetailVO goodsId(String goodsId) {
    this.goodsId = goodsId;
    return this;
  }

  /**
   * Get goodsId
   * @return goodsId
  */
  
  @Schema(name = "goodsId", required = false)
  public String getGoodsId() {
    return goodsId;
  }

  public void setGoodsId(String goodsId) {
    this.goodsId = goodsId;
  }

  public GoodsDetailVO outId(String outId) {
    this.outId = outId;
    return this;
  }

  /**
   * Get outId
   * @return outId
  */
  
  @Schema(name = "outId", required = false)
  public String getOutId() {
    return outId;
  }

  public void setOutId(String outId) {
    this.outId = outId;
  }

  public GoodsDetailVO name(String name) {
    this.name = name;
    return this;
  }

  /**
   * Get name
   * @return name
  */
  
  @Schema(name = "name", required = false)
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public GoodsDetailVO desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
  
  @Schema(name = "desc", required = false)
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public GoodsDetailVO type(String type) {
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
  */
  
  @Schema(name = "type", required = false)
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public GoodsDetailVO blockchain(String blockchain) {
    this.blockchain = blockchain;
    return this;
  }

  /**
   * Get blockchain
   * @return blockchain
  */
  
  @Schema(name = "blockchain", required = false)
  public String getBlockchain() {
    return blockchain;
  }

  public void setBlockchain(String blockchain) {
    this.blockchain = blockchain;
  }

  public GoodsDetailVO price(BigDecimal price) {
    this.price = price;
    return this;
  }

  /**
   * Get price
   * @return price
  */
  @Valid 
  @Schema(name = "price", required = false)
  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }

  public GoodsDetailVO currency(String currency) {
    this.currency = currency;
    return this;
  }

  /**
   * Get currency
   * @return currency
  */
  
  @Schema(name = "currency", required = false)
  public String getCurrency() {
    return currency;
  }

  public void setCurrency(String currency) {
    this.currency = currency;
  }

  public GoodsDetailVO currencyType(String currencyType) {
    this.currencyType = currencyType;
    return this;
  }

  /**
   * Get currencyType
   * @return currencyType
  */
  
  @Schema(name = "currencyType", required = false)
  public String getCurrencyType() {
    return currencyType;
  }

  public void setCurrencyType(String currencyType) {
    this.currencyType = currencyType;
  }

  public GoodsDetailVO stock(Integer stock) {
    this.stock = stock;
    return this;
  }

  /**
   * Get stock
   * @return stock
  */
  
  @Schema(name = "stock", required = false)
  public Integer getStock() {
    return stock;
  }

  public void setStock(Integer stock) {
    this.stock = stock;
  }

  public GoodsDetailVO startTime(Long startTime) {
    this.startTime = startTime;
    return this;
  }

  /**
   * Get startTime
   * @return startTime
  */
  
  @Schema(name = "startTime", required = false)
  public Long getStartTime() {
    return startTime;
  }

  public void setStartTime(Long startTime) {
    this.startTime = startTime;
  }

  public GoodsDetailVO endTime(Long endTime) {
    this.endTime = endTime;
    return this;
  }

  /**
   * Get endTime
   * @return endTime
  */
  
  @Schema(name = "endTime", required = false)
  public Long getEndTime() {
    return endTime;
  }

  public void setEndTime(Long endTime) {
    this.endTime = endTime;
  }

  public GoodsDetailVO image(Map image) {
    this.image = image;
    return this;
  }

  /**
   * Get image
   * @return image
  */
  @Valid 
  @Schema(name = "image", required = false)
  public Map getImage() {
    return image;
  }

  public void setImage(Map image) {
    this.image = image;
  }

  public GoodsDetailVO labelName(String labelName) {
    this.labelName = labelName;
    return this;
  }

  /**
   * Get labelName
   * @return labelName
  */
  
  @Schema(name = "labelName", required = false)
  public String getLabelName() {
    return labelName;
  }

  public void setLabelName(String labelName) {
    this.labelName = labelName;
  }

  public GoodsDetailVO labelColor(String labelColor) {
    this.labelColor = labelColor;
    return this;
  }

  /**
   * Get labelColor
   * @return labelColor
  */
  
  @Schema(name = "labelColor", required = false)
  public String getLabelColor() {
    return labelColor;
  }

  public void setLabelColor(String labelColor) {
    this.labelColor = labelColor;
  }

  public GoodsDetailVO status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  
  @Schema(name = "status", required = false)
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public GoodsDetailVO param(String param) {
    this.param = param;
    return this;
  }

  /**
   * Get param
   * @return param
  */
  
  @Schema(name = "param", required = false)
  public String getParam() {
    return param;
  }

  public void setParam(String param) {
    this.param = param;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    GoodsDetailVO goodsDetailVO = (GoodsDetailVO) o;
    return Objects.equals(this.goodsId, goodsDetailVO.goodsId) &&
        Objects.equals(this.outId, goodsDetailVO.outId) &&
        Objects.equals(this.name, goodsDetailVO.name) &&
        Objects.equals(this.desc, goodsDetailVO.desc) &&
        Objects.equals(this.type, goodsDetailVO.type) &&
        Objects.equals(this.blockchain, goodsDetailVO.blockchain) &&
        Objects.equals(this.price, goodsDetailVO.price) &&
        Objects.equals(this.currency, goodsDetailVO.currency) &&
        Objects.equals(this.currencyType, goodsDetailVO.currencyType) &&
        Objects.equals(this.stock, goodsDetailVO.stock) &&
        Objects.equals(this.startTime, goodsDetailVO.startTime) &&
        Objects.equals(this.endTime, goodsDetailVO.endTime) &&
        Objects.equals(this.image, goodsDetailVO.image) &&
        Objects.equals(this.labelName, goodsDetailVO.labelName) &&
        Objects.equals(this.labelColor, goodsDetailVO.labelColor) &&
        Objects.equals(this.status, goodsDetailVO.status) &&
        Objects.equals(this.param, goodsDetailVO.param);
  }

  @Override
  public int hashCode() {
    return Objects.hash(goodsId, outId, name, desc, type, blockchain, price, currency, currencyType, stock, startTime, endTime, image, labelName, labelColor, status, param);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GoodsDetailVO {\n");
    sb.append("    goodsId: ").append(toIndentedString(goodsId)).append("\n");
    sb.append("    outId: ").append(toIndentedString(outId)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    blockchain: ").append(toIndentedString(blockchain)).append("\n");
    sb.append("    price: ").append(toIndentedString(price)).append("\n");
    sb.append("    currency: ").append(toIndentedString(currency)).append("\n");
    sb.append("    currencyType: ").append(toIndentedString(currencyType)).append("\n");
    sb.append("    stock: ").append(toIndentedString(stock)).append("\n");
    sb.append("    startTime: ").append(toIndentedString(startTime)).append("\n");
    sb.append("    endTime: ").append(toIndentedString(endTime)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    labelName: ").append(toIndentedString(labelName)).append("\n");
    sb.append("    labelColor: ").append(toIndentedString(labelColor)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    param: ").append(toIndentedString(param)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

