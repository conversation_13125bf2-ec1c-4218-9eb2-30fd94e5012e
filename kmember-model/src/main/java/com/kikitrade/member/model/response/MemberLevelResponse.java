package com.kikitrade.member.model.response;

import com.kikitrade.member.model.EquityDTO;
import com.kikitrade.member.model.MemberLevelDTO;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * vip等级的详情（包括权益、前置等配置）
 * <AUTHOR>
 * @create 2022/7/28 9:37 下午
 * @modify
 */
@Getter
@Builder
public class MemberLevelResponse implements Serializable {
    private ResponseCode code;
    private List<MemberLevelDTO> levels;
    private List<EquityDTO> equities;
}
