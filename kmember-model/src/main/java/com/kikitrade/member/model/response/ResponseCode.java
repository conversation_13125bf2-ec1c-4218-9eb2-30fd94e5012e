package com.kikitrade.member.model.response;

/**
 * MemberResponseCode
 *
 * <AUTHOR>
 * @create 2022/3/9 11:53 上午
 * @modify
 */
public enum ResponseCode {

    SUCCESS("0000", "success", true),

    INVALID_PARAMETER("4001", "invalid.parameter"),
    CUSTOMER_EMPTY("4002", "customer.empty"),
    CUSTOMER_INACTIVE("4003", "customer.inactive"),
    ASSET_ERROR("4004", "asset.error"),
    REWARD_FAIL("4005", "reward.fail"),
    PARAM_CONVERT_FAIL("4006", "param.convert.fail"),
    ALREADY_EXIST("4007", "already.exist"),
    NOT_EXIST("4008", "not.exist"),
    PROCESS_FAIL("4009", "process.fail"),
    ILLEGAL_STATE("4010", "illegal.state"),
    INSUFFICIENT_BALANCE("4011", "insufficient.balance"),
    LEVEL_NOT_EXIST("4012", "level.not.exist"),
    MEMBER_STILL_PROCESSING("4013", "member.still.processing"),
    CUSTOMER_UPDATE_ERROR("4014", "customer.update.error"),
    MEMBERSHIP_AUTO_RENEWAL_UPDATE_FAIL("4015", "membership.auto.renewal.update.fail"),
    QUESTS_LADDER_CALCULATING("4016", "quests.ladder.calculating"),
    NEXT_LEVEL_NOT_EXIST("4017", "next.level.not.exist"),
    LEVEL_CONDITION_NOT_EXIST("4018", "level.condition.not.exist"),
    LEVEL_CONFIG_ERROR("4019", "level.config.error"),
    CUSTOMER_ALREADY_UPGRADE("4020", "customer.already.upgrade.to.this.level", true), // 用户已经升级到指定的等级

    CONFIG_LOAD_ERROR("9998", "config.load.error", false),  // nacos配置文件加载错误
    SYSTEM_ERROR("9999", "system.error", false),
    ;

    private String key;

    private String code;

    private boolean success;

    /**
     * Full Parameter Constructor
     *
     * @param code
     * @param key
     * @param success
     */
    ResponseCode(String code, String key, boolean success) {
        this.key = key;
        this.code = code;
        this.success = success;
    }

    /**
     * Default success as FALSE
     *
     * @param code
     * @param key
     */
    ResponseCode(String code, String key) {
        this.key = key;
        this.code = code;
        this.success = false;
    }

    public String getKey() {
        return key;
    }

    public String getCode() {
        return code;
    }

    public boolean isSuccess() {
        return success;
    }

}
