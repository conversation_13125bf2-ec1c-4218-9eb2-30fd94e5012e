package com.kikitrade.member.model;


import com.kikitrade.asset.model.AssetLedgerReadonlyDTO;
import com.kikitrade.member.model.constant.MemberConstant;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@SuperBuilder(toBuilder = true)
public class MembershipDetailDTO implements Serializable {
    protected String customerId;
    protected String level;
    protected MemberConstant.MembershipStatus status;
    protected Date firstTime;
    protected Date effectTime;
    protected Date expireTime;
    protected Date notifyTime;
    protected Boolean autoRenewal;
    protected Date created;
    protected Date modified;
    protected String saasId;

    //customer-kiki数据
    protected String phone;
    protected String email;
    protected String username;

    //asset数据
    protected String assetId;
    protected BigDecimal point;
    protected BigDecimal receivePoint;

    protected boolean torrent;

    //积分流水
    protected List<AssetLedgerReadonlyDTO> assetLedgers;
}
