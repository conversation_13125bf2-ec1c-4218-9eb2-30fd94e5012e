package com.kikitrade.member.model;

import com.kikitrade.member.model.constant.EquityConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 权益默认配置
 * <AUTHOR>
 * @create 2024/6/7 9:03 PM
 * @modify
 */
@Data
public class EquityDefaultConfig implements Serializable {
    private String defaultIcon;
    private String defaultHyperLink;
    private String saasId;

    // 状态，大小写兼容
    private String status = EquityConstant.EquityStatus.Active.name();

    // 是否可见
    private Map<String, Boolean> visibles;
}
