package com.kikitrade.asset.model.constant;

import lombok.Getter;

/**
 * AssetBusinessType Enum
 *
 * <AUTHOR>
 * @create 2022/7/26 9:55 上午
 * @modify
 */
@Getter
public enum AssetBusinessType {

    ACTIVITY_TASK(1, "activity_task","activity_task"), // 活动任务
    MEMBER_PURCHASE(2, "member_purchase","Member Purchase"), // 购买会员
    ACTIVITY_LOTTERY(3, "activity_lottery","activity_lottery"), // 活动抽奖
    SHOP(4, "shop","shop"), //购物
    LIKE(5, "like","Like"),
    POST(6, "post","Post"),
    SIGN_IN(7, "sign_in", "Sign In"),
    COMMENT(8, "comment", "Comment"),
    LIKED(9, "liked","Receive Like"),
    MEGAPHONE(10, "megaphone", "Post Megaphone"),
    FOLLOW(11, "follow", "Follow"),
    JOIN_TRIBE(12, "join_tribe", "Join Tribe"),
    WALLET_BIND(13, "wallet_bind", "Bind Wallet"),
    WALLET_DEPOSIT(14, "wallet_deposit", "Wallet Deposit"),
    SUBJECT_POST_X(15, "subject_post_x", "Post in X with Subject"),
    FOLLOW_X(16,"follow_x", "follow X"),
    REPLY_POST_X(17, "reply_post_x", "reply post X with subject"),
    NAME_X(18, "name_x", "Change X name"),
    WALLET_ASSET(19, "wallet_asset", "AA Wallet Asset"),
    VAMPIRE_ATTACK(20, "vampire_attack", "vampire_attack"),
    MAKE_WISH(21, "make_wish", "make_wish"),
    CHOOSE_WINNER(22, "choose_winner", "choose_winner"),
    APPLY_WISH(23, "apply_wish", "apply_wish"),
    APPLY_WISH_WITH_CODE(24, "apply_wish_with_code", "apply_wish_with_code"),
    REFER_APPLY(25, "refer_apply", "refer_apply"),
    WIN_OFFER(26, "win_offer", "win_offer"),
    REGISTRATION(27, "registration", "registration"),
    INVITED_REGISTER(28, "invited_register", "invited_register"),
    SEASON_SETTLEMENT(29, "season_settlement", "season settlement"),
    BLACK_SETTLEMENT(30, "black_settlement", "black_settlement"),
    PRODUCTION_SETTLEMENT(31, "production_settlement", "production_settlement"),
    REWARD(32, "reward", "reward"),
    INVITE_CODE_VERIFICATION_APP(33, "invite_code_verification_app", "invite_code_verification_app"),
    INVITE_CODE_REGIST_APP(34, "invite_code_regist_app", "invite_code_regist_app"),
    UNLOCK_WISH(35, "unlock_wish", "unlock_wish"),
    TRADE_WISH(36, "trade_wish", "trade_wish"),
    X_AUTHORIZE(37, "x_authorize", "x_authorize"),
    BADGE(38, "badge", "badge"),
    INVITE_MINT_NFT(39, "invite_mint_nft", "invite_mint_nft"),
    JOIN_DC(40, "join_dc", "join_dc"),
    INVITE_CODE_VERIFICATION(41, "invite_code_verification", "invite_code_verification"),
    MINT_NFT(42, "mint_nft", "mint_nft"),
    MANUAL_REWARD(43, "manual_reward", "manual_reward"),
    CONNECT_X(44, "connect_x", "connect_x"),
    CONNECT_DC(45, "connect_dc", "connect_dc"),
    LIKE_POST_X(46, "like_post_x", "like_post_x"),
    PURCHASE_MEMBERSHIP(47, "purchase_membership", "purchase_membership"),
    INVITED_PURCHASE_MEMBERSHIP(48, "invited_purchase_membership", "invited_purchase_membership"),
    TRIBE_JOIN_MEMBER(49, "tribe_join_member", "tribe_join_member"),
    CREATED_TRIBE(50, "created_tribe", "created_tribe"),
    COMPENSATION(51, "compensation", "compensation"),
    OSP_PROFILE_CREATE(52, "osp_profile_create", "osp_profile_create"),
    VISIT_WEBSITE(53, "visit_website","visit_website"),
    CLAIM_TOKEN(54, "claim_token", "claim_token"),
    CLAIM_BADGE(55, "claim_badge", "claim_badge"),
    OWO_ASSET(56, "owo_asset", "owo_asset"),
    SKIP_VERIFY(57, "skip_verify", "skip_verify"),
    OWO_ASSET_OVER_LIMIT(58, "owo_asset_over_limit", "owo_asset_over_limit"),
    CLAIM_REDEEM(59, "claim_redeem", "claim_redeem"),
    INVITE_DIVIDE(60, "invite_divide", "invite_divide"),
    ACQUIRE_QUESTION(61, "acquire_question", "acquire_question"),
    SUBMIT_QUESTION(62, "submit_question", "submit_question"),
    POST_FOLLOW_BOUNTY_APP(63, "post_follow_bounty_app", "post_follow_bounty_app"),
    CLAIM_REWARDS_APP(64, "claim_rewards_app", "claim_rewards_app"),
    FOLLOW_DEEKER_APP(65, "follow_deeker_app", "follow_deeker_app"),
    FINISH_FOLLOW_BOUNTY_APP(66, "finish_follow_bounty_app", "finish_follow_bounty_app"),
    PLAY_GAME(68,"play_game", "play_game"),
    USER_BET(67, "user_bet", "user_bet"),
    CLAIM_TICKET(69,"claim_ticket", "claim_ticket"),
    CREATE_DEEK_PROFILE(70,"create_deek_profile", "create_deek_profile"),
    INVITE_APPLY_WISH(71, "invite_apply_wish", "invite_apply_wish"),
    INVITE_APPLY_REFERRAL(72, "invite_apply_referral", "invite_apply_referral"),
    APPLY_REFERRAL(73, "apply_referral", "apply_referral"),
    FOLLOWED_DEEKER_APP(74, "followed_deeker_app", "followed_deeker_app"),
    SPIN_A_WHEEL(75, "spin_a_wheel", "spin_a_wheel"),
    PLAY_DEEK_GAME_FARM(76, "play_deek_game_farm", "play_deek_game_farm"),
    MEMBER_CASTLE_LEVEL(77, "member_castle_level", "member_castle_level"),
    SLG_NFT(78, "slg_nft", "slg_nft"),
    SLG_VOTE(79, "slg_vote", "slg_vote"),
    CLAIM_ONCE_HARVEST(80, "claim_once_harvest", "claim_once_harvest"),
    JOIN_TG_CHANNEL_3(81, "join_tg_channel_3", "join_tg_channel_3"),
    PLAY_DEEK_GAME_SHAKE(82, "play_deek_game_shake", "play_deek_game_shake"),
    EOA_ASSET(83, "eoa_asset", "eoa_asset"),
    TRIBE_RECOMMENDED(84, "tribe_recommended", "tribe_recommended"),
    CONNECT_WALLET(85, "connect_wallet", "connect_wallet"),
    JOIN_TG(86, "join_tg", "join_tg"),
    OSP_CONNECT_TG(87, "osp_connect_tg", "osp_connect_tg"),
    CLAMIN_BADGE(88, "clamin_badge", "clamin_badge"),
    COMMENT_POST_X(89, "comment_post_x", "comment_post_x"),
    RETWEET_POST_X(90, "retweet_post_x", "retweet_post_x"),
    CLAIM_OWO(91, "claim_owo", "claim_owo"),
    SETS_CALCULATE_REWARD(92, "sets_calculate_reward", "sets_calculate_reward"),
    SETS_NATURE_REWARD(93, "sets_nature_reward", "sets_nature_reward"),
    JOIN_TG_CHANNEL(94, "join_tg_channel", "join_tg_channel"),
    EXTERNAL_REWARD(95, "external_reward", "external_reward"),
    START_GAME_DEEK(96, "start_game_deek", "start_game_deek"),
    START_GAME_LOA(97, "start_game_loa", "start_game_loa"),
    GAME_SETTLE(98, "game_settle", "game_settle"),
    SUBSCRIBE_TG(99, "subscribe_tg", "subscribe_tg"),
    WATCH_ADS(100, "watch_ads", "watch_ads"),
    TG_PREMIUM(101, "tg_premium", "tg_premium"),
    BOOST_TG_CHANNEL(102, "boost_tg_channel", "boost_tg_channel"),
    INVITED_PLAY_GAME(103, "invited_play_game", "invited_play_game"),
    CREATE_DAPP(104, "create_dapp", "create_dapp"),
    EXCHANGE_TICKETS(105, "exchange_tickets", "exchange_tickets"),
    EXCHANGE_MILES(106, "exchange_miles", "exchange_miles"),
    PURCHASE_TICKETS(107, "purchase_tickets", "purchase_tickets"),
    BOOST_CREATE_DAPP(108, "boost_create_dapp", "boost_create_dapp"),
    BOOST_LIKE_POST_X(109, "boost_like_post_x", "boost_like_post_x"),
    BOOST_RETWEET_POST_X(110, "boost_retweet_post_x", "boost_retweet_post_x"),
    BOOST_CHECK_IN(111, "boost_check_in", "boost_check_in"),
    BOOST_JOIN_OSP_DC(112, "boost_join_osp_dc", "boost_join_osp_dc"),
    LOTTERY_COIN(113, "lottery_coin", "lottery_coin"),
    LOTTERY_EXTRA_BOOST(114, "lottery_extra_boost", "lottery_extra_boost"),
    LOTTERY_TICKET(115, "lottery_ticket", "lottery_ticket"),
    REDEEM_CODE(116, "redeem_code", "redeem_code"),
    JOIN_TG_GROUP(117, "join_tg_group", "join_tg_group"),
    BUY_OJO(118, "buy_ojo", "buy_ojo"),
    WITHDRAW_OJO(119, "withdraw_ojo", "withdraw_ojo"),
    CONTRIBUTION_HUMAN(120, "contribution_human", "contribution_human"),
    CONTRIBUTION_DEEP_HUMAN(121, "contribution_deep_human", "contribution_deep_human"),
    CREDENTIALS_UNBIND_X(122, "credentials_unbind_x", "credentials_unbind_x"),
    CLAIM_EXPERIENCE_VOUCHER(124, "claim_experience_voucher", "claim_experience_voucher"),
    CLAIM_VOUCHER(125, "claim_voucher", "claim_voucher"),
    DISTRIBUTION(126, "distribution", "distribution"),
    CONTRIBUTION_AI(127, "contribution_ai", "contribution_ai"),
    ;

    private long code;
    private String codeDesc;
    private String desc;

    AssetBusinessType(long code, String codeDesc, String desc) {
        this.code = code;
        this.desc = desc;
        this.codeDesc = codeDesc;
    }


    public static AssetBusinessType valueOf(long code) {
        for (AssetBusinessType type : AssetBusinessType.values()) {
            if (code == type.code) {
                return type;
            }
        }
        return null;
    }

    public static AssetBusinessType valueOfByCodeDesc(String codeDesc){
        for (AssetBusinessType type : AssetBusinessType.values()) {
            if (type.codeDesc.equals(codeDesc)) {
                return type;
            }
        }
        return null;
    }
}
