package com.kikitrade.asset.model.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AssetDTO
 *
 * <AUTHOR>
 * @create 2022/7/26 9:48 上午
 * @modify
 */
@Data
public class AssetDailyIncrementResponse implements Serializable {

    private String date;

    private String saasId;

    private String customerId;

    private Long assetType;

    private BigDecimal increment;

}