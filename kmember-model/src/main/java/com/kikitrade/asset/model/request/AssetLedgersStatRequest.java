package com.kikitrade.asset.model.request;

import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetOperateType;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.member.model.constant.EquityConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AssetLedgersStatRequest implements Serializable {

    /**
     * 用户id
     */
    private String customerId;

    /**
     * saasID
     */
    private String saasId;

    /**
     * 虚拟资产类型， 必传
     */
    private AssetType assetType;

    /**
     * 账户类型，必传
     */
    private AssetCategory assetCategory;

    /**
     * 积分操作类型，过滤条件，非必传。
     */
    private AssetOperateType assetOperateType;
    /**
     * 获得积分的业务类型
     */
    private AssetBusinessType assetBusinessType;

    private Date start;

    private Date end;
}
