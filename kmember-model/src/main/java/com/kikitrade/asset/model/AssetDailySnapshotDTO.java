package com.kikitrade.asset.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AssetDTO
 *
 * <AUTHOR>
 * @create 2022/7/26 9:48 上午
 * @modify
 */
@Data
public class AssetDailySnapshotDTO implements Serializable {

    private String id;

    private String date;

    private String saasId;

    private String customerId;

    private Long assetType;

    private BigDecimal available;

}