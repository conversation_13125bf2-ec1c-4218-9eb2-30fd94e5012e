package com.drex.asset.api.model.constant;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum AssetType {
    /**
     * 积分
     */
    POINT(1),

    ;

    private int code;

    AssetType(int code) {
        this.code = code;
    }

    public int code() {
        return code;
    }

    public static AssetType fromCode(int code) {
        return Arrays.stream(AssetType.values()).filter(a -> a.code() == code).findAny().orElse(null);
    }
}
