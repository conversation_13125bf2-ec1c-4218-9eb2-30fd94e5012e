package com.drex.asset.api.model.request;

import com.drex.asset.api.model.constant.AssetType;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
public class AssetQueryRequest implements Serializable {

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * DApp ID - 默认为空
     */
    @Builder.Default
    private String dappId = "";

    /**
     * 资产类型
     */
    private AssetType assetType;

    /**
     * 构建资产ID
     * 格式：customerId + dappId(5位) + assetType.code(5位)
     * @return 资产ID
     */
    public String buildAssetId() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.customerId);
        sb.append(this.dappId);
        sb.append(String.format("%05d", this.assetType.code()));
        return sb.toString();
    }
}
