package com.drex.asset.api.model.request;

import com.drex.asset.api.model.constant.AssetBusinessType;
import com.drex.asset.api.model.constant.AssetType;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@SuperBuilder
@NoArgsConstructor
public class BaseAssetOperateRequest implements Serializable {

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * DApp ID - 默认为00001
     */
    @Builder.Default
    private String dappId = "00001";

    /**
     * 类型
     */
    private AssetType assetType;

    /**
     * 业务类型
     */
    private AssetBusinessType businessType;

    /**
     * 业务ID - Global Unique
     */
    private String businessId;

    /**
     * 操作金额
     */
    private BigDecimal amount;

    /**
     * 操作描述
     */
    private String description;

}
