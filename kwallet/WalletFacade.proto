syntax = "proto3";
import "google/protobuf/timestamp.proto";
package com.kikitrade.kwallet.facade;
option java_package = "com.kikitrade.kwallet.facade";
option java_multiple_files = true;

enum WalletChannelStatus{
  Enable = 0;// (0, "正常激活"),
  Disable = 1;//(1, "关闭-不可用"),
  Full = 2;//(2, "不可用-管理地址已满"),
}

enum WalletChannelType{
  HOT = 0; //(0, "hot"),
  FTX = 1; //(1, "ftx"),
  FIRE_BLOCK = 2; //(2, "fireblock"),
}

message WalletChannel {
  string saasId = 1;
  string id = 2;
  string name = 3;
  string currency = 4;
  WalletChannelType type = 5;
  string mainChannelId = 6;
  int32 maxAddressCount = 7;
  int32 batchAddressCount = 8;
  int32 currentAddressCount = 9;
  WalletChannelStatus status = 10;
  string apiUrl = 11;
  string apiUserName = 12;
  string apiPassword = 13;
  string config = 14; // must be json
  string extra = 15; // must be json
  int32 maxPackageCount = 16;
  int32 maxPackageInterval = 17;
  string apiCurrency = 18;
  bool enableDeposit = 19;
  bool enableWithdraw = 20;
  google.protobuf.Timestamp created = 21;
  google.protobuf.Timestamp modified = 22;
  string alertEmail = 23;
  string alertAmount = 24;
}

//////////////////////以下服务定义

message GetWalletChannelRequest{
  string saasId = 1;
  string id = 2;
}

message WalletChannelResponse{
  bool success = 1;
  string message = 2; //成功清除，增加清除的id
  WalletChannel walletChannel = 3;
}

message QueryWalletChannelRequest{
  string saasId = 1;
  WalletChannelType type = 2;
  string currency = 3;
  google.protobuf.Timestamp start = 4;
  google.protobuf.Timestamp end = 5;
  int32 offset = 6;
  int32 limit = 7;
}

message QueryWalletChannelResponse{
  bool success = 1;
  string message = 2;
  repeated WalletChannel walletChannel = 3;
  int32 totalCount = 4;
}

message CreateOrUpdateWalletChannelBaseRequest{
  string saasId = 1;
  string id = 2;
  string name = 3;
  string currency = 4;
  WalletChannelType type = 5;
  string mainChannelId = 6; //主链ID
  int32 maxAddressCount = 7;
  int32 batchAddressCount = 8;
  int32 currentAddressCount = 9;
  WalletChannelStatus status = 10;
  bool enableDeposit = 11;
  bool enableWithdraw = 12;
  int32 maxPackageCount = 13;
  int32 maxPackageInterval = 14;
  string alertEmail = 15;
  string alertAmount = 16;
}

message UpdateWalletChannelStatusRequest{
  string saasId = 1;
  string channelId = 2;
  WalletChannelStatus status = 3;
  bool enableDeposit = 4;
  bool enableWithdraw = 5;
}

message UpdateWalletChannelApiConfigRequest{
  string saasId = 1;
  string channelId = 2;
  string apiCurrency = 3;
  string apiUrl = 4;
  string apiUserName = 5;
  string apiPassword = 6;
  string config = 7; // must be json
  string extra = 8; // must be json

}


service WalletFacade {
  rpc getWalletChannel(GetWalletChannelRequest) returns (WalletChannelResponse);
  rpc queryWalletChannels(QueryWalletChannelRequest) returns (QueryWalletChannelResponse);
  rpc createOrUpdateWalletChannel(CreateOrUpdateWalletChannelBaseRequest) returns (WalletChannelResponse);
  rpc updateWalletChannelStatus(UpdateWalletChannelStatusRequest) returns (WalletChannelResponse);
  rpc updateWalletChannelApiConfig(UpdateWalletChannelApiConfigRequest) returns (WalletChannelResponse);
}
