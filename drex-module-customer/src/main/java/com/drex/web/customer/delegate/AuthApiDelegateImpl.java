package com.drex.web.customer.delegate;

import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.RemoteAuthService;
import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.ThirdAuthRequest;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.customer.api.response.ThirdAuthDTO;
import com.drex.customer.api.response.ThirdBindingsDTO;
import com.drex.web.common.*;
import com.drex.web.common.utils.JwtUtils;
import com.drex.web.common.utils.SymmetricEncryptionUtil;
import com.drex.web.customer.generated.api.v1.AuthApiDelegate;
import com.drex.web.customer.generated.model.v1.*;
import com.drex.web.customer.service.AuthMapperStruct;
import com.drex.web.customer.service.AuthService;
import com.drex.web.customer.sign.LoginSignatureDTO;
import com.kikitrade.framework.common.model.Response;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class AuthApiDelegateImpl implements AuthApiDelegate {

    @Resource
    private AuthService authService;
    @DubboReference
    private RemoteAuthService remoteAuthService;
    @Resource
    private AuthMapperStruct authMapperStruct;

    @Override
    public ResponseEntity<AccessTokenResponse> authLogin(LoginRequest loginRequest) {
        log.info("authLogin request:{}", JSON.toJSONString(loginRequest));
        AccessTokenResponse accessTokenResponse = new AccessTokenResponse();
        accessTokenResponse.success();
        //验证签名
        String message = authService.getChallenge(loginRequest.getEoaAddress());
        if(message == null){
            accessTokenResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), ErrorCode.SIGNATURE_INVALID.getMessage());
            return ResponseEntity.badRequest().body(accessTokenResponse);
        }
        LoginSignatureDTO loginSignatureDTO = authService.verifySignature(loginRequest.getEoaAddress(), message, loginRequest.getSignature());
        if(!loginSignatureDTO.getIsValid()){
            accessTokenResponse.fail(ErrorCode.SIGNATURE_INVALID.getCode(), ErrorCode.SIGNATURE_INVALID.getMessage());
            return ResponseEntity.badRequest().body(accessTokenResponse);
        }

        AuthLoginRequest authLoginRequest = new AuthLoginRequest();
        authLoginRequest.setEoaAddress(loginRequest.getEoaAddress());
        if(StringUtils.hasText(loginRequest.getExtraCrypto())){
            String decrypt = SymmetricEncryptionUtil.verifyAndDecrypt(message, loginRequest.getExtraCrypto());
            authLoginRequest.setExtraCrypto(decrypt);
        }

        log.info("authLoginRequest:{}", JSON.toJSONString(authLoginRequest));
        Response<CustomerDTO> loginResponse = remoteAuthService.login(authLoginRequest);

        log.info("loginResponse:{}", loginResponse);
        if(loginResponse.isSuccess()){
            JwtToken accessToken = JwtUtils.generateTokenPair(String.valueOf(loginResponse.getData().getCustomerId()), String.valueOf(loginResponse.getData().getCustomerId()));
            accessTokenResponse.setObj(new AccessToken().accessToken(accessToken.getAccessToken()).refreshToken(accessToken.getRefreshToken()).isNewUser(loginResponse.getData().getIsNewUser()));
            log.info("authLogin response:{}", JSON.toJSONString(accessTokenResponse));
            return ResponseEntity.ok(accessTokenResponse);
        }
        accessTokenResponse.fail(loginResponse.getCode(), loginResponse.getMessage());
        return ResponseEntity.badRequest().body(accessTokenResponse);
    }

    @Override
    public ResponseEntity<ChallengeResponse> challenge(String address, String type) {
        Assert.isTrue(Arrays.stream(Constant.ChallengeType.values()).anyMatch(t -> t.name().equals(type)), "type must be social or wallet");
        Challenge challenge = authService.generateChallenge(address);
        log.info("challenge response:{}", challenge);
        ChallengeResponse challengeResponse = new ChallengeResponse();
        challengeResponse.success();
        challengeResponse.setObj(challenge);
        return ResponseEntity.ok(challengeResponse);
    }

    @Override
    public ResponseEntity<LogoutResponse> logout() {
        LogoutResponse logoutResponse = new LogoutResponse();
        logoutResponse.success();
        return ResponseEntity.ok(logoutResponse);
    }

    @Override
    public ResponseEntity<AccessTokenResponse> refreshAccessToken(String accessToken, String refreshToken) {
        Jws<Claims> jws = JwtUtils.getAccessToken(refreshToken);
        if(jws == null){
            AccessTokenResponse response = new AccessTokenResponse();
            response.fail(ErrorCode.REFRESH_TOKEN_INVALID.getCode(), ErrorCode.REFRESH_TOKEN_INVALID.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
        JwtToken jwtToken = JwtUtils.generateTokenPair(jws.getBody().getSubject(), jws.getBody().getSubject());
        AccessTokenResponse accessTokenResponse = new AccessTokenResponse();
        accessTokenResponse.success();
        accessTokenResponse.setObj(new AccessToken().accessToken(jwtToken.getAccessToken()).refreshToken(jwtToken.getRefreshToken()).isNewUser(false));
        return ResponseEntity.ok(accessTokenResponse);
    }

    @Override
    public ResponseEntity<ThirdAuthorizeResponse> thirdAuthorize(ThirdAuthorizeRequest thirdAuthorizeRequest) {
        log.info("thirdAuthorize request:{}", JSON.toJSONString(thirdAuthorizeRequest));
        ThirdAuthorizeResponse response = new ThirdAuthorizeResponse();
        response.success();

        ThirdAuthRequest thirdAuthRequest = new ThirdAuthRequest();
        thirdAuthRequest.setPlatform(thirdAuthorizeRequest.getPlatform());
        thirdAuthRequest.setCode(thirdAuthorizeRequest.getCode());
        thirdAuthRequest.setCustomerId(CustomerHolder.customer().getCustomerId());
        Response<ThirdAuthDTO> thirdAuthDTO = remoteAuthService.thirdAuth(thirdAuthRequest);
        log.info("thirdAuthDTO:{}", JSON.toJSONString(thirdAuthDTO));
        if(thirdAuthDTO.isSuccess()){
            ThirdAuthorizeInfo thirdAuthorizeInfo = authMapperStruct.toThirdAuthorizeInfo(thirdAuthDTO.getData());
            response.setObj(thirdAuthorizeInfo);
            return ResponseEntity.ok(response);
        } else {
            response.fail(thirdAuthDTO.getCode(), thirdAuthDTO.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @Override
    public ResponseEntity<ThirdBindingsResponse> thirdBindings(String authorization) {
        ThirdBindingsResponse thirdBindingsResponse = new ThirdBindingsResponse();
        thirdBindingsResponse.success();

        Response<List<ThirdBindingsDTO>> thirdBindings = remoteAuthService.thirdBindings(CustomerHolder.customer().getCustomerId());
        if(thirdBindings.isSuccess()){
            thirdBindingsResponse.setObj(authMapperStruct.toThirdBindingsInfo(thirdBindings.getData()));
            return ResponseEntity.ok(thirdBindingsResponse);
        } else {
            thirdBindingsResponse.fail(thirdBindings.getCode(), thirdBindings.getMessage());
            return ResponseEntity.badRequest().body(thirdBindingsResponse);
        }
    }
}
