/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.6.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.drex.web.customer.generated.api.v1;

import com.drex.web.customer.generated.model.v1.CustomerInfoResponse;
import com.drex.web.customer.generated.model.v1.InviteBindRequest;
import com.drex.web.customer.generated.model.v1.InviteBindResponse;
import com.drex.web.customer.generated.model.v1.InviteInfoResponse;
import com.drex.web.customer.generated.model.v1.AddWaitListRequest;
import com.drex.web.customer.generated.model.v1.AddWaitListResponse;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "Customer", description = "用户相关接口描述")
public interface CustomerApi {

    default CustomerApiDelegate getDelegate() {
        return new CustomerApiDelegate() {};
    }

    /**
     * POST /customers/invites/bind : 邀请绑定
     * 邀请绑定
     *
     * @param inviteBindRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "bindInvite",
        summary = "邀请绑定",
        description = "邀请绑定",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = InviteBindResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customers/invites/bind",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<InviteBindResponse> bindInvite(
        @Parameter(name = "InviteBindRequest", description = "") @Valid @RequestBody(required = false) InviteBindRequest inviteBindRequest
    ) {
        return getDelegate().bindInvite(inviteBindRequest);
    }


    /**
     * GET /customers/me : 查询当前用户信息
     * 查询当前用户信息
     *
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "customer",
        summary = "查询当前用户信息",
        description = "查询当前用户信息",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = CustomerInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customers/me",
        produces = { "application/json" }
    )
    default ResponseEntity<CustomerInfoResponse> customer(

    ) {
        return getDelegate().customer();
    }


    /**
     * GET /customers/invites/me : 查询当前用户邀请信息
     * 查询当前用户邀请信息
     *
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "invite",
        summary = "查询当前用户邀请信息",
        description = "查询当前用户邀请信息",
        tags = { "Customer" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = InviteInfoResponse.class))
            })
        },
        security = {
            @SecurityRequirement(name = "jwtBearerAuth")
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customers/invites/me",
        produces = { "application/json" }
    )
    default ResponseEntity<InviteInfoResponse> invite(

    ) {
        return getDelegate().invite();
    }

    /**
     * POST /customers/addWaitList : /customers/addWaitList
     * join the waitList from officialWebsite
     *
     * @param addWaitListRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
            operationId = "addWaitList",
            summary = "/customers/addWaitList",
            description = "join the waitList from officialWebsite",
            tags = { "Customer" },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = AddWaitListResponse.class))
                    })
            }
    )
    @RequestMapping(
            method = RequestMethod.POST,
            value = "/customers/addWaitList",
            produces = { "application/json" },
            consumes = { "application/json" }
    )
    default ResponseEntity<AddWaitListResponse> addWaitList(
            @Parameter(name = "AddWaitListRequest", description = "") @Valid @RequestBody(required = false) AddWaitListRequest addWaitListRequest
    ) {
        return getDelegate().addWaitList(addWaitListRequest);
    }

}
