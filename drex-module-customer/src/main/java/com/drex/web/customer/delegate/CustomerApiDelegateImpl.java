package com.drex.web.customer.delegate;

import com.drex.activity.task.api.RemoteTaskService;
import com.drex.activity.task.model.response.TaskConfigResponse;
import com.drex.asset.api.model.AssetDTO;
import com.drex.asset.api.model.constant.AssetType;
import com.drex.asset.api.model.request.AssetQueryRequest;
import com.drex.asset.api.service.RemoteAssetService;
import com.drex.core.api.RemoteRexyBasketService;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.RexyBasketsRequest;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.web.common.CustomerHolder;
import com.drex.web.customer.generated.api.v1.CustomerApiDelegate;
import com.drex.web.customer.generated.model.v1.*;
import com.drex.web.customer.service.CustomerMapperStruct;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class CustomerApiDelegateImpl implements CustomerApiDelegate {

    @Resource
    private CustomerMapperStruct customerMapperStruct;

    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteRexyBasketService remoteRexyBasketService;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @DubboReference
    private RemoteTaskService remoteTaskService;

    @Override
    public ResponseEntity<CustomerInfoResponse> customer() {
        CustomerDTO customerDTO = CustomerHolder.customer();
        CustomerInfo customerInfo = customerMapperStruct.toCustomerInfo(customerDTO);

        AssetQueryRequest assetQueryRequest = AssetQueryRequest.builder()
                .customerId(customerDTO.getCustomerId())
                .assetType(AssetType.POINT).build();

        Response<AssetDTO> assetDTOResponse = remoteAssetService.asset(assetQueryRequest);
        if(assetDTOResponse.isSuccess() && assetDTOResponse.getData() != null){
            customerInfo.setPoint(assetDTOResponse.getData().getAvailable().longValue());
        }

        RexyBasketsRequest request = RexyBasketsRequest.builder()
                .customerId(customerDTO.getCustomerId())
                .basketType(RexyConstant.RexyBasketsTypeEnum.normal)
                .build();
        Response<CustomerRexyBasketsDTO> customerRexyBasketsResponse = remoteRexyBasketService.getCustomerRexyBaskets(request);
        if(customerRexyBasketsResponse.isSuccess()){
            CustomerRexyBasketsDTO basketsDTO = customerRexyBasketsResponse.getData();
            CustomerInfoRexy customerInfoRexy = new CustomerInfoRexy();
            customerInfoRexy.setId(basketsDTO.getRexyId());
            customerInfoRexy.setName(basketsDTO.getRexyName());
            customerInfoRexy.setAvatar(basketsDTO.getRexyAvatar());
            customerInfo.setAvatar(basketsDTO.getRexyAvatar());
            customerInfoRexy.setBasketPoint(basketsDTO.getReceived().intValue());
            customerInfoRexy.setRate(basketsDTO.getBasketRate().intValue());
            customerInfoRexy.setLimitPoint(basketsDTO.getBasketLimit().intValue());
            customerInfo.setRexy(customerInfoRexy);
        }

        CustomerInfoResponse customerInfoResponse = new CustomerInfoResponse();
        customerInfoResponse.success();
        customerInfoResponse.setObj(customerInfo);
        return ResponseEntity.ok(customerInfoResponse);
    }

    @Override
    public ResponseEntity<InviteInfoResponse> invite() {
        InviteInfo inviteInfo = new InviteInfo();
        Response<TaskConfigResponse> task = remoteTaskService.getTaskByCode("trex", "invite_register", null);
        Response<Long> inviteCountResponse = remoteCustomerService.countByReferrerId(CustomerHolder.customer().getCustomerId());
        inviteInfo.setInviteCount(inviteCountResponse.isSuccess() ? inviteCountResponse.getData().intValue() : 0);
        inviteInfo.setCustomerId(CustomerHolder.customer().getCustomerId());
        inviteInfo.setInviteCode(CustomerHolder.customer().getInviteCode());
        inviteInfo.setAddress(CustomerHolder.customer().getRegisterAddress());
        inviteInfo.setInviteRule(task.isSuccess() ? task.getData().getDesc() : "");

        RexyBasketsRequest request = RexyBasketsRequest.builder()
                .customerId(CustomerHolder.customer().getCustomerId())
                .basketType(RexyConstant.RexyBasketsTypeEnum.invite)
                .build();
        Response<CustomerRexyBasketsDTO> customerRexyBasketsResponse = remoteRexyBasketService.getCustomerRexyBaskets(request);
        if(customerRexyBasketsResponse.isSuccess()){
            inviteInfo.setInviteUnClaimedReward(customerRexyBasketsResponse.getData().getReceived().intValue());
        }
        AssetQueryRequest assetQueryRequest = AssetQueryRequest.builder()
                .customerId(CustomerHolder.customer().getCustomerId())
                .assetType(AssetType.POINT)
                .build();

        Response<BigDecimal> bigDecimalResponse = remoteAssetService.sumInvitedAsset(assetQueryRequest);
        inviteInfo.setInviteClaimedReward(bigDecimalResponse.isSuccess() ? bigDecimalResponse.getData().intValue() : 0);
        InviteInfoResponse response = new InviteInfoResponse();
        response.success();
        response.setObj(inviteInfo);
        return ResponseEntity.ok(response);
    }

    /**
     * POST /customers/invites/bind : 邀请绑定
     * 邀请绑定
     *
     * @param inviteBindRequest (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<InviteBindResponse> bindInvite(InviteBindRequest inviteBindRequest) {
        log.info("bindInvite:{}", inviteBindRequest);
        InviteBindResponse inviteBindResponse = new InviteBindResponse();
        if(StringUtils.isBlank(inviteBindRequest.getInviteCode())){
            inviteBindResponse.success();
            return ResponseEntity.ok(inviteBindResponse);
        }
        Response response = remoteCustomerService.bindInviteCode(CustomerHolder.customer().getCustomerId(), inviteBindRequest.getInviteCode());
        if(response.isSuccess()){
            inviteBindResponse.success();
            return ResponseEntity.ok(inviteBindResponse);
        }
        inviteBindResponse.fail(response);
        return ResponseEntity.badRequest().body(inviteBindResponse);
    }

    public static void main(String[] args) {
        System.out.println(isValidEmail("<EMAIL>"));
    }
}
