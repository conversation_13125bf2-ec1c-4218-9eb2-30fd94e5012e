package com.kikitrade.ksocial.mock.impl;

import com.kikitrade.ksocial.common.exception.SocialException;
import com.kikitrade.ksocial.common.exception.SocialMessageEnum;
import com.kikitrade.ksocial.dal.builder.IUserAuthorityStoreBuilder;
import com.kikitrade.ksocial.dal.model.UserAuthorityDO;
import com.kikitrade.ksocial.mock.IUserAuthorityStoreBuilderMock;
import lombok.extern.slf4j.Slf4j;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.kikitrade.ksocial.op.BusinessOP.userAuthorityDOList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@Slf4j
@Component
public class IUserAuthorityStoreBuilderMockImpl implements IUserAuthorityStoreBuilderMock {
    @Autowired
    IUserAuthorityStoreBuilder userAuthorityStoreBuilder;


    @Override
    public void getByEoAddress() {
        Mockito.doAnswer(c -> {
            String chainId = c.getArgument(0);
            String eoaAddress = c.getArgument(1);
            return userAuthorityDOList.stream().filter(u -> u.getChainId().equals(chainId) && u.getEoaAddress().equals(eoaAddress)).toList();
        }).when(userAuthorityStoreBuilder).getByEoAddress(anyString(), anyString());
    }

    @Override
    public void getByPrimaryKey() {
        Mockito.doAnswer(c -> {
            UserAuthorityDO userAuthorityDO = c.getArgument(0);
            return userAuthorityDOList.stream().filter(u -> u.getChainId().equals(userAuthorityDO.getChainId()) && u.getEoaAddress().equals(userAuthorityDO.getEoaAddress()) && u.getOspAddress().equals(userAuthorityDO.getOspAddress())).
                    findFirst().orElse(null);
        }).when(userAuthorityStoreBuilder).getByPrimaryKey(any(UserAuthorityDO.class));
    }

    @Override
    public void store() {
        Mockito.doAnswer(c -> {
            UserAuthorityDO userAuthorityDO = c.getArgument(0);
            userAuthorityDO.setCreated(System.currentTimeMillis());
            userAuthorityDO.setModified(System.currentTimeMillis());

            Optional<UserAuthorityDO> first = userAuthorityDOList.stream().filter(u -> u.getEoaAddress().equals(userAuthorityDO.getEoaAddress()) && u.getOspAddress().equals(userAuthorityDO.getOspAddress())).
                    findFirst();
            if (first.isPresent()) {
                log.error("store repeat, userAuthorityDO:{}", userAuthorityDO);
                throw new SocialException(SocialMessageEnum.SYSTEM_ERROR);
            }
            userAuthorityDOList.add(userAuthorityDO);
            return userAuthorityDO;
        }).when(userAuthorityStoreBuilder).store(any(UserAuthorityDO.class));
    }

    @Override
    public void updateByPrimaryKey() {
        Mockito.doAnswer(c -> {
            UserAuthorityDO userAuthorityDO = c.getArgument(0);
            userAuthorityDO.setModified(System.currentTimeMillis());
            Optional<UserAuthorityDO> first = userAuthorityDOList.stream().filter(u -> u.getEoaAddress().equals(userAuthorityDO.getEoaAddress()) && u.getOspAddress().equals(userAuthorityDO.getOspAddress())).findFirst();
            if (first.isPresent()) {
                UserAuthorityDO userAuthorityDB = first.get();
                userAuthorityDB.setAuthority(userAuthorityDO.getAuthority());
                userAuthorityDB.setModified(userAuthorityDO.getModified());
                return userAuthorityDB;
            }
            return null;
        }).when(userAuthorityStoreBuilder).updateByPrimaryKey(any(UserAuthorityDO.class));
    }

    @Override
    public void delete() {
        Mockito.doAnswer(c -> {
            UserAuthorityDO userAuthorityDO = c.getArgument(0);
            Optional<UserAuthorityDO> first = userAuthorityDOList.stream().filter(u -> u.getEoaAddress().equals(userAuthorityDO.getEoaAddress()) && u.getOspAddress().equals(userAuthorityDO.getOspAddress())).findFirst();
            if (first.isPresent()) {
                return userAuthorityDOList.remove(userAuthorityDO);
            }
            return false;
        }).when(userAuthorityStoreBuilder).delete(any(UserAuthorityDO.class));
    }


}
