package com.kikitrade.activity.model.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.SimpleTimeZone;
import java.util.TimeZone;

/**
 * @ClassName TimeUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2018/7/21 19:44
 * @Version 1.0
 **/
public class TimeUtil {

    public static final String YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDDHHMM = "yyyy-MM-dd HH:mm";
    public static final String YYYYMMDD_000000 = "yyyy-MM-dd 00:00:00";
    public static final String YYYYMMDD_235959 = "yyyyMMdd235959";
    public static final String YYYYMMDDHH_5959 = "yyyyMMddHH5959";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYYMM = "yyyy-MM";
    public static final String YYYYMMDDHH_0000 = "yyyyMMddHH0000";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYYMMDDHH = "yyyyMMddHH";
    public static final String YYYYMMDDHHmmss = "yyyyMMddHHmmss";
    public static final String YYYYMMDDHHmmssSSS = "yyyyMMddHHmmssSSS";
    public static final String DD = "dd";
    public static final String YYYY_MM_DD_235959 = "yyyy-MM-dd 23:59:59";

    public static Date parseUnittime(long unitTime){
        return new Date(unitTime);
    }

    /**
     * 获得任意时区的时间
     *
     * @param timeZoneOffset
     * @return
     */
    public static String getFormatedDateString(Date date, float timeZoneOffset) {
        if (timeZoneOffset > 13 || timeZoneOffset < -12) {
            timeZoneOffset = 0;
        }
        int newTime = (int) (timeZoneOffset * 60 * 60 * 1000);
        TimeZone timeZone;
        String[] ids = TimeZone.getAvailableIDs(newTime);
        if (ids.length == 0) {
            timeZone = TimeZone.getDefault();
        } else {
            timeZone = new SimpleTimeZone(newTime, ids[0]);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(timeZone);
        return sdf.format(date);
    }

    public static String getCurrentUtcTime(String formatter) {
        return getCurrentUtcTime(new SimpleDateFormat(formatter));
    }

    public static String getCurrentUtcTime(DateFormat formatter) {
        Date l_datetime = new Date();
        TimeZone l_timezone = TimeZone.getTimeZone("GMT-0");
        formatter.setTimeZone(l_timezone);
        return formatter.format(l_datetime);
    }

    public static String getCurrentTime(String formatter, float timeZoneOffset) {
        return getCurrentTime(new SimpleDateFormat(formatter), timeZoneOffset);
    }

    public static String getNextTime(String formatter, float timeZoneOffset) {
        return getCurrentTime(new SimpleDateFormat(formatter), timeZoneOffset);
    }

    public static String getCurrentTime(DateFormat formatter, float timeZoneOffset) {
        Date l_datetime = new Date();
        if (timeZoneOffset > 13 || timeZoneOffset < -12) {
            timeZoneOffset = 0;
        }
        int newTime = (int) (timeZoneOffset * 60 * 60 * 1000);
        TimeZone timeZone;
        String[] ids = TimeZone.getAvailableIDs(newTime);
        if (ids.length == 0) {
            timeZone = TimeZone.getDefault();
        } else {
            timeZone = new SimpleTimeZone(newTime, ids[0]);
        }
        formatter.setTimeZone(timeZone);
        return formatter.format(l_datetime);
    }

    public static String getUtcTime(Date date, String formatter) {
        return getUtcTime(date, new SimpleDateFormat(formatter));
    }

    public static String getUtcTime(Date date,DateFormat formatter) {
        TimeZone l_timezone = TimeZone.getTimeZone("GMT-0");
        formatter.setTimeZone(l_timezone);
        return formatter.format(date);
    }

    public static String getDataStr(Date date, String formatter) {
        return getDataStr(date, new SimpleDateFormat(formatter));
    }

    public static String getDataStr(Date date,DateFormat formatter) {
        return formatter.format(date);
    }

    public static Date parse(String date){
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(date);
        }catch (ParseException ex){
            return null;
        }
    }

    public static Date parse(String date, DateFormat formatter) {
        try {
            return formatter.parse(date);
        } catch (ParseException ex) {
            return null;
        }
    }

    public static String getUtcTime(Date date) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm Z");
        TimeZone l_timezone = TimeZone.getTimeZone("UTC");
        sdf.setTimeZone(l_timezone);
        return sdf.format(date);
    }


    public static Date getOffsetTime(int hour, int minute, int second) {
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(new Date());
        rightNow.add(Calendar.HOUR_OF_DAY, hour);//日期
        rightNow.add(Calendar.MINUTE, minute);//日期
        rightNow.add(Calendar.SECOND, second);//日期
        return rightNow.getTime();
    }

    public static Date getOffsetTime(Date date,int hour, int minute, int second) {
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(date);
        rightNow.add(Calendar.HOUR_OF_DAY, hour);//日期
        rightNow.add(Calendar.MINUTE, minute);//日期
        rightNow.add(Calendar.SECOND, second);//日期
        return rightNow.getTime();
    }

    public static Date getZeroDate() {
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(new Date());
        rightNow.set(Calendar.HOUR_OF_DAY, 0) ;
        rightNow.set(Calendar.MINUTE, 0);
        rightNow.set(Calendar.SECOND, 0);
        rightNow.set(Calendar.MILLISECOND, 0);
        return rightNow.getTime();
    }

    public static Date addMinute(Date current, int minute){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }

    public static Date addHour(Date current, int hours){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        return calendar.getTime();
    }

    public static Date addDay(Date current, int days){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    public static Date addWeek(Date current, int weeks){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        calendar.add(Calendar.WEEK_OF_MONTH, weeks);
        return calendar.getTime();
    }

    public static Date addMonth(Date current, int months){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        calendar.add(Calendar.MONTH, months);
        return calendar.getTime();
    }

    /**
     * 获取本周的第一天
     * @return String
     * **/
    public static String getWeekStart(Date date, String pattern){
        LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime with = localDate.with(DayOfWeek.MONDAY);
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String getCustomWeekStart(Date date, DayOfWeek dayOfWeek ,String pattern){
        LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        if(dayOfWeek != null){
            localDate = localDate.with(dayOfWeek);
        }
        return localDate.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 获取本周的第一天
     * @return String
     * **/
    public static String getWeekEnd(Date date, String pattern){
        LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime with = localDate.with(DayOfWeek.SUNDAY).plusWeeks(1);
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String getCustomWeekEnd(Date date, DayOfWeek dayOfWeek ,String pattern){
        LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusWeeks(1);
        if(dayOfWeek != null){
            localDate = localDate.with(dayOfWeek);
        }
        return localDate.plusWeeks(1).format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 获取本月的第一天
     * @return String
     * **/
    public static String getMonthStart(Date date, String pattern){
        LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime with = localDate.with(TemporalAdjusters.firstDayOfMonth());
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 获取本月的最后一天
     * @return String
     * **/
    public static String getMonthEnd(Date date, String pattern){
        LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime with = localDate.with(TemporalAdjusters.lastDayOfMonth());
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static int getDaysOfMonth(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    public static void main(String[] args) {
        getZeroDate();
    }


}
