package com.stanly.admin.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/11/1 16:28
 */
@Getter
public enum ErrorMsg {


    Sever_Error("server error"),

    /**
     * file
     */
    Read_File_Error("read file error"),

    Upload_Error("upload error"),

    /**
     * 群聊相关错误信息
     */
    Add_Group_Member_Error("add the member to group error"),

    Modify_Group_Member_To_Admin_Error("modify group member to admin error"),

    Modify_Group_Member_Role_Error("modify group member role error"),

    Group_Member_File_Empty("The file that should contain group members is empty"),

    Invalid_File("the file is invalid"),

    ;


    ErrorMsg(String message) {
        this.message = message;
    }
    private String message;
}
