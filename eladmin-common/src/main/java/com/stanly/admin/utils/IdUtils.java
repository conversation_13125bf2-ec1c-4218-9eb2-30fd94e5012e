package com.stanly.admin.utils;

import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/18 16:38
 */
@Component
public class IdUtils {

    @Resource
    RedisUtils redisUtils;

    private static final String ID_KEY = "task_config_id";

    private static final Map<String, String> idMap = new HashMap<>(){{
        put("monster","400");
        put("monster-pre","400");
        put("zeek","401");
        put("zeek-app","402");
        put("bread","404");
        put("opentroch","405");
        put("mugen","406");
        put("loa-op","407");
        put("ufool","408");
        put("dojo3","409");
        put("opensocial","410");
        put("deek-app-84532","412");
        put("dojo3-tg","411");
        put("deek-app","413");
        put("deek-tg","414");
    }};

    public String generateId(String saasId){
        double hincr = redisUtils.hincr(ID_KEY, saasId, 1);
        return String.format("%s%05d", idMap.get(saasId), (int)hincr);
    }
}
