package com.kikitrade.kweb.service.paystack;

import com.kikitrade.kcustomer.api.exception.CustomerException;
import org.apache.dubbo.config.annotation.DubboReference;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.UpdateIdCertifiedRequest;
import com.kikitrade.kcustomer.api.service.RemoteCustomerExtraService;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.kweb.service.RegisterServiceHook;
import com.kikitrade.p2p.model.constants.PayStackCustomerIdentityConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@ConditionalOnProperty(prefix = "kweb", name = "saas-id", havingValue = "vibra")
public class PayStackRegisterServiceHookImpl implements RegisterServiceHook {
    @DubboReference
    private RemoteCustomerExtraService remoteCustomerExtraService;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @Autowired(required = false)
    private EventClient eventClient;

    @Override
    public void onRegister(String saasId, CustomerDTO customerDTO) {
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put("kyc_level", PayStackCustomerIdentityConstants.KycLevel.L1.getLevelName());
        boolean success = remoteCustomerExtraService.updateExtensionFields(customerDTO.getId(), updateFields);
        try {
            remoteCustomerService.updateIdCertified(UpdateIdCertifiedRequest.builder().saasId(customerDTO.getSaasId()).customerId(customerDTO.getId()).kycLevel(CustomerIdentityConstants.KycLevel.L1).build());
        } catch (CustomerException e) {
            log.error("remoteCustomerService.updateIdCertified.exception:{}", e);
        }
        log.info("kyc_level update to L1 [{}]", success);
        if (eventClient != null) {
            EventDTO eventDTO = EventDTO.builder()
                    .name(EventConstants.EventName.KYC_L1.getName())
                    .time(System.currentTimeMillis())
                    .globalUid(customerDTO.getId() + customerDTO.getPhone()).customerId(customerDTO.getId())
                    .source(eventClient.getDefaultSource())
                    .build();
            eventClient.push(eventDTO);
        }
    }
}
