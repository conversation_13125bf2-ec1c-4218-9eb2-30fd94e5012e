package com.kikitrade.kweb.model.p2p.merchant;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AdsUpdateRequest {
    private String saasId;
    private String adsNo;
    private BigDecimal priceLimit;
    private BigDecimal orderMin;
    private BigDecimal orderMax;
    private Integer timeLimit;
    private List<String> channelType;
    private List<String> paymentChannel;
    private String comment;
    private Integer payTimeOut;
    private Integer unconfirmedTimeOut;
    private String kycLevel;
}
