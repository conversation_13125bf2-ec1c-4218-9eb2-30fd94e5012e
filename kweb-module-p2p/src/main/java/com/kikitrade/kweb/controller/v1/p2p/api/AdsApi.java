package com.kikitrade.kweb.controller.v1.p2p.api;


import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.CustomerExtraDTO;
import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.controller.v1.p2p.delegate.AdsApiDelegate;
import com.kikitrade.kweb.model.common.WebResponse;
import com.kikitrade.kweb.model.p2p.merchant.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;


@Tag(name = "Ads", description = "C2C广告相关接口")
public interface AdsApi {
    String TAG = "Ads";

    default AdsApiDelegate getAdsApiDelegate() {
        return new AdsApiDelegate() {
        };
    }


    @Operation(summary = "C2C广告-广告发布", description = "商户发布广告", tags = TAG)
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", useReturnTypeSchema = true)
    })
    @PostMapping(value = "/ads/post")
    default WebResponse postAds(HttpServletRequest request,
                                @Parameter(description = "用户信息", in = ParameterIn.HEADER, required = true) @RequestAttribute(WebConstants.CUSTOMER) CustomerExtraDTO customer,
                                @RequestAttribute(WebConstants.SAAS_ID) String saasId,
                                AdsPostRequest adsPostRequest) throws CustomerException {
        adsPostRequest.setSaasId(saasId);
        return getAdsApiDelegate().postAds(request, customer, adsPostRequest);
    }


    @Operation(summary = "C2C广告-广告修改", description = "商户修改广告", tags = TAG)
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", useReturnTypeSchema = true)
    })
    @PostMapping(value = "/ads/update")
    default WebResponse updateAds(HttpServletRequest request,
                                  @Parameter(description = "用户信息", in = ParameterIn.HEADER, required = true) @RequestAttribute(WebConstants.CUSTOMER) CustomerExtraDTO customer,
                                  @RequestAttribute(WebConstants.SAAS_ID) String saasId,
                                  AdsUpdateRequest adsUpdateRequest) {
        adsUpdateRequest.setSaasId(saasId);
        return getAdsApiDelegate().updateAds(request, customer, adsUpdateRequest);
    }

    @Operation(summary = "C2C广告-广告取消", description = "商户广告取消", tags = TAG)
    @PostMapping(value = "/ads/cancel")
    default WebResponse cancelAds(@RequestAttribute(WebConstants.CUSTOMER) CustomerExtraDTO customer,
                                  @RequestAttribute(WebConstants.SAAS_ID) String saasId,
                                  @RequestParam(value = "adsNo") String adsNo) {
        return getAdsApiDelegate().cancelAds(customer, saasId, adsNo);
    }

    @Operation(summary = "C2C广告-广告上下线", description = "商户广告上下线", tags = TAG)
    @PostMapping(value = "/ads/online")
    default WebResponse onlineAds(@RequestAttribute(WebConstants.CUSTOMER) CustomerExtraDTO customer,
                                  @RequestAttribute(WebConstants.SAAS_ID) String saasId,
                                  @RequestParam(value = "adsNo") String adsNo,
                                  @RequestParam(value = "online") Boolean online) throws CustomerException {
        return getAdsApiDelegate().onlineAds(customer,saasId, adsNo, online);
    }

    @Operation(summary = "C2C广告-广告详情", description = "商户广告详情", tags = TAG)
    @PostMapping(value = "/ads/detail")
    default WebResponse adsDetail(@RequestParam(value = "adsNo") String adsNo) {
        return getAdsApiDelegate().adsDetail(adsNo);
    }

    @Operation(summary = "C2C广告-商户广告列表", description = "商户广告列表", tags = TAG)
    @PostMapping(value = "/myads/list")
    default WebResponse myAdsList(@RequestAttribute(WebConstants.CUSTOMER) CustomerExtraDTO customer,
                                  @Valid MyAdsListRequest request) {
        return getAdsApiDelegate().myAdsList(customer, request);
    }

    @Operation(summary = "C2C广告-广告列表", description = "广告列表", tags = TAG)
    @PostMapping(value = "/ads/list")
    default WebResponse adsList(@Valid AdsListRequest request) {
        return getAdsApiDelegate().adsList(request);
    }

    @Operation(summary = "C2C广告-快速交易广告匹配", description = "广告匹配", tags = TAG)
    @PostMapping(value = "/ads/match")
    default WebResponse instantAdsMatch(@RequestAttribute(WebConstants.CUSTOMER) CustomerExtraDTO customer, @Valid AdsMatchRequest request) {
        return getAdsApiDelegate().instantAdsMatch(customer, request);
    }

}
