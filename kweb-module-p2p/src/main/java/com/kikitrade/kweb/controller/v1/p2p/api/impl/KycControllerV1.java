package com.kikitrade.kweb.controller.v1.p2p.api.impl;

import com.kikitrade.kweb.annotation.V1RestController;
import com.kikitrade.kweb.controller.BaseController;
import com.kikitrade.kweb.controller.v1.p2p.api.KycApi;
import com.kikitrade.kweb.controller.v1.p2p.delegate.KycApiDelegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@V1RestController
@RequestMapping("/paystack/kyc")
public class KycControllerV1 extends BaseController implements KycApi {

    @Resource
    private KycApiDelegate kycApiDelegate;

    @Override
    public KycApiDelegate getKycApiDelegate() {
        return kycApiDelegate;
    }
}
