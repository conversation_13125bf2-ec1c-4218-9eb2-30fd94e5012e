package com.kikitrade.kweb.controller.v1.p2p.delegate;

import com.kikitrade.kweb.model.common.WebResponse;
import com.kikitrade.kweb.model.p2p.PaymentChannelRequest;

import java.util.List;

public interface PaymentChannelApiDelegate {

    default WebResponse paymentChannelSave(PaymentChannelRequest paymentChannelRequest) {
        return null;
    }

    default WebResponse paymentChannelUpdate(PaymentChannelRequest paymentChannelRequest) {
        return null;
    }

    default WebResponse channelStatusUpdate(String channelId, String status) {
        return null;
    }

    default WebResponse channelList(String channel, String channelType, List<String> status, Integer offset, Integer limit) {
        return null;
    }

    default WebResponse paywayList(String channelType, Integer oftenused) {
        return null;
    }

    default WebResponse channelDetail(String channelId) {
        return null;
    }
}
