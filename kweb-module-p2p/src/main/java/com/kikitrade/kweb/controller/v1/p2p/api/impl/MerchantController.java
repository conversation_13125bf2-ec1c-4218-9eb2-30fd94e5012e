package com.kikitrade.kweb.controller.v1.p2p.api.impl;

import com.kikitrade.kweb.annotation.V1RestController;
import com.kikitrade.kweb.controller.BaseController;
import com.kikitrade.kweb.controller.v1.p2p.api.MerchantApi;
import com.kikitrade.kweb.controller.v1.p2p.delegate.MerchantApiDelegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@V1RestController
@RequestMapping("/p2p/merchant")
public class MerchantController extends BaseController implements MerchantApi {
    @Resource
    private MerchantApiDelegate merchantApiDelegate;

    @Override
    public MerchantApiDelegate getMerchantApiDelegate() {
        return this.merchantApiDelegate;
    }
}
