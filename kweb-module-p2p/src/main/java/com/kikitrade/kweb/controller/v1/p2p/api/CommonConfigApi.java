package com.kikitrade.kweb.controller.v1.p2p.api;


import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.controller.v1.p2p.delegate.CommonConfigApiDelegate;
import com.kikitrade.kweb.model.common.WebResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Tag(name = "CommonConfigApi", description = "C2C系统参数，以及汇率盘口等参数")
public interface CommonConfigApi {
    String TAG = "CommonConfig";

    default CommonConfigApiDelegate getCommonConfigApiDelegate() {
        return new CommonConfigApiDelegate() {
        };
    }

    @Operation(summary = "系统数据-业务配置参数", description = "业务配置参数", tags = TAG)
    @GetMapping(value = "/sys/config")
    default WebResponse getSysCnfig(HttpServletRequest request, @RequestAttribute(WebConstants.SAAS_ID) String saasId) {
        return getCommonConfigApiDelegate().getSysConfig(request, saasId);
    }

    @Operation(summary = "系统数据-汇率结果", description = "汇率结果", tags = TAG)
    @GetMapping(value = "/exchange/rate")
    default WebResponse getExchangeRate(HttpServletRequest request, @RequestParam(value = "symbol") String pair) {
        return getCommonConfigApiDelegate().getExchangeRate(request, pair);
    }

    @Operation(summary = "系统数据-获取汇率", description = "获取汇率", tags = TAG)
    @GetMapping(value = "/exchange/rate/{base}")
    default WebResponse getAllExchangeRate(@PathVariable(value = "base") String base) {
        return getCommonConfigApiDelegate().getAllExchangeRate(base);
    }

    @Operation(summary = "系统数据-获取外部盘口汇率", description = "获取外部盘口汇率", tags = TAG)
    @GetMapping(value = "/external/symbols")
    default WebResponse getExternalSymbols(HttpServletRequest request) {
        return getCommonConfigApiDelegate().getAllExternalSymbolPrice();
    }

}
