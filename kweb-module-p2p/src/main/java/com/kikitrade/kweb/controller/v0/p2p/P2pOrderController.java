package com.kikitrade.kweb.controller.v0.p2p;

import com.kikitrade.kcustomer.api.constants.SecurityVerifyScene;
import com.kikitrade.kweb.config.KWebProperties;
import com.kikitrade.kweb.service.KwebBaseService;
import org.apache.dubbo.config.annotation.DubboReference;
import com.google.common.collect.Lists;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.CustomerExtraDTO;
import com.kikitrade.kcustomer.api.service.RemotePaymentTermService;
import com.kikitrade.kcustomer.common.constants.PaymentTermConstant;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.controller.p2p.P2pBaseController;
import com.kikitrade.kweb.model.common.WebResponse;
import com.kikitrade.kweb.service.P2pConfigService;
import com.kikitrade.p2p.api.RemoteP2pOrderService;
import com.kikitrade.p2p.api.RemotePaymentChannelService;
import com.kikitrade.p2p.model.*;
import com.kikitrade.p2p.model.constants.P2pConstants;
import com.kikitrade.trade.api.RemoteTradingService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Controller
@RequestMapping("/p2p/order")
public class P2pOrderController extends P2pBaseController {

    @DubboReference(retries = 0)
    private RemoteP2pOrderService remotep2pOrderService;
    @Resource
    private P2pConfigService p2pConfigService;
    @DubboReference
    private RemoteTradingService remoteTradingService;
    @Resource
    private KWebProperties kWebProperties;

    @DubboReference
    private RemotePaymentChannelService remotePaymentChannelService;
    @DubboReference
    private RemotePaymentTermService remotePaymentTermService;
    @Resource
    private KwebBaseService kwebBaseService;


    @RequestMapping(value = "/place", method = RequestMethod.POST)
    @ResponseBody
    public WebResponse placeOrder(@RequestAttribute(SAAS_ID) String saasId,
                                  @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                  @RequestParam(name = "type") String type,
                                  @RequestParam(name = "paymentChannel", required = false) List<String> paymentChannel,
                                  @RequestParam(name = "paymentTermId", required = false) List<String> paymentTermIds,
                                  @RequestParam(name = "adsNo") String adsNo,
                                  @RequestParam(name = "amount", required = false) BigDecimal amount,
                                  @RequestParam(name = "quantity", required = false) BigDecimal quantity,
                                  @RequestParam(value = "busiType", required = false, defaultValue = "GENERAL") String busiType,
                                  HttpServletRequest request) throws CustomerException {

        // 风险校验
        if (kWebProperties.isEnableRiskVerify() && remoteTradingService.isFraud(customer.getId())) {
            return WebResponse.result(WebResponseEnum.SYSTEM_SECURITY_CHECK_FAIL);
        }
        /**
         * 风控决策结果检查
         */
        kwebBaseService.verify(customer.getId(), null, null, SecurityVerifyScene.WITHDRAW);
        P2pOrderDTO p2pOrderDTO = new P2pOrderDTO();
        p2pOrderDTO.setType(type);
        if (!CollectionUtils.isEmpty(paymentTermIds)){
            paymentChannel = getPaymentChannel(saasId,paymentTermIds);
        }
        p2pOrderDTO.setChannelList(paymentChannel);
        p2pOrderDTO.setAdsNo(adsNo);
        boolean amountGet = amount != null && amount.compareTo(BigDecimal.ZERO) > 0;
        boolean quantityGet = quantity != null && quantity.compareTo(BigDecimal.ZERO) > 0;
        if (amountGet) {
            p2pOrderDTO.setAmount(amount.stripTrailingZeros());
        }
        if (quantityGet) {
            p2pOrderDTO.setQuantity(quantity.stripTrailingZeros());
        }
        p2pOrderDTO.setCreated(new Date());
        p2pOrderDTO.setModified(new Date());
        p2pOrderDTO.setSaasId(saasId);
        p2pOrderDTO.setBusiType(busiType);
        WebResponse checkresult = checkp2pOrder(p2pOrderDTO);
        if (!checkresult.isSuccess()) {
            return checkresult;
        }
        p2pOrderDTO.setId(remotep2pOrderService.acquireSeqNo(customer.getId()));
        P2pResponse<P2pOrderDTO> response = remotep2pOrderService.placeOrder(customer, p2pOrderDTO);
        if (!CollectionUtils.isEmpty(paymentTermIds)){
            addBusiness(paymentTermIds,customer.getId(),saasId);
        }
        return WebResponse.result(b -> result(b, response));

    }


    @RequestMapping(value = "/cancel/count", method = RequestMethod.GET)
    @ResponseBody
    public WebResponse cancelOrderCount(@RequestAttribute(SAAS_ID) String saasId,
                                        @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                        HttpServletRequest request) {

        return WebResponse.result(b -> result(b, remotep2pOrderService.cancelOrderCount(customer)));
    }


    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    @ResponseBody
    public WebResponse cancelOrder(@RequestAttribute(SAAS_ID) String saasId,
                                   @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                   @RequestParam(name = "orderId") String orderId,
                                   HttpServletRequest request) {
        log.info("cancelOrder execute start id {}", orderId);
        WebResponse checkResult = p2pConfigService.serviceAvailableCheck(saasId, true,
                false, false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        OrderCancelRequest requestDto = OrderCancelRequest.builder().orderId(orderId).customerId(customer.getId()).bizId(remotep2pOrderService.acquireSeqNo(customer.getId())).build();
        return WebResponse.result(b -> result(b, remotep2pOrderService.cancelOrder(requestDto)));
    }


    @RequestMapping(value = "/payer/confirm", method = RequestMethod.POST)
    @ResponseBody
    public WebResponse payerConfirm(@RequestAttribute(SAAS_ID) String saasId,
                                    @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                    @RequestParam(name = "orderId") String orderId,
                                    HttpServletRequest request) {
        log.info("payerConfirm execute start id {}", orderId);
        return WebResponse.result(b -> result(b, remotep2pOrderService.payerConfirm(orderId, customer.getId())));

    }

    @RequestMapping(value = "/payee/confirm", method = RequestMethod.POST)
    @ResponseBody
    public WebResponse payeeConfirm(@RequestAttribute(SAAS_ID) String saasId,
                                    @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                    @RequestParam(name = "orderId") String orderId,
                                    HttpServletRequest request) {
        log.info("payeeConfirm execute start id {}", orderId);
        WebResponse checkResult = p2pConfigService.serviceAvailableCheck(saasId, true,
                false, false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        PayeeConfirmRequest requestDto = PayeeConfirmRequest.builder().orderId(orderId).customerId(customer.getId()).bizId(remotep2pOrderService.acquireSeqNo(customer.getId())).build();
        return WebResponse.result(b -> result(b, remotep2pOrderService.payeeConfirm(requestDto)));
    }

    @RequestMapping(value = "/appeal", method = RequestMethod.POST)
    @ResponseBody
    public WebResponse orderFreeze(@RequestAttribute(SAAS_ID) String saasId,
                                   @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                   @RequestParam(name = "orderId") String orderId,
                                   @RequestParam(name = "appealReason") String appealReason,
                                   HttpServletRequest request) {

        log.info("orderFreeze execute start id {}", orderId);
        if (StringUtils.isBlank(appealReason) || appealReason.length() > 140) {
            return WebResponse.result(WebResponseEnum.P2P_PARAMETER_LENGTH_INVALID, new String[]{"freezeReason", "max length:500"});
        }
        OrderAppealRequest requestDto = OrderAppealRequest.builder().orderId(orderId).customerId(customer.getId()).appealResult(appealReason).build();
        return WebResponse.result(b -> result(b, remotep2pOrderService.orderAppeal(requestDto)));
    }


    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ResponseBody
    public WebResponse orderDetailQuery(@RequestAttribute(SAAS_ID) String saasId,
                                        @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                        @RequestParam(name = "orderId") String orderId,
                                        HttpServletRequest request) {
        return WebResponse.result(b -> result(b, remotep2pOrderService.orderDetailQuery(orderId, customer.getId())));
    }

    @RequestMapping(value = "/appeal/detail", method = RequestMethod.GET)
    @ResponseBody
    public WebResponse orderAppealQuery(@RequestAttribute(SAAS_ID) String saasId,
                                        @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                        @RequestParam(name = "orderId") String orderId,
                                        HttpServletRequest request) {
        return WebResponse.result(b -> result(b, remotep2pOrderService.orderAppealQuery(orderId, customer.getId())));
    }


    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public WebResponse list(HttpServletRequest request,
                            @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                            @RequestParam(name = "userRole", required = false) String userRole,
                            @RequestParam(name = "merchantId", required = false) String merchantId,
                            @RequestParam(name = "adsNo", required = false) String adsNo,
                            @RequestParam(name = "type", required = false) String type,
                            @RequestParam(name = "fiatCurrency", required = false) String fiatCurrency,
                            @RequestParam(name = "cryptoCurrency", required = false) String cryptoCurrency,
                            @RequestParam(name = "status", required = false) List<String> status,
                            @RequestParam(value = "busiType", required = false) List<String> busiType,
                            @RequestParam(value = "startTime", required = false) Long startTime,
                            @RequestParam(value = "endTime", required = false) Long endTime,
                            @RequestParam(name = "offset", required = true) Integer offset,
                            @RequestParam(name = "limit", required = true) Integer limit) {

        P2pOrderQueryRequest queryRequest = new P2pOrderQueryRequest();
        P2pConstants.UserRole role = P2pConstants.UserRole.USER;
        if (userRole != null) {
            role = P2pConstants.UserRole.get(userRole);
        } else {
            //TODO remove 兼容老版本
            role = (StringUtils.isEmpty(merchantId) ? P2pConstants.UserRole.USER : P2pConstants.UserRole.MERCHANT);

        }
        queryRequest.setUserRole(role);
        queryRequest.setMerchantId(customer.getMerchantId());
        queryRequest.setCustomerId(customer.getId());
        queryRequest.setAdsNo(adsNo);
        queryRequest.setType(type);
        queryRequest.setFiatCurrency(fiatCurrency);
        queryRequest.setCryptoCurrency(cryptoCurrency);
        queryRequest.setStatus(status);
        queryRequest.setBusiType(busiType);
        queryRequest.setStartTime(startTime);
        queryRequest.setEndTime(endTime);
        queryRequest.setOffset(offset);
        queryRequest.setLimit(limit);
        return WebResponse.result(b -> result(b, remotep2pOrderService.orderList(queryRequest)));
    }

    @RequestMapping(value = "/check", method = RequestMethod.GET)
    @ResponseBody
    public WebResponse checkC2C(@RequestAttribute(SAAS_ID) String saasId,
                                @RequestAttribute(CUSTOMER) CustomerExtraDTO customer,
                                HttpServletRequest request) {

        return WebResponse.result(b -> result(b, remotep2pOrderService.checkOwnC2C(saasId, customer.getId())));
    }


    private WebResponse checkp2pOrder(P2pOrderDTO p2pOrderDTO) {

        WebResponse checkResult = p2pConfigService.serviceAvailableCheck(p2pOrderDTO.getSaasId(), true,
                false, false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        if (P2pConstants.TransType.get(p2pOrderDTO.getType()) == null) {
            return WebResponse.result(WebResponseEnum.P2P_INVALID_PARAMETER, "type");
        }

        boolean amountGet = p2pOrderDTO.getAmount() != null && p2pOrderDTO.getAmount().compareTo(BigDecimal.ZERO) > 0;
        boolean quantityGet = p2pOrderDTO.getQuantity() != null && p2pOrderDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0;

        if (amountGet && quantityGet) {
            return WebResponse.result(WebResponseEnum.P2P_INVALID_PARAMETER, "amount and quantity  cannot entered at the same time");
        }

        if (!amountGet && !quantityGet) {
            return WebResponse.result(WebResponseEnum.P2P_INVALID_PARAMETER, "amount and quantity cannot be empty at the same time");
        }

        return WebResponse.success();
    }

    /**
     *  // paymentChannel 有则转换， 无则新增
     * @param saasId
     * @param paymentTermIds
     * @return
     */
    private List<String> getPaymentChannel(String saasId, List<String> paymentTermIds){
        List<String> paymentChannelIds = Lists.newArrayList();;
        P2pResponse<List<P2pPaymentChannelDTO>> response = remotePaymentChannelService.upsert(saasId,paymentTermIds);
        List<P2pPaymentChannelDTO> p2pPaymentChannelDTOS = response.getObj();
        if (p2pPaymentChannelDTOS != null){
            paymentChannelIds.addAll(p2pPaymentChannelDTOS.stream().map(P2pPaymentChannelDTO::getId).collect(Collectors.toList()));
        }
        return  paymentChannelIds;
    }
    /**
     *  addBusiness
     * @param paymentTermIds
     * @return
     */
    private void addBusiness(List<String> paymentTermIds, String customerId,String saasId){
        if (!CollectionUtils.isEmpty(paymentTermIds)){
            paymentTermIds.forEach(id ->{
                try {
                    log.info("P2pOrder=addBusiness-start id:{},customerId:{},saasId:{}", id,customerId, saasId);
                    remotePaymentTermService.modifyBusiness(saasId,customerId,id, PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
                }catch (CustomerException ce) {
                    log.warn("P2pOrder-addBusiness error,id:{},customerId:{},saasId:{}", id,customerId, saasId, ce);
                } catch (Exception e) {
                    log.error("P2pOrder-addBusiness error,id:{},customerId:{},saasId:{}", id,customerId, saasId, e);
                }
            });
        }
    }
}
