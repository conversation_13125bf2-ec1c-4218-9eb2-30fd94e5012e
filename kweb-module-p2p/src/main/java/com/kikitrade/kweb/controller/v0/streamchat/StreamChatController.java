package com.kikitrade.kweb.controller.v0.streamchat;

import org.apache.dubbo.config.annotation.DubboReference;
import com.kikitrade.kweb.controller.p2p.P2pBaseController;
import com.kikitrade.kweb.model.common.WebResponse;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.p2p.model.P2pResponse;
import com.kikitrade.p2p.model.UserDTO;
import com.kikitrade.vibra.api.RemoteImService;
import com.kikitrade.vibra.common.CommonResult;
import com.kikitrade.vibra.model.CustomerUser;
import com.kikitrade.vibra.model.MessageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Controller
@RequestMapping("/im")
public class StreamChatController extends P2pBaseController {

    @Resource
    private KwebBaseService kwebBaseService;
    @DubboReference(check = false)
    private RemoteImService remoteImService;


    @RequestMapping(value = "/messages/{messageId}", method = RequestMethod.GET)
    @ResponseBody
    public WebResponse getMessage(@RequestAttribute(SAAS_ID) String saasId,
                                  @PathVariable(name = "messageId") String messageId,
                                  HttpServletRequest request) {
        CommonResult<MessageInfo> message = remoteImService.getMessage(messageId);
        return WebResponse.result(r -> result(r, P2pResponse.result(message.getCode(), message.getMessage(), message.getData())));
    }

    @RequestMapping(value = "/serviceusers", method = RequestMethod.GET)
    @ResponseBody
    public WebResponse getServiceUsers(@RequestAttribute(SAAS_ID) String saasId,
                                       HttpServletRequest request) {
        CommonResult<List<CustomerUser>> serviceUser = remoteImService.getCustomerUser();
        UserDTO userDTO=new UserDTO();
        userDTO.setUsers(serviceUser.getData().stream().map(f->f.getCustomerId()).collect(Collectors.toList()));
        return WebResponse.result(r -> result(r, P2pResponse.success().setObj(userDTO)));
    }


}
