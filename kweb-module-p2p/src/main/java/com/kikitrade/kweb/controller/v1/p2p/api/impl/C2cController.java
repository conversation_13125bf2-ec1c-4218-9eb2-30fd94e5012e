package com.kikitrade.kweb.controller.v1.p2p.api.impl;

import com.kikitrade.kweb.annotation.V1RestController;
import com.kikitrade.kweb.controller.BaseController;
import com.kikitrade.kweb.controller.v1.p2p.api.C2CApi;
import com.kikitrade.kweb.controller.v1.p2p.delegate.C2CAssetApiDelegate;
import com.kikitrade.kweb.controller.v1.p2p.delegate.C2CImApiDelegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Slf4j
@V1RestController
@RequestMapping("/p2p")
public class C2cController extends BaseController implements C2CApi {

    private final C2CImApiDelegate c2cImApiDelegate;
    private final C2CAssetApiDelegate c2CAssetApiDelegate;

    public C2cController(@Autowired(required = false) C2CImApiDelegate c2cImApiDelegate,
                         @Autowired(required = false) C2CAssetApiDelegate c2CAssetApiDelegate) {
        this.c2cImApiDelegate = Optional.ofNullable(c2cImApiDelegate).orElse(new C2CImApiDelegate() {
        });
        this.c2CAssetApiDelegate = Optional.ofNullable(c2CAssetApiDelegate).orElse(new C2CAssetApiDelegate() {
        });
    }

    @Override
    public C2CImApiDelegate getC2CImDelegate() {
        return c2cImApiDelegate;
    }

    @Override
    public C2CAssetApiDelegate getC2CAssetApiDelegate() {
        return c2CAssetApiDelegate;
    }
}
