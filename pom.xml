<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kweb</artifactId>
        <version>2.6.0-SNAPSHOT</version>
    </parent>

    <groupId>com.kikitrade</groupId>
    <artifactId>quests-web</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>quests-web</name>
    <description>The parent entry of all quests-web modules</description>

    <properties>
        <skipChecks>true</skipChecks>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.property.path>.</project.property.path>
        <spring.version>5.2.8.RELEASE</spring.version>
        <openapi-generator-maven-plugin.version>6.4.0</openapi-generator-maven-plugin.version>
        <springdoc.openapi.webmvc.version>2.0.2</springdoc.openapi.webmvc.version>
    </properties>

    <modules>
        <module>quests-module-common</module>
        <module>quests-module-customer</module>
        <module>quests-module-activity</module>
        <module>quests-module-member</module>
        <module>quests-web-app</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>quests-module-customer</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>quests-module-activity</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kevent-client</artifactId>
                <version>2.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>quests-module-common</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>quests-module-member</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kactivity-api</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kmember-api</artifactId>
                <version>2.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kactivity-model</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>0.12.3</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>0.12.3</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId> <!-- or jjwt-gson if Gson is preferred -->
                <version>0.12.3</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kcustomer-api</artifactId>
                <version>2.0.5-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kwallet-api</artifactId>
                <version>2.1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>ktrade-api</artifactId>
                <version>2.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>io.getstream.client</groupId>
                <artifactId>stream-java</artifactId>
                <version>3.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.openapitools</groupId>
                <artifactId>jackson-databind-nullable</artifactId>
                <version>0.2.2</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>2.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.jms</groupId>
                        <artifactId>jms</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 内部 kweb 服务依赖 -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.openapi.webmvc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.openapi.webmvc.version}</version>
            </dependency>
            <!--Spring 授权服务器-->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-oauth2-authorization-server</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <project.profile.id>local</project.profile.id>
            </properties>
            <build>
                <filters>
                    <filter>${project.property.path}/config-local.properties</filter>
                </filters>
            </build>
            <repositories>
                <repository>
                    <id>snapshot</id>
                    <!--<url>http://nexus.marathon.l4lb.thisdcos.directory:8081/repository/maven-snapshots/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/maven-snapshots/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>public</id>
                    <!--<url>http://*************:8081/nexus/groups/public/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>releases</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>

                <repository>
                    <id>maven-public</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                </repository>

                <repository>
                    <id>spring-jcenter-cache</id>
                    <!--<url>http://repo.spring.io/jcenter-cache/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/spring-jcenter-cache</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>repo.maven.apache.org</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/repo.maven.apache.org</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus.dipbit.xyz/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshot</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus.dipbit.xyz/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <pluginRepositories>
                <pluginRepository>
                    <id>maven-public</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                </pluginRepository>
                <pluginRepository>
                    <id>repo.maven.apache.org</id>
                    <url>https://nexus.dipbit.xyz/repository/repo.maven.apache.org</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.yaml</include>
                    <include>**/*.json</include>
                    <include>**/*.txt</include>
                    <include>**/*.png</include>
                    <include>log4j2.xml</include>
                    <include>dubbo.json</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.ftl</include>
                    <include>**/*.vm</include>
                    <include>META-INF/**</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>
