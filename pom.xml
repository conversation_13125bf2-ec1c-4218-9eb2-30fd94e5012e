<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-framework</artifactId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.drex</groupId>
    <artifactId>drex-activity</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>drex-activity</name>

    <properties>
        <skipChecks>true</skipChecks>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <drex.activity.version>0.0.1-SNAPSHOT</drex.activity.version>
        <com.alibaba.nacos.version>2.2.1-RC</com.alibaba.nacos.version>
        <project.property.path>..</project.property.path>
        <kiki.version>3.0.0-SNAPSHOT</kiki.version>
        <kevent.version>2.0.2-SNAPSHOT</kevent.version>
        <hutool.version>5.8.16</hutool.version>
        <commons.maths3.version>3.6.1</commons.maths3.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <common-lang3.version>3.12.0</common-lang3.version>
        <curator.version>5.1.0</curator.version>
        <twitter.api.version>2.0.3</twitter.api.version>
    </properties>

    <modules>
        <module>activity-api</module>
        <module>activity-dal</module>
        <module>activity-model</module>
        <module>activity-service</module>
        <module>activity-web</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>activity-api</artifactId>
                <version>${drex.activity.version}</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>activity-dal</artifactId>
                <version>${drex.activity.version}</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>activity-model</artifactId>
                <version>${drex.activity.version}</version>
            </dependency>
            <dependency>
                <groupId>com.drex</groupId>
                <artifactId>activity-service</artifactId>
                <version>${drex.activity.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.kikitrade</groupId>-->
<!--                <artifactId>kevent-client</artifactId>-->
<!--                <version>${kevent.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${com.alibaba.nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>${com.alibaba.nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kiki-dubbo-deployment-tag-aware-spring-boot-starter</artifactId>
                <version>${kiki.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kiki-observability-tracing-spring-boot-starter</artifactId>
                <version>${kiki.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kiki-observability-metrics-spring-boot-starter</artifactId>
                <version>${kiki.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kiki-ons-spring-boot-starter</artifactId>
                <version>${kiki.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>${commons.maths3.version}</version>
            </dependency>
            <!-- MapStruct for object mapping -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${common-lang3.version}</version>
            </dependency>
            <!-- Apache Curator for ZooKeeper -->
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.twitter</groupId>
                <artifactId>twitter-api-java-sdk</artifactId>
                <version>${twitter.api.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.mouzt</groupId>
                <artifactId>bizlog-sdk</artifactId>
                <version>3.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.1</version>
            </dependency>
            <!-- oss使用aliyun-core 3.4.0不能升级为最新版本 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.10</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <project.profile.id>local</project.profile.id>
            </properties>
            <build>
                <filters>
                    <filter>${project.property.path}/config-local.properties</filter>
                </filters>
            </build>
            <repositories>
                <repository>
                    <id>snapshot</id>
                    <!--<url>http://nexus.marathon.l4lb.thisdcos.directory:8081/repository/maven-snapshots/</url>-->
                    <url>https://nexus-drex.dipbit.xyz/repository/maven-snapshots/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>public</id>
                    <!--<url>http://*************:8081/nexus/groups/public/</url>-->
                    <url>https://nexus-drex.dipbit.xyz/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>releases</id>
                    <url>https://nexus-drex.dipbit.xyz/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>

                <repository>
                    <id>maven-public</id>
                    <url>https://nexus-dipbit.xyz/repository/maven-public/</url>
                </repository>

                <repository>
                    <id>spring-jcenter-cache</id>
                    <!--<url>http://repo.spring.io/jcenter-cache/</url>-->
                    <url>https://nexus-drex.dipbit.xyz/repository/spring-jcenter-cache</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>repo.maven.apache.org</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus-drex.dipbit.xyz/repository/repo.maven.apache.org</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus-drex.dipbit.xyz/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshot</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus-drex.dipbit.xyz/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <pluginRepositories>
                <pluginRepository>
                    <id>maven-public</id>
                    <url>https://nexus-drex.dipbit.xyz/repository/maven-public/</url>
                </pluginRepository>
                <pluginRepository>
                    <id>repo.maven.apache.org</id>
                    <url>https://nexus-drex.dipbit.xyz/repository/repo.maven.apache.org</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.yaml</include>
                    <include>**/*.json</include>
                    <include>**/*.txt</include>
                    <include>**/*.png</include>
                    <include>log4j2.xml</include>
                    <include>dubbo.json</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.ftl</include>
                    <include>**/*.vm</include>
                    <include>META-INF/**</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>
