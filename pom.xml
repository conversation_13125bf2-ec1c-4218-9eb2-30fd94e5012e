<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-framework</artifactId>
        <version>2.5.0-SNAPSHOT</version>
    </parent>
    <groupId>org.example</groupId>
    <artifactId>learn</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <web3j.version>4.10.3</web3j.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>core</artifactId>
            <version>${web3j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>6.0.7</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.kikitrade</groupId>-->
        <!--            <artifactId>kiki-observability-tracing-spring-boot-starter</artifactId>-->
        <!--            <version>2.5.1-SNAPSHOT</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>3.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>2.0.6</version>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework</groupId>-->
        <!--            <artifactId>spring-test</artifactId>-->
        <!--            <version>6.0.4</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>6.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.34</version>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>quests-api-client</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
            <version>4.7.1</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.web3j</groupId>
                <artifactId>web3j-maven-plugin</artifactId>
                <version>4.11.1</version>
                <configuration>
                    <packageName>org.example.contract</packageName>
                    <sourceDestination>src/main/java/generated</sourceDestination>
                    <nativeJavaType>true</nativeJavaType>
                    <outputFormat>java,bin</outputFormat>
                    <soliditySourceFiles>
                        <directory>src/main/resources</directory>
                        <includes>
                            <include>**/*.sol</include>
                        </includes>
                    </soliditySourceFiles>
                    <abiSourceFiles>
                        <directory>src/main/resources</directory>
                        <includes>
                            <include>**/*.json</include>
                        </includes>
                    </abiSourceFiles>
                    <outputDirectory>
                        <java>src/main/java</java>
                        <bin>src/bin/generated</bin>
                        <abi>src/abi/generated</abi>
                    </outputDirectory>
                    <contract>
                        <includes>
                            <include>greeter</include>
                        </includes>
                        <excludes>
                            <exclude>mortal</exclude>
                        </excludes>
                    </contract>
                    <pathPrefixes>
                        <pathPrefix>dep=../dependencies</pathPrefix>
                    </pathPrefixes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>