package com.kikitrade.asset.dal.tablestore.model.constant;

import java.util.regex.Pattern;

/**
 * AssetLogOtsConstant
 *
 * <AUTHOR>
 * @create 2022/7/27 2:19 下午
 * @modify
 */
public interface AssetLedgerOtsConstant {

    Pattern TABLE_NAME_PATTERN = Pattern.compile("asset_ledger_\\d+");

    String TABLE_NAME = "asset_ledger";

    String ID = "id";
    String ASSET_ID = "asset_id";
    String AMOUNT = "amount";
    String OPERATE_ID = "operate_id";
    String BUSINESS_TYPE = "business_type";
    String OPERATE_TYPE = "operate_type";
    String CREATED = "created";
    String MENU = "menu";
    String CUSTOMER_ID = "customer_id";
    String NAMESPACE = "namespace";
    String AVAILABLE = "available";
    String FROZEN = "frozen";
    String VISIBLE = "visible";
    String SAAS_ID = "saas_id";
    String TS = "ts";
    String DESC = "desc_i18n";

    int COIN_START_OPERATE_TYPE = 2;

}
