package com.kikitrade.asset.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

import static com.kikitrade.asset.dal.tablestore.model.constant.AssetFreezeLedgerOtsConstant.*;

/**
 * AssetFreezeLedger readonly data model
 *
 * <AUTHOR>
 * @create 2022/7/27 2:19 下午
 * @modify
 */
@Data
@Table(name = TABLE_NAME)
public class AssetFreezeLedgerReadonlyDO implements Comparable, Serializable {

    @PartitionKey(name = ID)
    private String id;

    @Column(name = ASSET_ID)
    private String assetId;

    @Column(name = NAMESPACE)
    private String namespace;

    @Column(name = CUSTOMER_ID)
    private String customerId;

    /**
     * 业务ID
     */
    @Column(name = BUSINESS_ID)
    private String businessId;

    /**
     * 业务类型
     */
    @Column(name = BUSINESS_TYPE)
    private Long businessType;

    /**
     * 冻结金额
     */
    @Column(name = AMOUNT)
    private BigDecimal amount;

    @Column(name = CREATED)
    private Long created;

    @Column(name = MODIFIED)
    private Long modified;

    @Column(name = SAAS_ID)
    private String saasId;

    @Override
    public int compareTo(Object o) {
        return this.id.compareTo(((AssetFreezeLedgerReadonlyDO) o).getId());
    }
}
