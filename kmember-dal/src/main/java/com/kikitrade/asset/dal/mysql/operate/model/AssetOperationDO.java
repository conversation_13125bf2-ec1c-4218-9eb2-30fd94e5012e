package com.kikitrade.asset.dal.mysql.operate.model;

import com.kikitrade.framework.mybatis.BaseModelDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.util.List;

/**
 * Asset Operation Data Object
 * <p>
 * daily table
 *
 * <AUTHOR>
 * @create 2022/7/26 8:06 下午
 * @modify
 */
@Data
public class AssetOperationDO extends BaseModelDO {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    private String id;

    @Column(name = "customer_id")
    private String customerId;

    @Column(name = "business_id")
    private String businessId;

    @Column(name = "business_type")
    private Long businessType;

    @Column(name = "original_business_id")
    private String originalBusinessId;

    @Column(name = "status")
    private String status;

    @Column(name = "menu")
    private String menu;

    @Column(name = "strict_check")
    private Boolean strictCheck;

    @Column(name = "freeze_asset")
    private Boolean freezeAsset;

    @Column(name = "ledger_ids")
    private String ledgerIds;

    @Transient
    private List<AssetLedgerDO> assetLedgers;

    private boolean general;

    private boolean originalGeneral;

}
