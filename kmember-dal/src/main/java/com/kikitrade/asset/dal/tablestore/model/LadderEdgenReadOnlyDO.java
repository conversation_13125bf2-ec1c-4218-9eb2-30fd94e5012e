package com.kikitrade.asset.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * LadderEdgenDO
 *
 * <AUTHOR>
 * @create 2025/4/28
 */
@Data
@Table(name = "member_ladder_edgen")
public class LadderEdgenReadOnlyDO implements Serializable {

    public static final String SEARCH_INDEX_LADDER_EDGEN = "search_index_ladder_edgen";

    /**
     * 用户ID
     */
    @PartitionKey(name = "uid")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "uid")
    private String uid;

    /**
     * 业务编号
     */
    @PartitionKey(name = "saas_id", value = 1)
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "saas_id")
    private String saasId;

    /**
     * 资产类型
     */
    @PartitionKey(name = "asset_type", value = 2)
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "asset_type", fieldType = FieldType.LONG)
    private Integer assetType;

    /**
     * Aura owner id
     */
    @Column(name = "cid")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "cid")
    private String cid;

    /**
     * 推特用户展示名
     */
    @Column(name = "twitter_display_name")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "twitter_display_name")
    private String twitterDisplayName;

    /**
     * 推特用户handle
     */
    @Column(name = "twitter_handle")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "twitter_handle")
    private String twitterHandle;

    /**
     * 推特用户头像
     */
    @Column(name = "twitter_avatar")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "twitter_avatar")
    private String twitterAvatar;

    /**
     * OSP用户名
     */
    @Column(name = "osp_handle")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "osp_handle")
    private String ospHandle;

    /**
     * OSP用户头像
     */
    @Column(name = "osp_avatar")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "osp_avatar")
    private String ospAvatar;

    /**
     * 参与aura活动的帖子数,distribution+contribution
     */
    @Column(name = "post_count")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "post_count", fieldType = FieldType.LONG)
    private Long postCount;

    /**
     * 当前排名
     */
    @Column(name = "ranking")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "ranking", fieldType = FieldType.LONG)
    private Long ranking;

    /**
     * 当前排名参与总数
     */
    @Column(name = "ranking_count")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "ranking_count", fieldType = FieldType.LONG)
    private Long rankingCount;

    /**
     * 自己参加活动的aura分
     */
    @Column(name = "earned_point", type = Column.Type.DOUBLE)
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "earned_point", fieldType = FieldType.DOUBLE)
    private BigDecimal earnedPoint;

    /**
     * 邀请继承aura分
     */
    @Column(name = "referral_point", type = Column.Type.DOUBLE)
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "referral_point", fieldType = FieldType.DOUBLE)
    private BigDecimal referralPoint;

    /**
     * 当前总aura分
     */
    @Column(name = "total_point", type = Column.Type.DOUBLE)
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "total_point", fieldType = FieldType.DOUBLE)
    private BigDecimal totalPoint;

    /**
     * 创建时间
     */
    @Column(name = "created")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "created", fieldType = FieldType.LONG)
    private Long created;

    /**
     * 更新时间
     */
    @Column(name = "modified")
    @SearchIndex(name = SEARCH_INDEX_LADDER_EDGEN, column = "modified", fieldType = FieldType.LONG)
    private Long modified;
} 