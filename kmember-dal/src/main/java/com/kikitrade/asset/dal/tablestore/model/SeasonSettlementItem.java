package com.kikitrade.asset.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.Index;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/16 14:03
 */
@Data
@Table(name = "season_settlement_item")
public class SeasonSettlementItem implements Serializable {

    public static final String SEARCH_SEASON_SETTLEMENT = "search_season_settlement";

    @PartitionKey(name = "saas_id")
    @SearchIndex(name = SEARCH_SEASON_SETTLEMENT, column = "saas_id")
    private String saasId;
    @PartitionKey(name = "season", value = 1)
    @SearchIndex(name = SEARCH_SEASON_SETTLEMENT, column = "season")

    private String season;
    @PartitionKey(name = "customer_id", value = 2)
    @SearchIndex(name = SEARCH_SEASON_SETTLEMENT, column = "customer_id")
    private String customerId;
    @Column(name = "is_settlement", type = Column.Type.BOOLEAN, isDefined = true)
    @SearchIndex(name = SEARCH_SEASON_SETTLEMENT, column = "is_settlement", fieldType = FieldType.BOOLEAN)
    private Boolean isSettlement;

    @Column(name = "point", type = Column.Type.DOUBLE, isDefined = true)
    @SearchIndex(name = SEARCH_SEASON_SETTLEMENT, column = "point", fieldType = FieldType.DOUBLE)
    private BigDecimal point;

    @Column(name = "vip_count", type = Column.Type.INTEGER, isDefined = true)
    private Integer vipCount;

    @Column(name = "customer_suffix", type = Column.Type.INTEGER, isDefined = true)
    private Integer customerSuffix;
}
