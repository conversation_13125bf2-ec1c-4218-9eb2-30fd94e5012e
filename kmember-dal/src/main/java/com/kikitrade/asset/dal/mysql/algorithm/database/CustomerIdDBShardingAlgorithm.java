package com.kikitrade.asset.dal.mysql.algorithm.database;

import com.kikitrade.asset.dal.mysql.algorithm.AlgorithmConstants;
import io.shardingjdbc.core.api.algorithm.sharding.PreciseShardingValue;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;

/**
 * CustomerIdDBShardingAlgorithm
 *
 * <AUTHOR>
 * @create 2022/7/28 8:01 下午
 * @modify
 */
@Slf4j
public final class CustomerIdDBShardingAlgorithm extends BaseDBShardingAlgorithm {

    @Override
    public String doSharding(final Collection<String> availableTargetNames, final PreciseShardingValue<String> shardingValue) {
        return doShardingBasedOnCustomerId(availableTargetNames, AlgorithmConstants.COMMON_FIELD_CUSTOMER_ID, shardingValue);
    }

}
