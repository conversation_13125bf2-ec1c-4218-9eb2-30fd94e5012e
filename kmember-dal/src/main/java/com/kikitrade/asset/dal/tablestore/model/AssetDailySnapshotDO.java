package com.kikitrade.asset.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AssetDTO
 *
 * <AUTHOR>
 * @create 2022/7/26 9:48 上午
 * @modify
 */
@Data
@Table(name = "asset_daily_snapshot")
public class AssetDailySnapshotDO implements Serializable {

    public static final String ASSET_DAILY_SNAPSHOT_SEARCH_INDEX = "asset_daily_snapshot_search_index";

    @PartitionKey(name = "id")
    @SearchIndex(name = ASSET_DAILY_SNAPSHOT_SEARCH_INDEX, column = "id")
    private String id;

    @PartitionKey(name = "date", value = 1)
    @SearchIndex(name = ASSET_DAILY_SNAPSHOT_SEARCH_INDEX, column = "date")
    private String date;

    @Column(name = "saas_id", isDefined = true)
    @SearchIndex(name = ASSET_DAILY_SNAPSHOT_SEARCH_INDEX, column = "saas_id")
    private String saasId;

    @Column(name = "customer_id", isDefined = true)
    @SearchIndex(name = ASSET_DAILY_SNAPSHOT_SEARCH_INDEX, column = "customer_id")
    private String customerId;

    @Column(name = "asset_type", isDefined = true, type = Column.Type.INTEGER)
    @SearchIndex(name = ASSET_DAILY_SNAPSHOT_SEARCH_INDEX, column = "asset_type", fieldType = FieldType.LONG)
    private Long assetType;

    @Column(name = "available", isDefined = true, type = com.kikitrade.framework.ots.annotations.Column.Type.DOUBLE)
    @SearchIndex(name = ASSET_DAILY_SNAPSHOT_SEARCH_INDEX, column = "available", fieldType = FieldType.DOUBLE)
    private BigDecimal available;

}