package com.kikitrade.asset.dal.mysql.algorithm.table;

import com.kikitrade.asset.dal.mysql.algorithm.AlgorithmConstants;
import io.shardingjdbc.core.api.algorithm.sharding.PreciseShardingValue;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;

/**
 * Business Id TableShardingAlgorithm
 *
 * <AUTHOR>
 * @create 2022/7/28 7:26 下午
 * @modify
 */
@Slf4j
public final class BusinessIdTableShardingAlgorithm extends BaseKeyTableShardingAlgorithm {

    @Override
    public String doSharding(final Collection<String> availableTargetNames, final PreciseShardingValue<String> shardingValue) {
        return doSharding(availableTargetNames, AlgorithmConstants.BUSINESS_ID, shardingValue);
    }

}


