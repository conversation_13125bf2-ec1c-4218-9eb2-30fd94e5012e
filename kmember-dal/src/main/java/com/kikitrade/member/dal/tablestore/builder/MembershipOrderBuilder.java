package com.kikitrade.member.dal.tablestore.builder;

import com.kikitrade.member.dal.tablestore.model.MembershipOrderDO;

/**
 * MembershipDO ots spec
 *
 * <AUTHOR>
 * @create 2022/7/22 9:56 上午
 * @modify
 */
public interface MembershipOrderBuilder {

    boolean create(MembershipOrderDO order);

    boolean createNotExist(MembershipOrderDO order);

    boolean modify(MembershipOrderDO order);

    MembershipOrderDO query(MembershipOrderDO order);

}
