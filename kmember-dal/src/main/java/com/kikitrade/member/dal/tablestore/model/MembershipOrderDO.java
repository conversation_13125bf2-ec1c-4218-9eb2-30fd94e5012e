package com.kikitrade.member.dal.tablestore.model;

import com.dipbit.dtm.client.domain.order.BaseStateModel;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import com.kikitrade.framework.ots.annotations.Transient;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * MembershipOrderDO
 *
 * <AUTHOR>
 * @create 2022/7/21 7:43 下午
 * @modify
 */
@Table(name = "membership_order")
@Data
public class MembershipOrderDO implements BaseStateModel {

    // 	会员ID
    @PartitionKey(name = "customer_id")
    private String customerId;

    // 	会员订单ID
    @PartitionKey(name = "order_id", value = 1)
    private String orderId;

    //命名空间，平台会员为0，其他为对应明星id
    @Column(name = "namespace")
    private String namespace;

    // 	会员订单类型
    @Column(name = "type")
    private String type;

    // 	状态
    @Column(name = "status")
    private String status;

    // 	FSM 状态
    @Column(name = "state")
    private String state;

    // 	FSM tenant
    @Column(name = "tenant")
    private String tenant;

    // 	FSM version
    @Column(name = "version")
    private Long version;

    // 	会员等级
    @Column(name = "level")
    private String level;

    //范围：平台会员 明星会员
    @Column(name = "scope")
    private String scope;


    //生成会员订单时的等级条件类型
    @Column(name = "condition_type")
    private String conditionType;

    // 升级需要的资产数量
    @Column(name = "amount")
    private BigDecimal amount;

    //币种id
    @Column(name = "currency_id")
    private Integer currencyId; // 币种，如果可以提供

    //币种
    @Column(name = "currency")
    private String currency; // 不是币种的，使用0

    // 	生效时间
    @Column(name = "effect_time")
    private Date effectTime;

    // 	失效时间
    @Column(name = "expire_time")
    private Date expireTime;

    // 	虚拟资产账户ID
    @Column(name = "asset_id")
    private String assetId;

    // 	创建时间
    @Column(name = "created")
    private Date created;

    // 	修改时间
    @Column(name = "modified")
    private Date modified;

    // 	Saas ID
    @Column(name = "saas_id")
    private String saasId;

    // 是否自动续期
    @Column(name = "autoRenewal")
    private Boolean autoRenewal;

    // 升级前的等级
    @Column(name = "prior_level")
    private String priorLevel;

    private String desc;

    // 链上交易监听orderId
    private String ethOrderId;


}
