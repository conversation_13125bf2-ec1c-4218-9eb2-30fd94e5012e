package com.kikitrade.member.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.member.dal.tablestore.builder.QuestsLadderBuilder;
import com.kikitrade.member.dal.tablestore.model.QuestsPointRankingDO;
import com.kikitrade.member.dal.tablestore.model.RankingOtsContant;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/1 16:52
 */
@Component
public class QuestsLadderBuilderImpl extends WideColumnStoreBuilder<QuestsPointRankingDO> implements QuestsLadderBuilder {

    @PostConstruct
    public void init() {
        super.init(QuestsPointRankingDO.class);
    }

    @Override
    public Boolean update(QuestsPointRankingDO questsPointRankingDO, String column){
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(questsPointRankingDO, List.of(column), condition);
    }

    @Override
    public QuestsPointRankingDO queryByCustomerId(String season, String cycle, String customerId){
        QuestsPointRankingDO query = new QuestsPointRankingDO();
        query.setSeason(season);
        query.setCycle(cycle);
        query.setCustomerId(customerId);
        return super.getRow(query);
    }

    @Override
    public Boolean existByCycle(String saasId, String season, String cycle) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(RankingOtsContant.SAAS_ID, saasId))
                .must(QueryBuilders.term(RankingOtsContant.SEASON, season))
                .must(QueryBuilders.term(RankingOtsContant.CYCLE, cycle));

        return super.searchOne(boolQuery.build(), RankingOtsContant.SEARCH_BADGE) != null;
    }

    @Override
    public Page<QuestsPointRankingDO> queryByCycleAndLevel(String saasId, String season, String cycle, Integer level, int offset, int limit, Integer group){
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(RankingOtsContant.SAAS_ID, saasId))
                .must(QueryBuilders.term(RankingOtsContant.SEASON, season))
                .must(QueryBuilders.term(RankingOtsContant.CYCLE, cycle))
                .must(QueryBuilders.term(RankingOtsContant.LEVEL, level));

        if(group != null){
            boolQuery.must(QueryBuilders.term(RankingOtsContant.GROUP, group));
        }
        Sort sort = new Sort(Arrays.asList(new FieldSort(RankingOtsContant.RANK, SortOrder.ASC)));
        return super.pageSearchQuery(boolQuery.build(), sort, offset, limit, RankingOtsContant.SEARCH_BADGE);
    }

    @Override
    public TokenPage<QuestsPointRankingDO> tokenQueryByCycleAndLevel(String saasId, String season, String cycle, Integer level, String nextToken, int limit, Integer group){
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(RankingOtsContant.SAAS_ID, saasId))
                .must(QueryBuilders.term(RankingOtsContant.SEASON, season))
                .must(QueryBuilders.term(RankingOtsContant.CYCLE, cycle))
                .must(QueryBuilders.term(RankingOtsContant.LEVEL, level));

        if(group != null){
            boolQuery.must(QueryBuilders.term(RankingOtsContant.GROUP, group));
        }
        Sort sort = new Sort(Arrays.asList(new FieldSort(RankingOtsContant.RANK, SortOrder.ASC)));
        return super.pageSearchQuery(boolQuery.build(), sort, nextToken, limit, RankingOtsContant.SEARCH_BADGE);
    }

    @Override
    public List<QuestsPointRankingDO> queryLastByCustomerId(String saasId, String customerId) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(RankingOtsContant.SAAS_ID, saasId))
                .must(QueryBuilders.term(RankingOtsContant.CUSTOMER_ID, customerId));
        Sort sort = new Sort(Arrays.asList(new FieldSort(RankingOtsContant.SEASON, SortOrder.DESC), new FieldSort(RankingOtsContant.CYCLE, SortOrder.DESC)));
        PageResult pageResult = super.pageSearch(boolQuery.build(), sort, 0, 1, RankingOtsContant.SEARCH_BADGE);
        return pageResult != null ? pageResult.getRows() : null;
    }
}
