package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.mysql.model.ActivityRuleMap;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.service.common.model.JsonResult;
import org.jeasy.rules.api.Rule;

import java.util.List;


public interface ActivityCommonService {


    List<Activity> checkRules(ActivityMessage activityMassage, List<Activity> activityList) throws Exception;

    boolean doActions(ActivityMessage activityMassage, List<Activity> activityList);

    List<Activity> getActivityListFromRedis(ActivityMessage activityMassage, List<String> keyList);

    void saveRecords(ActivityMessage activityMassage, List<Activity> activityList, Integer record_status, String remark) throws Exception;

    List<String> getKeyList(List<Rule> ruleList);

    List<Activity> doCheck(ActivityMessage activityMassage,
                           List<Activity> activityList);

    boolean timesCheck(ActivityMessage request, Activity activity);

    boolean actionPrepare(ActivityMessage activityMassage, List<Activity> activityList);

    String getKey(List<ActivityRuleMap> ruleList);

    JsonResult redisSave(Activity activity);

    String getRedisKey(Activity activity);

    String getRedisKey(List<ActivityRuleMap> activityRuleMapList);

    JsonResult redisDelete(Activity activity);

    long redisDelete(String key);

    List<Activity> redisGet(String key);

    List<Activity> getProcessingActivity(String saasId);


}
