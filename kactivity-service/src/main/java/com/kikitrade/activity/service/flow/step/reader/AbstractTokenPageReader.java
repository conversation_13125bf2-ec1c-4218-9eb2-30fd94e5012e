package com.kikitrade.activity.service.flow.step.reader;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.support.AbstractItemCountingItemStreamItemReader;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

import java.util.List;

/**
 * @author: penuel
 * @date: 2022/3/23 10:27
 * @desc: 依赖ots的tokenPage翻页方式
 */
@Slf4j
public abstract class AbstractTokenPageReader<T> extends AbstractItemCountingItemStreamItemReader<T> implements InitializingBean {

    private volatile boolean initialized = false;

    private int pageSize = 10;

    private volatile int current = 0;
    private volatile PrimaryKey nextToken;
    protected volatile List<T> results;

    private Object lock = new Object();

    public AbstractTokenPageReader() {
        setName(ClassUtils.getShortName(AbstractTokenPageReader.class));
    }

    /**
     * 进行分页读取
     * @return nextToken
     */
    protected abstract PrimaryKey doReadPage();


    @Override
    public void afterPropertiesSet() throws Exception {
        Assert.isTrue((getPageSize() > 0), "pageSize must be greater than zero");
    }

    @Override
    protected void doOpen() throws Exception {
        Assert.state(!initialized, "Cannot open an already opened ItemReader, call close first");
        initialized = true;
    }

    @Override
    @Nullable
    protected T doRead() throws Exception {
        synchronized (this.lock) {

            if (results == null || (current >= getPageSize() && nextToken != null)) {
                log.debug("Reading page by nextToken " + nextToken);

                //第二次查询前先置空list，防止，子类查询为空，未主动置空results
                if(results != null){
                    results.clear();
                }
                nextToken = doReadPage();

                if (current >= getPageSize()) {
                    current = 0;
                }
            }

            int next = current++;
            if (null != results && next < results.size()) {
                return results.get(next);
            } else {
                return null;
            }
        }
    }

    @Override
    protected void doClose() throws Exception {
        synchronized (this.lock) {
            initialized = false;
            current = 0;
            nextToken = nextToken;
            results = null;
        }
    }

    public PrimaryKey getNextToken() {
        return nextToken;
    }

    protected int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
