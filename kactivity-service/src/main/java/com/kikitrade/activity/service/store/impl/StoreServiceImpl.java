package com.kikitrade.activity.service.store.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryDetailConfigDao;
import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryPoolConfigDao;
import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryStoreLogDao;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryPoolConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryStoreLog;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.model.StoreInfo;
import com.kikitrade.activity.service.store.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class StoreServiceImpl implements StoreService {

    @Resource
    private ActivityLotteryPoolConfigDao activityLotteryPoolConfigDao;
    @Resource
    private ActivityLotteryDetailConfigDao activityLotteryDetailConfigDao;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private ActivityLotteryStoreLogDao activityLotteryStoreLogDao;

    /**
     * 查询库存所在仓库
     *
     * @param now
     * @param drew
     * @return
     */
    @Override
    public StoreInfo getStore(long now, ActivityLotteryDetailConfig drew) {
        //添加库存锁
        String valid = TimeUtil.getDataStr(TimeUtil.parseUnittime(now), TimeUtil.YYYYMM);
        //查询库存
        Integer day = Integer.parseInt(TimeUtil.getDataStr(TimeUtil.parseUnittime(now), TimeUtil.DD));
        StoreInfo storeInfo;
        if("POINT".equalsIgnoreCase(drew.getCurrency()) || drew.getIsLow()){
            storeInfo = getStoreNum(drew.getId(), "1");
        }else{
            int days = getDays(valid);
            int poolNum = poolNum(drew.getNum(), days);
            storeInfo = getStoreNum(drew.getId(), String.valueOf(poolNo(day, days, poolNum)));
        }
        return storeInfo;
    }

    /**
     * 删除库存
     *
     * @param skuId
     * @param itemId
     * @param storeNo
     * @return
     */
    @Override
    public boolean decreaseStore(String skuId, String storeNo, String itemId) {

        boolean successSub = activityLotteryPoolConfigDao.decreaseStoreNum(skuId, storeNo) > 0;
        if(successSub){
            boolean success = activityLotteryDetailConfigDao.decreaseStoreNum(skuId) > 0;
            if(success){
                ActivityLotteryStoreLog log = new ActivityLotteryStoreLog();
                log.setId(itemId);
                log.setTime(new Date().getTime());
                log.setCreated(new Date());
                log.setModified(new Date());
                log.setStatus(0);
                log.setSaasId(kactivityProperties.getSaasId());
                return activityLotteryStoreLogDao.insert(log) > 0;
            }
        }
        return false;
    }

    /**
     * 增加库存
     *
     * @param skuId
     * @param storeNo
     * @param num
     * @return
     */
    @Override
    public boolean increaseStore(String skuId, String storeNo, Integer num) {
        boolean successSub = activityLotteryPoolConfigDao.increaseStoreNum(skuId, storeNo, num) > 0;
        return successSub && activityLotteryDetailConfigDao.increaseStoreNum(skuId, num) > 0;
    }

    private StoreInfo getStoreNum(String sku, String storeNo) {

        Example example = new Example(ActivityLotteryPoolConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("drawId", sku);
        List<ActivityLotteryPoolConfig> poolConfigs = activityLotteryPoolConfigDao.selectByExample(example);
        if(CollectionUtils.isEmpty(poolConfigs)){
            //库存无限
            return null;
        }
        Optional<ActivityLotteryPoolConfig> current = poolConfigs.stream().filter(config -> config.getPoolNo().equals(storeNo)).findFirst();
        if(current.isPresent() && current.get().getRemainNum() > 0){
            //存在库存
            return new StoreInfo(sku, storeNo, current.get().getRemainNum());
        }else{
            log.info("其他库存：{}", poolConfigs);
            for(ActivityLotteryPoolConfig store : poolConfigs){
                if(Integer.parseInt(store.getPoolNo()) < Integer.parseInt(storeNo) && store.getRemainNum() > 0){
                    return new StoreInfo(sku, store.getPoolNo(), store.getRemainNum());
                }
            }
        }
        return new StoreInfo(sku, storeNo, 0);
    }

    /**
     * 获取当前月天数
     * @param date
     * @return
     */
    private int getDays(String date){
        return TimeUtil.getDaysOfMonth(TimeUtil.parse(date, new SimpleDateFormat("yyyy-MM")));
    }

    /**
     * 计算子奖池数量
     * @param num
     * @param days
     * @return
     */
    private int poolNum(int num, int days){
        if(num > days){
            return days;
        }else{
            days = days == 31 ? 30 : (days == 29 ? 28 : days);
            for(int i = num; i >= 1; i--){
                if(days % i == 0){
                    return i;
                }
            }
        }
        return 1;
    }

    /**
     * 今日命中的奖池号
     * @param current
     * @param days
     * @param poolNum
     * @return
     */
    private int poolNo(int current, int days ,int poolNum){
        int avg = days / poolNum;
        if(current % avg != 0){
            return Math.min(current/avg + 1, poolNum);
        }else{
            return Math.min(current/avg, poolNum);
        }
    }
}
