package com.kikitrade.activity.service.common.strategy.twitter;

import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/3 17:46
 */

@Component
@Slf4j
public class TwitterAuthRejectStrategy implements TwitterSaasStrategyService {

    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public String strategy() {
        return SaasStrategyConstant.TwitterStrategyEnum.REJECT.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        if(request.getAuthId() != null && request.getLoginTwitterId() != null){
            if(request.getAuthId().equals(request.getLoginTwitterId())){
                //登陆和授权不是同一个twitter账户
                List<TCustomerDTO> tCustomerBySocial = remoteCustomerBindService.findTCustomerBySocial(request.getSaasId(), ActivityTaskConstant.OpenSocialEnum.twitter.name(), request.getAuthId());
                if(CollectionUtils.isNotEmpty(tCustomerBySocial)){
                    List<TCustomerDTO> customerDTOS = tCustomerBySocial.stream().filter(social -> !request.getCustomerId().equals(social.getUid())).toList();
                    if(CollectionUtils.isNotEmpty(customerDTOS)){
                        // 如果配置了，其他人已绑定时报错的开关，这里抛出异常
                        throw new ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());
                    }
                }
            }else{
                log.info("auth_login_not_same:{}", request);
                throw new com.kikitrade.activity.model.exception.ActivityException(ActivityResponseCode.AUTH_NO_SAME_LOGIN, ActivityResponseCode.AUTH_NO_SAME_LOGIN.getKey());
            }
        } else {
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(request.getSaasId(), request.getCustomerId());
            //之前授权过
            if(StringUtils.isNotBlank(customerBindDTO.getTwitterId())){
                if(StringUtils.isNotBlank(request.getAuthId()) && !request.getAuthId().equals(customerBindDTO.getTwitterId())){
                    throw new com.kikitrade.activity.model.exception.ActivityException(ActivityResponseCode.AUTH_NO_SAME_LAST, "@" + customerBindDTO.getTwitterName());
                }
            }else{
                List<TCustomerDTO> tCustomerBySocial = remoteCustomerBindService.findTCustomerBySocial(request.getSaasId(), ActivityTaskConstant.OpenSocialEnum.twitter.name(), request.getAuthId());
                if(CollectionUtils.isNotEmpty(tCustomerBySocial)){
                    throw new com.kikitrade.activity.model.exception.ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());
                }
            }
        }
    }
}
