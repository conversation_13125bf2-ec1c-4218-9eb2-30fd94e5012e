package com.kikitrade.activity.service.rpc.tg;

import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.bots.DefaultAbsSender;
import org.telegram.telegrambots.bots.DefaultBotOptions;
import org.telegram.telegrambots.meta.api.methods.groupadministration.GetChatMember;
import org.telegram.telegrambots.meta.api.objects.chatmember.ChatMember;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/5 19:32
 */
@Component
@Slf4j
public class QuestsTelegramBot extends DefaultAbsSender {

    @Resource
    private ThreePlatformProperties threePlatformProperties;

    public QuestsTelegramBot() {
        this(new DefaultBotOptions());
    }

    public QuestsTelegramBot(DefaultBotOptions options) {
        super(options);
    }

    @Override
    public String getBotToken() {
        return threePlatformProperties.getTg().getBotToken();
    }

    public ChatMember getChatMember(TGBotRequest request){

        try {
            return super.execute(new GetChatMember(request.getChatId(), request.getUserId()));
        } catch (TelegramApiException e) {
            log.error("getChatMember error:{}" ,request, e);
            return null;
        }
    }
}
