
package com.kikitrade.activity.service.common;

import com.aliyun.credentials.utils.AuthUtils;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.InstanceProfileCredentialsProvider;
import com.aliyun.oss.model.*;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.Map;

@Slf4j
public class UploadOssUtil {

    private OSS ossClient;

    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String path;

    public UploadOssUtil(String endpoint, String accessKeyId, String accessKeySecret, String bucketName, String path, List<String> refererList) {

        this.endpoint = endpoint;
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.bucketName = bucketName;
        this.path = path;
        if (StringUtils.isAnyBlank(this.accessKeyId, this.accessKeySecret)) {
            try {
                String sysRoleName = AuthUtils.getEnvironmentECSMetaData();
                log.info("UploadOssUtil, RoleName={}", sysRoleName);
                InstanceProfileCredentialsProvider instanceProfileCredentialsProvider = CredentialsProviderFactory.newInstanceProfileCredentialsProvider(sysRoleName);
                ossClient = new OSSClientBuilder().build(endpoint, instanceProfileCredentialsProvider, new ClientBuilderConfiguration());
            } catch (ClientException e) {
                log.error("UploadOssUtil init error", e);
            }
        } else {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        }
        if(CollectionUtils.isNotEmpty(refererList)){
            BucketReferer br = new BucketReferer(true, refererList);
            ossClient.setBucketReferer(bucketName, br);
        }
    }

    /**
     * Upload String
     *
     * @param key
     * @param content
     */
    public PutObjectResult putObject(String key, String content) {
        return ossClient.putObject(bucketName, absoluteKey(key), new ByteArrayInputStream(content.getBytes()));
    }

    /**
     * Upload Bytes
     *
     * @param key
     * @param content
     * @return
     */
    public PutObjectResult putObject(String key, byte[] content) {
        return ossClient.putObject(bucketName, absoluteKey(key), new ByteArrayInputStream(content));
    }

    /**
     * Upload file
     *
     * @param key
     * @param file
     */
    public PutObjectResult putObject(String key, File file) {
        return ossClient.putObject(bucketName, absoluteKey(key), file);
    }

    /**
     * Upload InputStream
     *
     * @param key
     * @param inputStream
     * @return
     */
    public PutObjectResult putObject(String key, InputStream inputStream) {
        return ossClient.putObject(bucketName, absoluteKey(key), inputStream);
    }

    /**
     * Delete Object
     *
     * @param absoluteKey
     * @return
     */
    public void deleteObject(String absoluteKey) {
        ossClient.deleteObject(bucketName, absoluteKey);
    }

    /**
     * Get OssObject
     *
     * @param absoluteKey
     * @return
     */
    public OSSObject getObject(String absoluteKey) {
        return ossClient.getObject(bucketName, absoluteKey);
    }

    public OSSObject getObject(String bucketName, String absoluteKey) {
        return ossClient.getObject(bucketName, absoluteKey);
    }

    public OSSObject getObject(URL url) {
        return ossClient.getObject(url, null);
    }

    public OSSObject getObject(URL url, Map<String, String> headerRequest) {
        return ossClient.getObject(url, headerRequest);
    }

    public List<Bucket> listBuckets() {
        return ossClient.listBuckets();
    }

    public List<OSSObjectSummary> listObjects(String KeyPrefix) {
        ObjectListing objectListing = ossClient.listObjects(bucketName,KeyPrefix);
        return objectListing.getObjectSummaries();
    }

    public String absoluteKey(String key) {

        if (Strings.isEmpty(path)) {
            return key;
        }

        return path + File.separator + key;

    }

    /**
     * Shutdown
     */
    public void shutdown() {

        if (ossClient == null) {
            return;
        }

        ossClient.shutdown();
    }

    public OSS getOssClient() {
        return ossClient;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }


    public String getAccessKeySecret() {
        return accessKeySecret;
    }


    public String getBucketName() {
        return bucketName;
    }

    public String getPath() {
        return path;
    }

    public String getLocation(String filePath) {
        if(StringUtils.isBlank(filePath)){
            return null;
        }
        String p = "https://";
        return String.format("https://%s.%s/%s/%s", bucketName, endpoint.substring(p.length() - 1), path, filePath);
    }

    public String getLocation(String path, String filePath) {
        if(StringUtils.isBlank(filePath)){
            return null;
        }
        String p = "https://";
        return String.format("https://%s.%s/%s/%s", bucketName, endpoint.substring(p.length() - 1), path, filePath);
    }
}
