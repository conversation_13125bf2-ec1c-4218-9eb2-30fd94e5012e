package com.kikitrade.activity.service.flow.step.listener;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.business.CsvService;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-05 10:57
 */
@Slf4j
@Component
@StepScope
public class RosterListener implements StepExecutionListener {

    @Value("#{jobParameters['batchId']}")
    private String batchId;
    @Value("#{jobParameters['activityId']}")
    private String activityId;
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private CsvService<ActivityCustomReward> rewardCsvService;
    @Resource
    private NoticeService noticeService;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;

    @Override
    public void beforeStep(StepExecution stepExecution) {
        log.info("RosterListener beforeStep start: activityId{}, batchId:{}", activityId, batchId);
        ActivityBatch batch = activityBatchNewService.findByBatchId(batchId);
        activityBatchStatusStoreBuilder.updateStatusAndNum(batchId, ActivityConstant.ImportStatusEnum.IMPORTING.name(), null);
        batch.setRewardStatus(ActivityConstant.ImportStatusEnum.IMPORTING.name());
        activityBatchNewService.updateBatch(batch);
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        boolean exist_not_imported = activityBatchRewardRosterStoreBuilder.existByBatchIdAndStatus(batchId, ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name());
        boolean exist_importing = activityBatchRewardRosterStoreBuilder.existByBatchIdAndStatus(batchId, ActivityConstant.ImportStatusEnum.IMPORTING.name());
        ActivityBatch batch = activityBatchNewService.findByBatchId(batchId);
        if(exist_importing || exist_not_imported){
            return ExitStatus.FAILED;
        }
        batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.UNAUDITED.name());
        activityBatchNewService.updateBatch(batch);
        activityBatchStatusStoreBuilder.updateStatusAndNum(batchId, ActivityConstant.ImportStatusEnum.IMPORT_SUCCESS.name(), null);

        noticeService.noticeDingTalk(String.format("%s%s",batchId,"_UNAUDITED"), kactivityModuleProperties.getReward().getDingTalkUrl(),
                String.format("批次：%s(%s), 名单导入完成，待审核发奖", batch.getName(), batch.getBatchId()));
        try{
            ActivityRewardPageParam pageParam=new ActivityRewardPageParam();
            pageParam.setBatchId(batch.getBatchId());
            pageParam.setActivityType(batch.getActivityType());
            pageParam.setPageNo(0);
            rewardCsvService.write(String.format("%s-%s.%s",batch.getName().replaceAll("\\s+",""),batch.getBatchId(),"csv"),pageParam);
        }catch(Exception ex){
            log.error("rosterstepexportcsverror:{}",JSON.toJSONString(batch),ex);
        }
        return ExitStatus.COMPLETED;
    }
}
