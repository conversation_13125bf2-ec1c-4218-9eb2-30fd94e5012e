package com.kikitrade.activity.service.job;

import com.kikitrade.activity.service.business.ActivitySchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * ActivityFailCheckJob
 *
 * <AUTHOR>
 * @create 2021/9/6 11:21 下午
 * @modify
 */
@Slf4j
@Component
public class ActivityFailCheckJob implements SimpleJob {

    private ActivitySchedulerService activitySchedulerTasks;

    @Lazy
    @Autowired
    public void setActivitySchedulerTasks(ActivitySchedulerService activitySchedulerTasks) {
        this.activitySchedulerTasks = activitySchedulerTasks;
    }

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            activitySchedulerTasks.failCheck();
        } catch (Exception e) {
            log.error("ActivityFailCheckJob running fail", e);
        }
    }
}
