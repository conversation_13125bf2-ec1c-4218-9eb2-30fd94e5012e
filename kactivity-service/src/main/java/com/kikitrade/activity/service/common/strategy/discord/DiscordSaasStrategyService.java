package com.kikitrade.activity.service.common.strategy.discord;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 17:47
 */
public interface DiscordSaasStrategyService {
    String strategy();
    void execute(OpenStrategyAuthRequest request) throws ActivityException;
}
