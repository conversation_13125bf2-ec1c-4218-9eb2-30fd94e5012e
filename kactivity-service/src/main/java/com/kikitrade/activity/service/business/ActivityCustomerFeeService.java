package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityCustomerFee;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.activity.model.constant.ActivityConstant;

import java.util.List;

public interface ActivityCustomerFeeService {


    List<ActivityCustomerFee> findAll(String tran_date, Integer offset, Integer limit, Integer type);


    /**
     * 查找
     *
     * @param tran_date    yyyyMMdd
     * @param activityType {@link ActivityType}
     * @param status       {@link ActivityConstant.BatchSatus}
     * @param offset
     * @param limit
     * @return
     */
    List<ActivityCustomerFee> findByDateAndStatus(String tran_date, Integer activityType, Integer status, int offset, int limit);


    List<ActivityCustomerFee> findByCurrencyDate(String currency, String date, int offset, int limit);

    List<ActivityCustomerFee> findByDate(String tran_date, int offset, int limit);

    int updateStatus(String tran_date, String customer_id, String currency, Integer status, Integer type);

    Long countByTypeAndTye(String tran_date, int type, int code);


}
