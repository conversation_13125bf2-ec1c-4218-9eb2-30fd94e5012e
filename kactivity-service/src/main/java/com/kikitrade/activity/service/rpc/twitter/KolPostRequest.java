package com.kikitrade.activity.service.rpc.twitter;

import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.model.Tweet;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/15 10:57
 */
@Data
@Slf4j
public class KolPostRequest extends AccessToken {

    private TwitterProperties twitterProperties;
    private String kols;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;

    public KolPostRequest(TwitterProperties twitterProperties){
        this.twitterProperties = twitterProperties;
    }

    public KolPostRequest build(List<String> kols, OffsetDateTime startTime, OffsetDateTime endTime){
        this.kols = StringUtils.join(kols, " ");
        this.startTime = startTime;
        this.endTime = endTime;
        return this;
    }

    public List<Tweet> execute(){
        try{
            return this.getApi(this.twitterProperties).tweets()
                    .tweetsRecentSearch(String.format("from:%s", kols))
                    .tweetFields(Set.of("entities"))
                    .startTime(startTime)
                    .endTime(endTime)
                    .maxResults(100)
                    .execute().getData();
        }catch (Exception ex){
            log.error("PostSubjectRequest error{}", this.kols ,ex);
            return null;
        }
    }
}
