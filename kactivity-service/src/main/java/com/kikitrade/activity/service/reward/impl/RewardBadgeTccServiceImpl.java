package com.kikitrade.activity.service.reward.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/7 19:22
 */
@Slf4j
@Service("rewardBadgeTccService")
public class RewardBadgeTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @Resource
    private ThreePlatformProperties threePlatformProperties;

    private static final String APP_KEY = "app_key";
    @Resource
    private OkHttpClient okHttpClient;

    @Override
    public void tryReward(RewardRequest request) throws Exception {

        if(request.getAmount() != null){
            for(int i = 0; i < request.getAmount().intValue(); i++){
                request.setRewardId(request.getRewardId() + i);
                reward(request);
            }
        }else {
            reward(request);
        }
    }

    private void reward(RewardRequest request) throws Exception  {
        if (request.getBadgeType() != null && ActivityConstant.BadgeTypeEnum.BADGE_CODE == request.getBadgeType()){
            rewardBadgeCode(request);
        } else {
            rewardBadgeId(request);
        }
    }

    private void rewardBadgeId(RewardRequest request) throws Exception {
        TreeMap<String, Object> param = new TreeMap<>();
        param.put("remark", request.getDesc());
        param.put("customer_id", request.getCustomerId());
        param.put("business_id", request.getRewardId());
        if (StringUtils.isNotBlank(request.getCurrency()) && request.getCurrency().contains("_")) {
            // eg: 54、5_2_LEADERBOARD
            String[] badgeInfoArr = request.getCurrency().split("_", -1);
            if (badgeInfoArr.length == 3) {
                param.put("season_id", Long.parseLong(badgeInfoArr[0]));
                param.put("badge_type", badgeInfoArr[2]);
                param.put("badge_level", Long.parseLong(badgeInfoArr[1]));
            }
        } else {
            param.put("badge_id", request.getCurrency());
        }
        param.put("signature", generateSignature(param, SaasConfigLoader.getConfig(request.getSaasId()).getAppKey()));
        log.info("rewardBadgeTccService request body:{}", JSON.toJSONString(param));

        MediaType mediaType = MediaType.parse("application/json");
        Request httpRequest = new Request.Builder()
                .url(String.format("%s", SaasConfigLoader.getConfig(request.getSaasId()).getApiHost() + "/v2/badges/records"))
                .method(HttpMethod.POST.name(), RequestBody.create(mediaType, JSON.toJSONString(param)))
                .addHeader(APP_KEY, "quests")
                .build();
        try {
            Response response = okHttpClient.newCall(httpRequest).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("rewardBadgeTccService result:{}", jsonObject);
            if(!jsonObject.getBoolean("success")){
                throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
            }
        } catch (Exception e) {
            log.error("rewardBadgeTccService error:{}", request, e);
            throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
        }
    }

    private void rewardBadgeCode(RewardRequest request) throws Exception {
        TreeMap<String, Object> param = new TreeMap<>();
        param.put("week_id", Long.parseLong(request.getCycle()));
        param.put("level_id", request.getLevel());
        param.put("season_id", Long.parseLong(request.getSeasonId()));
        param.put("customer_id", request.getCustomerId());
        param.put("business_id", request.getRewardId());
        param.put("signature", generateSignature(param, SaasConfigLoader.getConfig(request.getSaasId()).getAppKey()));

        MediaType mediaType = MediaType.parse("application/json");
        Request httpRequest = new Request.Builder()
            .url(String.format("%s", SaasConfigLoader.getConfig(request.getSaasId()).getAnchorHost() + "/s1/badges"))
            .method(HttpMethod.POST.name(), RequestBody.create(mediaType, JSON.toJSONString(param)))
            .addHeader(APP_KEY, "quests")
            //成就系统需要，目前deek、dojo、slg发徽章都走成就系统，需要定位哪个应用和哪个链
            .addHeader("client_id", SaasConfigLoader.getConfig(request.getSaasId()).getClientId())
            .addHeader("chain_id", SaasConfigLoader.getConfig(request.getSaasId()).getChainId())
            .build();
        try {
            Response response = okHttpClient.newCall(httpRequest).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("rewardBadgeTccService rewardBadgeCode result:{}", jsonObject);
            if(!jsonObject.getBoolean("success")){
                throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
            }
        } catch (Exception e) {
            log.error("rewardBadgeTccService rewardBadgeCode error:{}", request, e);
            throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
        }
    }

    public static String generateSignature(TreeMap<String, Object> sortedMap, String secretKey) {
        sortedMap.remove("signature");
        // 签名字符串
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedMap.entrySet()) {
            Object value = entry.getValue();
            String key = entry.getKey();
            if (value != null && !"".equals(value)) {
                //空值不传递，不参与签名组串
                signStr.append(key + "=" + value + "&");
            }
        }
        System.out.println(signStr);
        // 添加商户密钥生成最终签名
        String sign = signStr.toString() + secretKey;
        return md5(sign);
    }

    private static String md5(String str) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(str.getBytes());
            return new BigInteger(1, digest.digest()).toString(16);
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
    }
}
