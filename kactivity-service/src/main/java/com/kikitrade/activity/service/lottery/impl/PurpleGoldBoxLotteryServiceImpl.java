package com.kikitrade.activity.service.lottery.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.dal.tablestore.model.LotteryAward;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.order.api.RemoteOrderService;
import com.kikitrade.order.model.constant.OrderEventEnum;
import com.kikitrade.order.model.exception.OrderException;
import com.kikitrade.order.model.request.PlaceOrderRequest;
import com.kikitrade.order.model.response.PlaceOrderResponse;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/26 19:26
 */
@Service("purpleGoldBoxLotteryService")
public class PurpleGoldBoxLotteryServiceImpl extends AbstractLotteryService{


    @Resource
    private ActivityLotteryItemBuilder activityLotteryItemBuilder;
    @DubboReference
    private RemoteOrderService remoteOrderService;

    @Override
    public LotteryAward draw(String customerId, LotteryConfig lotteryConfig) throws Exception {
        LotteryAward award = JSON.parseObject(lotteryConfig.getAwards(), LotteryAward.class);
        if(award.getAmount().contains("-")){
            //奖励积分包含 "-" 表示随机
            award.setAmount(String.valueOf(RandomUtil.randomInt(Integer.parseInt(award.getAmount().split("-")[0].trim()), Integer.parseInt(award.getAmount().split("-")[1].trim()))));
        }
        return award;
    }

    @Override
    public LotteryResponse postAction(ActivityLotteryItem lotteryItem) {

        PlaceOrderRequest orderRequest = new PlaceOrderRequest();
        orderRequest.setEvent(OrderEventEnum.lottery);
        orderRequest.setSaasId(lotteryItem.getSaasId());
        orderRequest.setCustomerId(lotteryItem.getCustomerId());
        orderRequest.setParam(JSON.toJSONString(lotteryItem));

        //生成订单
        try {
            PlaceOrderResponse orderResponse = remoteOrderService.placeOrder(orderRequest);
            lotteryItem.setStatus(ActivityConstant.LotteryStatus.NO_PAID.name());
            activityLotteryItemBuilder.update(lotteryItem);
            LotteryResponse lotteryResponse = new LotteryResponse();
            lotteryResponse.setOrderId(orderResponse.getOrderId());
            return lotteryResponse;
        } catch (OrderException e) {
            throw new RuntimeException(e);
        }
    }
}
