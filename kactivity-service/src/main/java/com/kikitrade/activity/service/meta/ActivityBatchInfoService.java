package com.kikitrade.activity.service.meta;

import com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo;

import java.util.List;

public interface ActivityBatchInfoService {


    int save(ActivityBatchInfo activityBatchInfo) throws Exception;


    int updateStatus(String tran_date, Integer activity_id, String batch_id, Integer status);

    ActivityBatchInfo findByPrimaryKey(String tran_date, Integer activity_id, String batch_id);

    ActivityBatchInfo findByBatchId(String batch_id);

    ActivityBatchInfo findLastBatchByActivityId(Integer activity_id);

    List<ActivityBatchInfo> findAll(String tran_date, Integer activity_id, String batch_id, Integer offset, Integer limit);

    String getBatchInfoId(String processDataTableName, int activityType, String batchPt);

}
