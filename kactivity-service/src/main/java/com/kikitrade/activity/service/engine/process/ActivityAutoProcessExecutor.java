package com.kikitrade.activity.service.engine.process;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.service.business.ActivityCommonService;
import com.kikitrade.activity.service.business.ActivityService;
import com.kikitrade.activity.service.common.LogAlarmConstant;
import com.kikitrade.activity.service.common.LogAlarmUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Setter
@Getter
@Service
public class ActivityAutoProcessExecutor {

    @Resource
    private ActivityCommonService activityCommonService;
    @Resource
    private ActivityService activityService;
    @Resource
    ActivityRecordsBuilder activityRecordsBuilder;
    @Resource
    private LogAlarmUtil logAlarmUtil;


    public boolean execute(ActivityMessage activityMassage) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        boolean flag = true;
        log.info("ActivityAutoProcessExecutor execute start~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        log.info("ActivityAutoProcessExecutor activity massage is {} ", JSONObject.toJSONString(activityMassage));
        List<Activity> activityList = new ArrayList<>();

        Integer status = null;
        String remark = null;

        //do check
        try {
            if (activityMassage.getActivityId() != null) {
                //如果已经指定 活动id，则直接取。
                JsonResult jsonResult = activityService.findDetailById(activityMassage.getActivityId());
                if (jsonResult.getSuccess()) {
                    Activity activity = (Activity) jsonResult.getObj();
                    activityList = activity != null && activity.getStatus() == ActivityConstant.Status.PROCESSING.getCode()
                            || activity.getStatus() == ActivityConstant.Status.END.getCode() ? Arrays.asList(activity) : activityList;
                }

                //检查是否有满足条件的活动可以参加
                activityList = activityCommonService.checkRules(activityMassage, activityList);
                if (!CollectionUtils.isEmpty(activityList)) {
                    status = ActivityConstant.RecordStatus.RECORDED.getCode();
                    remark = ActivityConstant.MsgStatus.CHECK_PASSED.getCodeAndDesc();
                } else {
                    //活动规则检查失败，登记为完成，备注为检查失败
                    status = ActivityConstant.RecordStatus.FAILED.getCode();
                    remark = ActivityConstant.MsgStatus.PROCESS_FAILED.getParaMsg("checkRules fail");
                }

            } else {

                activityList = activityCommonService.getProcessingActivity(activityMassage.getSaasId());

                //检查是否有满足条件的活动可以参加
                activityList = activityCommonService.checkRules(activityMassage, activityList);
                if (CollectionUtils.isEmpty(activityList)) {
                    //活动规则检查失败，登记为完成，备注为检查失败
                    status = ActivityConstant.RecordStatus.FAILED.getCode();
                    remark = ActivityConstant.MsgStatus.PROCESS_FAILED.getParaMsg("checkRules fail");
                } else {

                    log.info("activityList size:{}, ids=[{}]", activityList.size(), Joiner.on(",").join(activityList.stream().map(a -> a.getId()).collect(Collectors.toList())));
                    // 检查活动参加次数
                    activityList = activityList.stream().filter(f -> activityCommonService.timesCheck(activityMassage, f)).collect(Collectors.toList());

                    log.info("activityList size:{} after check times. ids=[{}]", activityList.size(), activityList.isEmpty() ? "" : Joiner.on(",").join(activityList.stream().map(a -> a.getId()).collect(Collectors.toList())));

                    // 通过检查的活动
                    if (!CollectionUtils.isEmpty(activityList)) {
                        //活动规则检查通过，公共规则检查通过
                        status = ActivityConstant.RecordStatus.RECORDED.getCode();
                        remark = ActivityConstant.MsgStatus.CHECK_PASSED.getCodeAndDesc();
                    } else {
                        // 达到活动参与次数，跳过处理
                        log.info("activityCommonService timesCheck fail, so skip!");
                        stopWatch.stop();
                        log.info("ActivityAutoProcessExecutor execute end ,execute flag [{}] and costs [{}] ms~~~~~~~~~~~~~~~", flag, stopWatch.getTotalTimeMillis());
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            flag = false;
            status = ActivityConstant.RecordStatus.FAILED.getCode();
            remark = ActivityConstant.MsgStatus.PROCESS_FAILED.getParaMsg(e.getCause().getMessage().length() > 128 ? e.getCause().getMessage().substring(0, 127) : e.getCause().getMessage());
            logAlarmUtil.alarm(LogAlarmConstant.CheckPoint.ACTIVITY_DO_CHECK_ERROR, "ActivityAutoProcessExecutor do check fail", e);
        } finally {
            if (status != null && remark != null) {
                try {
                    activityCommonService.saveRecords(activityMassage, activityList, status, remark);
                } catch (Exception e) {
                    flag = false;
                    log.error("ActivityAutoProcessExecutor saveRecords fail.", e);
                }
            }
            if (!flag) {
                stopWatch.stop();
                log.info("ActivityAutoProcessExecutor execute end ,execute flag [{}] and costs [{}] ms~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", flag, stopWatch.getTotalTimeMillis());
                return flag;
            }
        }


        //do actionPrepare
        boolean isOk = activityCommonService.actionPrepare(activityMassage, activityList);

        //do action
        try {
            if (isOk) {
                //检查活动执行类型   0-自动 1-手动 2-定时
                activityList = activityList.stream().filter(f -> f.getExecute_type() == ActivityConstant.ExecuteType.AUTO.getCode()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(activityList)) {
                    flag = activityCommonService.doActions(activityMassage, activityList);
                }
            }
        } catch (Exception e) {
            flag = false;
            logAlarmUtil.alarm(LogAlarmConstant.CheckPoint.ACTIVITY_DO_CHECK_ERROR, "ActivityAutoProcessExecutor do actions fail", e);
        } finally {
            stopWatch.stop();
            log.info("ActivityAutoProcessExecutor execute end ,execute flag [{}] and costs [{}] ms~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", flag, stopWatch.getTotalTimeMillis());
            return flag;
        }


    }
}
