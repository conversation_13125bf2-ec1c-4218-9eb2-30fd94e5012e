package com.kikitrade.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.framework.observability.tracing.annotation.TracingSpan;
import com.kikitrade.framework.observability.tracing.constant.TracingBusiness;
import com.kikitrade.framework.ons.OnsMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
@Slf4j
public class ActivityEventListener implements OnsMessageListener {

    @Resource
    private ActivityEventAction activityEventAction;
    @Resource
    private TopicConfig topicConfig;
    @Resource
    @Lazy
    private ActivityEventListener activityEventListener;

    @Override
    public String topic() {
        return topicConfig.getTopicActivityEvent();
    }

    @Override
    public Action doConsume(Message message, ConsumeContext consumeContext) {
        return activityEventListener.doProcess(message, consumeContext);
    }

    @TracingSpan(name = "activityEventListener", business = TracingBusiness.none)
    public Action doProcess(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody());
        log.info("[task] eventAction start,{},{},{}", body, message.getMsgID(), message.getTopic());
        JSONObject message1 = JSON.parseObject(body);
        JSONObject message2 = JSON.parseObject(String.valueOf(message1.get("message")));
        JSONObject message3 = JSON.parseObject(String.valueOf(message2.get("body")));
        JSONObject bodyMessage = JSON.parseObject(String.valueOf(message3.get("body")));

        ActivityEventMessage activityEventMessage = JSON.parseObject(body, ActivityEventMessage.class);
        activityEventMessage.setBody(bodyMessage);

        try {
            String customerId = activityEventMessage.getCustomerId();
            if(StringUtils.isBlank(customerId) || "-1".equals(customerId) || customerId.equals("null")){
                return Action.CommitMessage;
            }
            activityEventAction.action(activityEventMessage);
            return Action.CommitMessage;
        } catch (ActivityException activityException) {
            if (activityException.getCode() == ActivityResponseCode.TASK_RETRY) {
                log.error("[task] eventAction retry, activityEventMessage:{},{}", activityEventMessage, activityException.getCode());
                return Action.ReconsumeLater;
            }
        } catch (Exception ex) {
            log.error("[task] eventAction error, activityEventMessage{}", activityEventMessage, ex);
        }
        return Action.CommitMessage;
    }
}
