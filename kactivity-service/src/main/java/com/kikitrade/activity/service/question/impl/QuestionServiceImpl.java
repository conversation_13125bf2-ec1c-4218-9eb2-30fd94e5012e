package com.kikitrade.activity.service.question.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.kikitrade.activity.api.model.QuestionDTO;
import com.kikitrade.activity.api.model.QuestionSimpleVO;
import com.kikitrade.activity.api.model.RespondentIdentity;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.api.model.request.UserAnswerRequest;
import com.kikitrade.activity.api.model.response.QuestionSetsResponse;
import com.kikitrade.activity.api.model.response.QuestionListResponse;
import com.kikitrade.activity.api.model.response.QuestionSettleResponse;
import com.kikitrade.activity.api.model.response.QuestionSettleVO;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.dal.tablestore.model.CustomerQuestionSets;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.question.QuestionSetsService;
import com.kikitrade.activity.service.question.QuestionService;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskAwardDomain;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.model.EventDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

import static com.kikitrade.activity.model.util.TimeFormat.YYYYMMDDHHMMSS_PATTERN;

/**
 * 答题服务实现
 */
@DubboService
@Slf4j
public class QuestionServiceImpl implements QuestionService {

    private static final String TK_ACQUIRE_QUESTION = "acquire_question";
    private static final String TK_SUBMIT_QUESTION = "submit_question";
    private static final String TK_SETS_CALCULATE_REWARD = "sets_calculate_reward";

    @Resource
    private TaskConfigService taskConfigService;

    @Resource
    private QuestionSetsService questionSetsService;

    @Resource
    private KactivityModuleProperties kactivityModuleProperties;

    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;

    @Autowired(required = false)
    private EventClient eventClient;

    @Override
    public QuestionSetsResponse getQuestionSets(RespondentIdentity identity) {
        CustomerQuestionSets questionSets = questionSetsService.findByUser(identity.getSaasId(), identity.getUid());
        QuestionSetsResponse questionSetsResponse = BeanUtil.copyProperties(questionSets, QuestionSetsResponse::new);

        List<TaskConfigDTO> signInTasks = taskConfigService.findByTaskCode(identity.getSaasId(), "sign_in");
        if (CollectionUtils.isNotEmpty(signInTasks)){
            TaskConfigDTO signInTask = signInTasks.get(0);
            ActivityTaskItem primaryTaskItem = activityTaskItemBuilder.findByCustomer(
                    identity.getUid(), TaskCycleDomain.getCurrencyCycle(signInTask, null), signInTask.getTaskId());
            log.info("signInTask primaryTaskItem = {}", primaryTaskItem);
            if (Objects.nonNull(primaryTaskItem)) {
                LocalDate modifiedDate = LocalDateTime.parse(primaryTaskItem.getModified(), DateTimeFormatter.ofPattern(YYYYMMDDHHMMSS_PATTERN)).toLocalDate();
                if (modifiedDate.equals(LocalDate.now())) {
                    questionSetsResponse.setTodayCheckIn(Boolean.TRUE);
                }
                if (modifiedDate.equals(LocalDate.now().minusDays(1)) || modifiedDate.equals(LocalDate.now())) {
                    questionSetsResponse.setSeriesCheckIn(Boolean.TRUE);
                    questionSetsResponse.setSeriesCheckInCount(primaryTaskItem.getProgress() % signInTask.getRewardFrequency());
                }
            }
        }

        List<TaskConfigDTO> invitedRegisterTasks = taskConfigService.findByTaskCode(identity.getSaasId(), "invited_register");
        if (CollectionUtils.isNotEmpty(invitedRegisterTasks)){
            TaskConfigDTO invitedRegisterTask = invitedRegisterTasks.get(0);
            ActivityTaskItem primaryTaskItem = activityTaskItemBuilder.findByCustomer(
                    identity.getUid(), TaskCycleDomain.getCurrencyCycle(invitedRegisterTask, null), invitedRegisterTask.getTaskId());
            log.info("invitedRegisterTask primaryTaskItem = {}", primaryTaskItem);
            if (Objects.nonNull(primaryTaskItem)) {
                questionSetsResponse.setInviteSucceedCount(primaryTaskItem.getProgress());
            }
        }
        return questionSetsResponse;
    }

    @Override
    public QuestionListResponse acquireQuestions(RespondentIdentity identity) throws ActivityException {
        log.info("begin acquire a set of questions with {}", identity);
        QuestionListResponse questionListResponse = new QuestionListResponse();

        List<TaskConfigDTO> acquireQuestionTasks = taskConfigService.findByTaskCode(identity.getSaasId(), TK_ACQUIRE_QUESTION);
        if (CollectionUtils.isEmpty(acquireQuestionTasks)){
            return questionListResponse;
        }

        CustomerQuestionSets questionSets = questionSetsService.findByUser(identity.getSaasId(), identity.getUid());
        log.info("before decrement questionSets = {}", questionSets);

        TaskConfigDTO acquireQuestionTask = acquireQuestionTasks.get(0);
        acquireQuestionTask.setLimit(Objects.isNull(questionSets) ? 0 : questionSets.getAvailableSets());
        if (acquireQuestionTask.getLimit() <= 0) {
            log.error("[acquireQuestions] acquire fail, availableSets not enough ! questionSets = {}", questionSets);
            return questionListResponse;
        }

        // 调用题库出题
        acquireQuestionsFromThirdParty(identity, questionListResponse);

        // Question 系统必须返回一组题目，否则报错结束
        if (!questionListResponse.getResult() || Objects.isNull(questionListResponse.getQuestions()) || questionListResponse.getQuestions().isEmpty()) {
            log.error("[submitQuestions] settleResponse is fail result, identity = {}, questionListResponse = {}", identity, questionListResponse);
            throw new ActivityException(ActivityResponseCode.THIRD_PLATFORM_FAIL);
        }

        // 扣减答题次数
        questionSets.setAvailableSets(questionSets.getAvailableSets() - 1);
        if (questionSetsService.update(questionSets)) {
            // 奖励固定经验值
            log.info("begin reward acquire question task");
            Token token = new Token();
            token.setLoginCustomerId(identity.getUid());
            token.setVipLevel(identity.getVipLevel());
            sendQuestsEvent(token, acquireQuestionTask, null);
        }
        return questionListResponse;
    }

    @Override
    public QuestionSettleResponse submitQuestions(RespondentIdentity identity, UserAnswerRequest userAnswerRequest) throws ActivityException {
        log.info("begin submit a set of questions with {}", identity);
        QuestionSettleResponse questionSettleResponse = new QuestionSettleResponse();
        List<TaskConfigDTO> submitQuestionTasks = taskConfigService.findByTaskCode(identity.getSaasId(), TK_SUBMIT_QUESTION);
        if (CollectionUtils.isEmpty(submitQuestionTasks)){
            throw new ActivityException(ActivityResponseCode.ACTIVITY_NOT_SUPPORT);
        }

        // 调用题库判题
        submitQuestionsFromThirdParty(identity, userAnswerRequest, questionSettleResponse);

        // Question 系统会识别该用户无效提交 (异常超时、恶意重复提交)，并给出特别标记
        if (!questionSettleResponse.getResult() || Objects.isNull(questionSettleResponse.getQuestionSettleVO()) || Objects.isNull(questionSettleResponse.getQuestionSettleVO().getScore())) {
            log.error("[submitQuestions] settleResponse is fail result, identity = {}, settleResponse = {}", identity, questionSettleResponse);
            if ("1002".equals(questionSettleResponse.getCode())) {
                // {"code":"1002","error_msg":"","message":"Output time was not obtained"}  // 无效提交、重复提交、错误提交
                throw new ActivityException(ActivityResponseCode.QUESTION_SUBMIT_INVALID);
            } else if ("1003".equals(questionSettleResponse.getCode())) {
                // {"code":"1003","error_msg":"error:The user's answer questions time exceeded the maximum time limit","message":"Spend time exceed the limit"} // 后端记录总答题时间 > 65 s
                throw new ActivityException(ActivityResponseCode.QUESTION_SUBMIT_TIMEOUT);
            }
            throw new ActivityException(ActivityResponseCode.THIRD_PLATFORM_FAIL);
        }

        Token token = new Token();
        token.setLoginCustomerId(identity.getUid());
        token.setVipLevel(identity.getVipLevel());

        // 奖励答题得分
        log.info("begin reward submit question task");
        TaskConfigDTO submitQuestionTask = submitQuestionTasks.get(0);
        JSONObject scoreExt = new JSONObject().fluentPut("amount", questionSettleResponse.getQuestionSettleVO().getScore());
        sendQuestsEvent(token, submitQuestionTask, scoreExt.toJSONString());

        // 累加答题次数，达标可奖励次数 (使用 usedSets 实时更新，因为如果使用用户提交答题任务的 taskItem.progress 会存在数据不准确的问题)
        CustomerQuestionSets questionSets = questionSetsService.findByUser(identity.getSaasId(), identity.getUid());
        Long afterIncrement = questionSetsService.incrementUsedSets(questionSets);
        log.info("before increment questionSets = {}, afterIncrement = {}", questionSets, afterIncrement);
        QuestionSettleVO questionSettleVO = questionSettleResponse.getQuestionSettleVO();
        questionSettleVO.setUsedSets(afterIncrement.intValue());
        questionSettleResponse.setQuestionSettleVO(questionSettleVO);

        List<TaskConfigDTO> calculateSetsTasks = taskConfigService.findByTaskCode(identity.getSaasId(), TK_SETS_CALCULATE_REWARD);
        if (Objects.nonNull(calculateSetsTasks) && !calculateSetsTasks.isEmpty()){
            TaskConfigDTO calculateSetsTask = calculateSetsTasks.get(0);
            log.info("begin reward calculate sets task. calculateSetsTasks id = {}", calculateSetsTask.getTaskId());
            JSONObject setsJson = new JSONObject().fluentPut("amount", afterIncrement);
            sendQuestsEvent(token, calculateSetsTask, setsJson.toJSONString());
        }

        fillRewardProperties(questionSettleResponse, identity);
        return questionSettleResponse;
    }

    /**
     * 补充答题奖励结算信息
     * @param questionSettleResponse
     * @param identity
     */
    private void fillRewardProperties(QuestionSettleResponse questionSettleResponse, RespondentIdentity identity) {
        CustomerQuestionSets questionSets = questionSetsService.findByUser(identity.getSaasId(), identity.getUid());
        QuestionSettleVO questionSettleVO = questionSettleResponse.getQuestionSettleVO();
        // 答题数组奖励
        Map<String, Object> param = new HashMap<>();
        param.put("amount", questionSettleVO.getUsedSets());
        TaskConfigDTO calculateSetsTask = taskConfigService.findByTaskCode(identity.getSaasId(), TK_SETS_CALCULATE_REWARD).get(0);
        BigDecimal rewardSets = getRewardAmountFromTask(calculateSetsTask, param, identity.getVipLevel());
        questionSettleVO.setRewardSets(rewardSets.intValue());
        questionSettleVO.setAvailableSets(questionSets.getAvailableSets() + questionSettleVO.getRewardSets());
        // 经验值奖励
        TaskConfigDTO acquireQuestionTask = taskConfigService.findByTaskCode(identity.getSaasId(), TK_ACQUIRE_QUESTION).get(0);
        BigDecimal rewardExp = getRewardAmountFromTask(acquireQuestionTask, null, identity.getVipLevel());
        questionSettleVO.setRewardExp(rewardExp);

        questionSettleResponse.setQuestionSettleVO(questionSettleVO);
    }

    private BigDecimal getRewardAmountFromTask(TaskConfigDTO taskConfig, Map<String, Object> param, String vipLevel) {
        List<Award> awards = TaskAwardDomain.getAward(taskConfig, new AtomicLong(1), vipLevel);
        if (Objects.nonNull(awards) && !awards.isEmpty()) {
            Award award = awards.get(0);
            String reward = award.getAmount(award.getAmount(), param);
            log.info("[getRewardAmountFromTask] get reward amount from taskConfig id = {}, param = {}, reward = {}", taskConfig.getTaskId(), param, reward);
            return new BigDecimal(reward);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 调用 Question 系统出题
     * @param identity
     * @param questionListResponse
     */
    private void acquireQuestionsFromThirdParty(RespondentIdentity identity, QuestionListResponse questionListResponse) {
        log.info("begin acquire questions from third-party, identity = {}", identity);
        JSONObject body = new JSONObject()
                .fluentPut("saas_id", identity.getSaasId())
                .fluentPut("category", "0").fluentPut("limit", "7");
        String responseBody = sendQuestionReq(identity.getAuthorization(), "/questions/output", body.toJSONString());
        if (StringUtils.isBlank(responseBody)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(responseBody);
        log.info("[question] acquireQuestions result:{}", jsonObject);

        String code = jsonObject.getString("code");
        JSONObject data = jsonObject.getJSONObject("data");
        if ("0000".equals(code) && Objects.nonNull(data) && data.containsKey("questions")) {
            JSONArray questions = data.getJSONArray("questions");
            List<QuestionSimpleVO> questionSimpleVOS = JSON.parseArray(questions.toJSONString(), QuestionSimpleVO.class);

            questionListResponse.setQuestions(questionSimpleVOS);
            questionListResponse.setResult(Boolean.TRUE);
            questionListResponse.setCode(code);
            questionListResponse.setMessage(jsonObject.getString("message"));
            log.info("acquire questions from third-party, result = {}", questionListResponse);
            return;
        }
        questionListResponse.setCode(code);
        questionListResponse.setMessage(jsonObject.getString("errorMsg"));
        log.error("acquire questions from third-party got fail result = {}", questionListResponse);
    }

    /**
     * 调用 Question 系统判题
     * @param identity
     * @param userAnswerRequest
     * @param questionSettleResponse
     */
    private void submitQuestionsFromThirdParty(RespondentIdentity identity, UserAnswerRequest userAnswerRequest, QuestionSettleResponse questionSettleResponse) {
        log.info("begin submit questions from third-party, identity = {}", identity);
        userAnswerRequest.setSaas_id(identity.getSaasId());
        String bodyStr = JSON.toJSONString(userAnswerRequest, SerializerFeature.WriteMapNullValue);

        String responseBody = sendQuestionReq(identity.getAuthorization(), "/questions/verify", bodyStr);
        if (StringUtils.isBlank(responseBody)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(responseBody);
        log.info("[question] submitQuestions result:{}", jsonObject);

        String code = jsonObject.getString("code");
        JSONObject data = jsonObject.getJSONObject("data");
        if ("0000".equals(code) && Objects.nonNull(data)) {
            QuestionSettleVO questionSettleVO = JSON.parseObject(data.toJSONString(), QuestionSettleVO.class);
            List<QuestionDTO> questionDTOS = JSON.parseArray(data.getString("questions"), QuestionDTO.class);
            questionSettleVO.setQuestions(questionDTOS);
            questionSettleResponse.setQuestionSettleVO(questionSettleVO);
            questionSettleResponse.setResult(Boolean.TRUE);
            questionSettleResponse.setCode(code);
            questionSettleResponse.setMessage(jsonObject.getString("message"));
            log.info("submit questions from third-party, result = {}", questionSettleResponse);
            return;
        }
        questionSettleResponse.setCode(code);
        questionSettleResponse.setMessage(jsonObject.getString("errorMsg"));
        log.error("submit questions from third-party got fail result = {}", questionSettleResponse);
    }

    private String sendQuestionReq(String authorization, String url, String bodyStr) {
        try {
            log.info("sendQuestionReq url = {}, body = {}", url, bodyStr);
            HttpUrl.Builder urlBuilder = HttpUrl.parse(
                    kactivityModuleProperties.getQuestion().getQuestionServiceAddress() + url).newBuilder();
            RequestBody requestBody = RequestBody.create(bodyStr,
                    MediaType.get("application/json; charset=utf-8"));
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("jwt-token", authorization)
                    .add("Content-Type", "application/json");
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .post(requestBody).build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (Objects.isNull(response.body())) {
                return null;
            }
            return response.body().string();
        } catch (Exception e) {
            log.error("[question] acquireQuestions fail", e);
        }
        return null;
    }

    private void sendQuestsEvent(Token token, TaskConfigDTO taskConfigDTO, String ext) {
        EventDTO eventDTO = new EventDTO();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", taskConfigDTO.getSaasId());
        jsonObject.put("vipLevel", token.getVipLevel());
        if (JSONUtil.isTypeJSON(ext)) {
            JSONObject extMap = JSON.parseObject(ext);
            if(extMap.containsKey("amount")){
                jsonObject.put("amount", extMap.getDoubleValue("amount"));
            }
        }
        eventDTO.setBody(jsonObject);
        eventDTO.setCustomerId(token.getLoginCustomerId());
        eventDTO.setTime(new Date().getTime());
        eventDTO.setGlobalUid(TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null)
                + "_" + RandomUtil.randomString(12) + "_" + token.getLoginCustomerId() + "_" +  taskConfigDTO.getTaskId());
        eventDTO.setName(taskConfigDTO.getCode());
        eventClient.push(eventDTO);
    }

}
