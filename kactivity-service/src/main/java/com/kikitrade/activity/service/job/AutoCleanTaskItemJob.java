package com.kikitrade.activity.service.job;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kevent.common.constant.EventConstants;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AutoCleanTaskItemJob {

  protected ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1);

  @Resource private KactivityProperties kactivityProperties;

  @Resource private ActivityTaskItemBuilder activityTaskItemBuilder;

  @PostConstruct
  public void init() {
    scheduledExecutorService.scheduleWithFixedDelay(
        this::action,
        0,
        kactivityProperties.getCleanTaskItemIntervalSeconds(),
        TimeUnit.SECONDS);
  }

  public void action() {
    log.info("AutoCleanTaskItemJob execute start");
    List<String> codeList =
        List.of(
            EventConstants.EventName.LIKE.getName(),
            EventConstants.EventName.LIKED.getName(),
            EventConstants.EventName.FOLLOW.getName(),
            EventConstants.EventName.FOLLOWED.getName());
    List<String> statusList =
        List.of(
            ActivityConstant.TaskStatusEnum.DONE.name(),
            ActivityConstant.TaskStatusEnum.NOT_STARTED.name(),
            ActivityConstant.TaskStatusEnum.APPENDING.name(),
            ActivityConstant.TaskStatusEnum.DOING.name(),
            ActivityConstant.TaskStatusEnum.FAIL.name());
    String startTime = "19990101";
    String endTime = getFormattedEndTime();
    int totalDeleted = 0;

    for (String code : codeList) {
      String nextToken = "";
      do {
        try {
          TokenPage<ActivityTaskItem> tokenPage =
              fetchTaskItems(List.of(code), statusList, startTime, endTime, nextToken);
          if (tokenPage == null || tokenPage.getRows() == null || tokenPage.getRows().isEmpty()) {
            log.info("No task items found for cleanup");
            break;
          }
          int deletedCount = deleteTaskItems(tokenPage);
          if (deletedCount == 0) {
            break;
          }
          totalDeleted += deletedCount;
          nextToken = tokenPage.getNextToken();
        } catch (Exception e) {
          log.error("AutoCleanTaskItemJob execute error", e);
          break;
        }
      } while (StringUtils.isNotBlank(nextToken));
    }
    log.info("AutoCleanTaskItemJob execute end, totalDeleted: {}", totalDeleted);
  }

  private String getFormattedEndTime() {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.DAY_OF_YEAR, -kactivityProperties.getCleanTaskItemDays());
    return new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
  }

  private TokenPage<ActivityTaskItem> fetchTaskItems(
      List<String> codeList, List<String> statusList, String startTime, String endTime, String nextToken) {
    return activityTaskItemBuilder.findByCodeList(codeList, startTime, endTime, statusList, nextToken, 100);
  }

  private int deleteTaskItems(TokenPage<ActivityTaskItem> tokenPage) throws InterruptedException {
    log.info(
        "Start to delete task items, nextToken: {}, total: {}",
        tokenPage.getNextToken(),
        tokenPage.getTotalCount());
    int deletedCount = 0;
    boolean res = activityTaskItemBuilder.delete(tokenPage.getRows());
    if (res) {
      deletedCount = tokenPage.getRows().size();
      log.info("Successfully deleted task items: {}", JSON.toJSONString(tokenPage.getRows()));
      //因为每次删除100条，所以这里sleep 2s，避免短时间内大量删除操作
      Thread.sleep(2000);
    } else {
      log.info("Failed to delete task items: {}", JSON.toJSONString(tokenPage.getRows()));
    }
    return deletedCount;
  }
}