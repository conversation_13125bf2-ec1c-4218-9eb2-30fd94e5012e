package com.kikitrade.activity.service.common.config;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:21
 */
@Data
public class TwitterProperties {

    private Map<String, String> clientId;
    private Map<String, String> clientSecret;
    private Map<String, String> bearer;
    private Map<String, String> pcRedirectUri;
    private Map<String, String> authScore;
    private Map<String, String> authVersion;
    private Map<String, Boolean> alarmWhenAuthRepeat;   // 检查到x被其他人已绑定时，是否报错。true-报错；false-不报错
}
