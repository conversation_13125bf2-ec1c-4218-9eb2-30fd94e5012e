package com.kikitrade.activity.service.flow.step.processor;

import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 11:41
 */
@Component
@StepScope
@Slf4j
public class RosterProcessor implements ItemProcessor<ActivityBatchRewardRoster, ActivityCustomReward> {

    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;

    @Override
    public ActivityCustomReward process(ActivityBatchRewardRoster roster) throws Exception {
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setBatchId(roster.getBatchId());
        reward.setCustomerId(roster.getCustomerId());
        reward.setSeq(roster.getSeq());
        reward.setUserName(roster.getUserName());
        reward.setCreated(roster.getCreated());
        reward.setStatus(ActivityConstant.RewardStatusEnum.NOT_AWARD.name());
        reward.setPhone(roster.getPhone());
        reward.setEmail(roster.getEmail());
        reward.setAmount(roster.getAmount());
        reward.setCurrency(roster.getCurrency());
        reward.setScope(roster.getScope());
        reward.setRewardType(roster.getRewardType() != null ? roster.getRewardType().toUpperCase() : roster.getRewardType());
        reward.setVipLevel(roster.getVipLevel());
        reward.setSide(roster.getSide());
        reward.setReferId(roster.getReferId());
        reward.setNickName(roster.getNickName());
        return reward;
    }
}
