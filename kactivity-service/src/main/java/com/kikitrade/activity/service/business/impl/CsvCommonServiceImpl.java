package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.csvreader.CsvWriter;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.CsvService;
import com.kikitrade.kpay.common.util.BigDecimalUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public abstract class CsvCommonServiceImpl<T> implements CsvService<T> {

    private static final String DEFAULT_PATH = "/tmp/csv/";

    @Override
    public void write(String fileName, ActivityRewardPageParam r) {

        ThreadPoolTaskExecutor threadPool = getThreadPool();
        //提交生成文件前执行
        before(r);
        //未配置线程池，直接执行
        if(threadPool == null){
            Pair<File, CsvResult> pair = doWrite(fileName, r);
            //文件生成完成， file可能为null
            if(pair != null){
                complete(r, pair.getLeft(), pair.getRight());
            }else{
                complete(r, null, null);
            }
        }else{
            getThreadPool().execute(() -> {
                Pair<File, CsvResult> pair = doWrite(fileName, r);
                //上传到oss
                if(pair != null){
                    complete(r, pair.getLeft(), pair.getRight());
                }else{
                    complete(r, null, null);
                }
            });
        }
    }

    private Pair<File, CsvResult> doWrite(String fileName, ActivityRewardPageParam r) {
        fileName = blankFileName(fileName);
        String path = String.format("%s%s", getPath(), fileName);
        File dir = new File(getPath());
        if(!dir.exists()){
            dir.mkdir();
        }
        CsvWriter csvWriter = null;
        try {
            CsvResult result = new CsvResult();
            File file = new File(path);
            file.deleteOnExit();
            csvWriter = new CsvWriter(path, ',', StandardCharsets.UTF_8);
            csvWriter.writeRecord(showHead(r.getActivityType()).toArray(new String[0]));
            List<T> list;
            do{
                list = read(r);
                if(CollectionUtils.isEmpty(list)){
                    break;
                }

                List<String[]> arrList = toArray(list, r.getActivityType());
                count(arrList, result, r.getActivityType(),r.getStatusList());
                for(String[] arr : arrList){
                    List<String> head = head(r.getActivityType());
                    int index = head.indexOf("status");
                    if(ActivityConstant.RewardStatusEnum.DELETE.isEquals(arr[index])){
                        continue;
                    }
                    csvWriter.writeRecord(arr, true);
                }
            }while (!CollectionUtils.isEmpty(list));
            return ImmutablePair.of(file, result);
        }catch (Exception ex){
            log.error("生成文件失败，fileName:{}", fileName, ex);
            return null;
        }finally {
            if(csvWriter != null){
                try {
                    csvWriter.endRecord();
                }catch (IOException ex){
                    log.error("file close error" ,ex);
                }
                csvWriter.flush();
                csvWriter.close();
            }
        }
    }

    private void count(List<String[]> item, CsvResult result, String activityType, List<String> statusList){
        Map<String, Integer> map = result.getStatusCount();
        Map<String, BigDecimal> amountMap = result.getAmount();
        if(map == null){
            map = new ConcurrentHashMap<>();
            amountMap = new ConcurrentHashMap<>();
        }
        List<String> head = head(activityType);
        int index = head.indexOf("status");
        int amountIndex = head.indexOf("amount");
        int awardIndex = head.indexOf("currency");
        int typeIndex = head.indexOf("rewardType");
        for(String[] arr : item){
            try{
                result.setTotal(result.getTotal() == null ? 1 : result.getTotal() + 1);
                if(ActivityConstant.RewardStatusEnum.DELETE.isEquals(arr[index])){
                    continue;
                }
                if(!CollectionUtils.isEmpty(statusList) && !statusList.contains(arr[index])){
                    continue;
                }
                map.put(arr[index], map.getOrDefault(arr[index], 0) + 1);
                String key = String.format("%s_%s",typeIndex < 0 ? "Token" : arr[typeIndex], arr[awardIndex]);
                BigDecimal amount = amountMap.getOrDefault(key, new BigDecimal(0.0)).add(StringUtils.isBlank(arr[amountIndex]) ? new BigDecimal(0.0) : new BigDecimal(arr[amountIndex]));
                amountMap.put(key, amount.stripTrailingZeros());
            }catch (Exception ex){
                log.error("计算金额失败：{}",arr, ex);
            }
        }
        result.setAmount(amountMap);
        result.setStatusCount(map);
    }

    private List<String[]> toArray(List<T> data, String activityType){
        log.info(JSON.toJSONString(data));
        List<String[]> list = new ArrayList<>();
        for(T t : data){
            Class<?> clazz = t.getClass();
            Field[] fields = clazz.getDeclaredFields();
            String[] result = new String[head(activityType).size()];
            log.info("head:{}", head(activityType));
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    log.info("field.getName{}", field.getName());
                    int pos = head(activityType).indexOf(field.getName());
                    if(pos < 0){
                        continue;
                    }
                    if(field.get(t) != null && NumberUtils.isNumber(field.get(t).toString()) && field.get(t).toString().length() >= 12){
                        result[pos] = field.get(t) == null ? "" : new String(String.format("\t%s",field.get(t).toString()).getBytes(), StandardCharsets.UTF_8);
                    }else{
                        result[pos] = field.get(t) == null ? "" : new String(field.get(t).toString().getBytes(), StandardCharsets.UTF_8);
                    }
                } catch (IllegalAccessException e) {
                    log.error("csv common toArray exception:{}", JSON.toJSONString(field),e);
                }
            }
            list.add(result);
        }
        return list;
    }

    public String getPath(){
        return DEFAULT_PATH;
    }

    abstract List<String> head(String activityType);

    //default show head
    abstract List<String> showHead(String activityType);

    abstract List<T> read(ActivityRewardPageParam r);

    abstract ThreadPoolTaskExecutor getThreadPool();

    protected abstract void complete(ActivityRewardPageParam r, File file, CsvResult result);

    protected abstract void before(ActivityRewardPageParam r);

    @Getter
    @Setter
    public class CsvResult{
        private Map<String, Integer> statusCount;
        private Map<String, BigDecimal> amount;
        private Integer total;
    }

    private String blankFileName(String fileName){
        return fileName.replaceAll("\"|\\*|<|>|\\?|\\|/|\\/|:|\\s+|\\\\|\\|", "");
    }
}
