package com.kikitrade.activity.service.flow.step.reader;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.importing.roster.domain.ActivityCustomRewardItem;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 11:53
 */
@Component
@StepScope
@Slf4j
public class RewardReader extends AbstractTokenPageReader<ActivityCustomReward> {

    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;

    @Value("#{jobParameters['batchId']}")
    private String batchId;

    /**
     * 进行分页读取
     *
     * @return nextToken
     */
    @Override
    protected PrimaryKey doReadPage() {
        RangeResult<ActivityCustomReward> page = activityCustomRewardStoreBuilder.findByStatusAndBatchId(ActivityConstant.RewardStatusEnum.NOT_AWARD.name(),
                batchId, getNextToken());
        if(page != null){
            results = page.list;
            return page.nextToken;
        }
        results = new ArrayList<>();
        return null;
    }

    @Override
    protected int getPageSize() {
        return 100;
    }
}
