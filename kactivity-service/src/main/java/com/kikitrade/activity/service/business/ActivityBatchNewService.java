package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.param.ActivityBatchParam;
import com.kikitrade.activity.facade.award.ActivityBatchDTO;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.Result;
import com.kikitrade.framework.common.model.PageResult;

import java.util.List;

public interface ActivityBatchNewService {

    /**
     * 根据批次id查询
     * @param batchId
     * @return
     */
    ActivityBatch findByBatchId(String batchId);

    /**
     * 根据批次id查询
     * @param batchId
     * @return
     */
    ActivityBatch findByBatchIdFromCache(String batchId);


    /**
     * 新建批次
     * @param activityBatchDto
     * @return
     */
    Result<ActivityBatch> saveOrUpdate(ActivityBatchDTO activityBatchDto);

    Result<ActivityBatch> save(ActivityBatchDTO activityBatchDto, ActivityEntity activityEntity, ActivityBatch activityBatch) throws Exception;

    /**
     * 创建批次
     * @param activityBatch
     * @return
     */
    Result<String> save(ActivityBatch activityBatch, ActivityEntity activityEntity);

    /**
     * 删除批次
     * @param batchId
     * @return
     */
    Result<String> deleteBatch(String batchId);

    /**
     * 分页查询批次列表
     * @param activityBatchParam
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageResult findForPage(ActivityBatchParam activityBatchParam, int pageNo, int pageSize);

    /**
     * 审核
     * @param batchId
     * @param auditTypeEnum
     * @return
     */
    Result<String> audit(String batchId, ActivityConstant.AuditTypeEnum auditTypeEnum);

    /**
     * 修改批次token值，用于下次执行worker
     * @param batchId
     * @param token
     * @return
     */
    Boolean updateBatchToken(String batchId, String token);

    /**
     * 分页下批次总数
     * @param activityId
     * @param statusList
     * @return
     */
    int count(String activityId, List<String> statusList);

    void updateBatchStatus(String batchId, String status);

    Boolean updateBatch(ActivityBatch activityBatch);

    /**
     * 活动下绑定的批次数量
     * @param activityId
     * @return
     */
    int countByActivity(String activityId);

    /**
     * 查询某个活动下最后一个批次
     * @param activityId
     * @return
     */
    ActivityBatch findLastBatchByActivity(String activityId);
}
