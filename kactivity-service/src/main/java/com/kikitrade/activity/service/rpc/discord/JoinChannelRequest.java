package com.kikitrade.activity.service.rpc.discord;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.service.common.config.DiscordProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/23 15:11
 */
@Data
@Slf4j
public class JoinChannelRequest extends AccessToken {

    private DiscordProperties discordProperties;
    private String targetUserId;
    private String guildId;
    private String roleId;

    public JoinChannelRequest(){}

    public JoinChannelRequest(DiscordProperties discordProperties, AccessToken accessToken){
        super(accessToken);
        this.discordProperties = discordProperties;
    }

    public JoinChannelRequest buildGetJoinRequest(String roleId, String guildId, String targetUserId){
        this.guildId = guildId;
        this.targetUserId = targetUserId;
        this.roleId = roleId;
        return this;
    }

    public String execute() {

        Request request = new Request.Builder()
                .url(String.format("https://discord.com/api/v10/guilds/%s/members/%s", this.guildId, this.targetUserId))
                .addHeader("Authorization", "Bot " + discordProperties.getBotToken().get(this.getSaasId()))
                .build();
        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            log.info("[JoinChannelRequest] execute response {}, {}, {}", response.isSuccessful(), response.code(), JSON.toJSONString(response));
            if(response.isSuccessful() && response.body() != null){
                String responseBody = response.body().string();
                JSONObject jsonObject = new JSONObject(responseBody);
                log.info("[discord] join channel response:{},{}", jsonObject, this.targetUserId);
                if(!jsonObject.isNull("user")){
                    if(this.roleId == null){
                        return this.targetUserId;
                    }
                    JSONArray roles = jsonObject.getJSONArray("roles");
                    if(roles.length() > 0){
                        for(int i = 0; i < roles.length(); i++){
                            if(this.roleId.equals(String.valueOf(roles.getString(i)))){
                                return this.targetUserId;
                            }
                        }
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("[discord] join channel error:{}", this.targetUserId ,e);
            return null;
        }
    }
}
