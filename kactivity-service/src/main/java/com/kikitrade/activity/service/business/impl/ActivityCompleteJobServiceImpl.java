package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityCompleteJobService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.common.CronUtil;
import com.kikitrade.activity.service.flow.job.BatchJobFactory;
import com.kikitrade.activity.service.job.ActivityImportJob;
import com.kikitrade.activity.service.job.ElasticJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class ActivityCompleteJobServiceImpl implements ActivityCompleteJobService {

    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ElasticJobService elasticJobService;
    @Resource
    private ActivityImportJob activityImportJob;
    @Resource
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @Resource
    private RedisService redisService;
    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @Resource
    private ActivityEntityService activityEntityService;
    @Resource
    private BatchJobFactory batchJobFactory;

    @Override
    public void exec(ShardingContext shardingContext) {
        log.info("ActivityCompleteJob start。。。");
        List<ActivityBatchStatus> batchList = activityBatchStatusStoreBuilder.findByStatus(ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name(), 10);
        Optional.of(batchList).orElse(new ArrayList<>()).forEach(b -> {
            ActivityBatch batch = activityBatchNewService.findByBatchId(b.getBatchId());
            log.info("batch:{}", JSON.toJSONString(batch));
            if (batch != null && StringUtils.isBlank(batch.getInstance())) {
                batchJobFactory.runManualRosterJob(batch.getActivityId(), batch.getBatchId());
            }
        });
    }
}
