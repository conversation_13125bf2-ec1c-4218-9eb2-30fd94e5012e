package com.kikitrade.activity.service.meta;

import com.kikitrade.activity.dal.mysql.model.ActivityContents;

import java.util.List;


public interface ActivityContentsService {

    boolean save(ActivityContents activityContents) throws Exception;

    boolean update(ActivityContents activityContents) throws Exception;

    boolean delete(Integer activityId, String locale) throws Exception;

    List<ActivityContents> findById(Integer activityId, String saasId);

    ActivityContents findByPara(Integer activityId, String locale);

}
