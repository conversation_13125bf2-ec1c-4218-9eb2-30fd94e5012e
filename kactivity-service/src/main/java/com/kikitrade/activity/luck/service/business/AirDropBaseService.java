package com.kikitrade.activity.luck.service.business;

import java.math.BigDecimal;

/**
 * @author: wang
 * @date: 2023/3/14
 * @desc:
 */
public interface AirDropBaseService {
    /**
     * 获取报价
     * @param amount
     * @param fromCurrency
     * @param toCurrency
     * @param keepDecimalForCoin
     * @return
     */
    BigDecimal getPrice(BigDecimal amount, String fromCurrency, String toCurrency, Integer keepDecimalForCoin);

    /**
     * 获取精度
     * @param currency
     * @return
     */
    Integer getKeepDecimalForCoin(String currency);

}
