package com.drex.core.service.business.rexy;

import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.OperateMaizeKernelRequest;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.api.response.MaizeKernelDTO;

public interface RexyBasketService {

    CustomerRexyBasketsDTO getCustomerRexyBaskets(String customerId, RexyConstant.RexyBasketsTypeEnum basketType) throws CoreException;

    MaizeKernelDTO collectMaizeKernel(OperateMaizeKernelRequest request) throws CoreException;

    MaizeKernelDTO claimMaizeKernel(OperateMaizeKernelRequest request) throws CoreException;

}
