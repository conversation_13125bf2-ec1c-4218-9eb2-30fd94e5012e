package com.drex.core.service.business.rexy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.RexyConfigDTO;
import com.drex.core.dal.tablestore.builder.CustomerRexyBasketsBuilder;
import com.drex.core.dal.tablestore.builder.CustomerRexyBuilder;
import com.drex.core.dal.tablestore.builder.RexyConfigBuilder;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.drex.core.dal.tablestore.model.CustomerRexyBaskets;
import com.drex.core.dal.tablestore.model.RexyConfig;
import com.drex.core.service.business.rexy.CustomerRexyService;
import com.drex.core.service.business.rexy.RexyConfigService;
import com.drex.core.service.mapperstruct.RexyMapperStruct;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.response.CustomerDTO;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerRexyServiceImpl implements CustomerRexyService {

    @Resource
    private RexyConfigService rexyConfigService;
    @Resource
    private RexyMapperStruct rexyMapperStruct;
    @Resource
    private CustomerRexyBuilder customerRexyBuilder;
    @Resource
    private RexyConfigBuilder rexyConfigBuilder;
    @Resource
    private CustomerRexyBasketsBuilder customerRexyBasketsBuilder;
    @Resource
    private RemoteCustomerService remoteCustomerService;


    @Override
    public void initCustomerRexy(CustomerDTO customerDTO) {
        // 初始化用户拥有的恐龙
        RexyConfigDTO rexyConfig = rexyConfigService.getDefaultRexyByLevel(customerDTO.getKycLevel());
        CustomerRexy customerRexy = rexyMapperStruct.toCustomerRexy(customerDTO, rexyConfig);
        customerRexy.setStatus(RexyConstant.CustomerRexyStatus.ACTIVE.name());
        customerRexyBuilder.insert(customerRexy);
    }

    @Override
    public List<CustomerRexy> getByCustomerAndStatus(String customerId, RexyConstant.CustomerRexyStatus status) {
        List<CustomerRexy> rexyList = customerRexyBuilder.getByCustomerAndStatus(customerId, status.name());
        if(CollectionUtil.isEmpty(rexyList)){
            Response<CustomerDTO> response = remoteCustomerService.getById(customerId);
            RexyConfigDTO rexyConfig = rexyConfigService.getDefaultRexyByLevel(response.getData().getKycLevel());
            CustomerRexy customerRexy = rexyMapperStruct.toCustomerRexy(response.getData(), rexyConfig);
            customerRexy.setStatus(RexyConstant.CustomerRexyStatus.ACTIVE.name());
            customerRexyBuilder.insert(customerRexy);
        }
        return rexyList;
    }

    @Override
    public List<CustomerRexy> getByCustomer(String customerId) {
        return customerRexyBuilder.getByCustomer(customerId);
    }

    @Override
    public Boolean updateCustomerRexyByLevel(String customerId, String userKycLevel) {
        log.info("updateCustomerRexyByLevel customerId: {}, kycLevel: {}", customerId, userKycLevel);
        RexyConfigDTO defaultRexyConfig = rexyConfigService.getDefaultRexyByLevel(userKycLevel);
        if (defaultRexyConfig == null) {
            log.error("updateCustomerRexyByLevel rexyConfig is null");
            return false;
        }
        String rexyId = defaultRexyConfig.getId();
        boolean res = true;
        int newBasketLimit;
        // 获取用户所有恐龙
        List<CustomerRexy> customerRexyList = getByCustomer(customerId);
        //持有的恐龙列表中删掉等级高于用户等级的恐龙，并从数据库中删除
        int userKycLevelNum = Integer.parseInt(userKycLevel.substring(1));

        // 找出需要删除的恐龙
        List<CustomerRexy> rexysToRemove = customerRexyList.stream()
                .filter(rexy -> {
                    int rexyLevelNum = Integer.parseInt(rexy.getRexyLevel().substring(1));
                    return rexyLevelNum > userKycLevelNum;
                })
                .collect(Collectors.toList());

        // 从数据库中删除这些恐龙
        if (CollectionUtil.isNotEmpty(rexysToRemove)) {
            Boolean batchDeleteRes = customerRexyBuilder.batchDelete(rexysToRemove);
            log.info("updateCustomerRexy batchDelete customerRexy: {}", batchDeleteRes);
            // 从列表中移除这些恐龙
            customerRexyList.removeAll(rexysToRemove);
        }

        // 检查用户是否已拥有指定的恐龙
        CustomerRexy targetRexy = null;
        for (CustomerRexy rexy : customerRexyList) {
            if (rexy.getRexyId().equals(rexyId)) {
                targetRexy = rexy;
                break;
            }
        }

        // 如果用户已拥有该恐龙
        if (targetRexy != null) {
            // 将用户当前持有的恐龙设置为非活跃
            customerRexyList.stream().filter(e -> e.getStatus().equals(RexyConstant.CustomerRexyStatus.ACTIVE.name())).findFirst().ifPresent(item -> {
                item.setStatus(RexyConstant.CustomerRexyStatus.DISABLE.name());
                customerRexyBuilder.updateStatus(item);
            });

            // 将目标恐龙设置为活跃状态
            targetRexy.setStatus(RexyConstant.CustomerRexyStatus.ACTIVE.name());
            customerRexyBuilder.updateStatus(targetRexy);
            newBasketLimit = targetRexy.getRexyBasketLimit();
        } else {
            // 用户不拥有该恐龙，需要查询恐龙配置并验证用户等级
            RexyConfig rexyConfig = rexyConfigBuilder.getById(rexyId);
            if (rexyConfig == null) {
                log.error("Can't find rexy config for id {}", rexyId);
                return !res;
            }

            // 验证用户KYC等级是否满足要求
            if (userKycLevelNum < Integer.parseInt(rexyConfig.getLevel().substring(1))) {
                log.error("user level not enough to get rexy, userLevel: {}, rexyLevel: {}", userKycLevel, rexyConfig.getLevel());
                return !res;
            }

            // 将用户当前持有的恐龙设置为非活跃
            customerRexyList.stream().filter(e -> e.getStatus().equals(RexyConstant.CustomerRexyStatus.ACTIVE.name())).findFirst().ifPresent(item -> {
                item.setStatus(RexyConstant.CustomerRexyStatus.DISABLE.name());
                customerRexyBuilder.updateStatus(item);
            });

            // 创建新的恐龙记录
            CustomerRexy newRexy = new CustomerRexy();
            newRexy.setCustomerId(customerId);
            newRexy.setRexyId(rexyId);
            newRexy.setRexyName(rexyConfig.getName());
            newRexy.setRexyLevel(rexyConfig.getLevel());
            newRexy.setRexyRate(rexyConfig.getRate());
            newRexy.setRexyBasketLimit(rexyConfig.getLimit());
            newRexy.setStatus(RexyConstant.CustomerRexyStatus.ACTIVE.name());
            newRexy.setCreated(System.currentTimeMillis());
            newRexy.setRexyAvatar(rexyConfig.getAvatar());
            Boolean insertRes = customerRexyBuilder.insert(newRexy);
            log.info("updateCustomerRexy insert customerRexy: {}", insertRes);

            newBasketLimit = rexyConfig.getLimit();
        }
        // 更新用户恐龙篮子的限制
        updateBasket(customerId, RexyConstant.RexyBasketsTypeEnum.normal.name(), newBasketLimit);
        return res;
    }

    /**
     * 更新特定类型的篮子
     *
     * @param customerId  用户ID
     * @param basketType  篮子类型
     * @param basketLimit 篮子限制
     */
    private void updateBasket(String customerId, String basketType, Integer basketLimit) {
        CustomerRexyBaskets basket = customerRexyBasketsBuilder.getByCustomerId(customerId, basketType);
        if (basket != null) {
            BigDecimal newLimit = BigDecimal.valueOf(basketLimit);
            basket.setBasketLimit(newLimit);

            // 如果received超过了basketLimit，则更新为basketLimit
            if (basket.getReceived().compareTo(newLimit) > 0) {
                basket.setReceived(newLimit);
            }
            customerRexyBasketsBuilder.update(basket);
        }
    }
}
