package com.drex.core.service.business.rexy;

import com.drex.core.api.common.CoreException;
import com.drex.core.api.response.InformationDTO;

import java.util.List;

public interface InformationService {

    /**
     * 根据ID获取信息
     *
     * @param id 信息ID
     * @return 信息DTO
     * @throws CoreException 核心异常
     */
    InformationDTO getById(String id) throws CoreException;

    /**
     * 保存信息
     *
     * @param informationDTO 信息DTO
     * @return 是否保存成功
     * @throws CoreException 核心异常
     */
    Boolean saveInformation(InformationDTO informationDTO) throws CoreException;

    /**
     * 更新信息
     *
     * @param informationDTO 信息DTO
     * @return 是否更新成功
     * @throws CoreException 核心异常
     */
    Boolean updateInformation(InformationDTO informationDTO) throws CoreException;

    /**
     * 删除信息
     *
     * @param id 信息ID
     * @return 是否删除成功
     * @throws CoreException 核心异常
     */
    Boolean deleteInformation(String id) throws CoreException;

    /**
     * 根据类型获取信息列表
     *
     * @param type 信息类型
     * @return 信息DTO列表
     * @throws CoreException 核心异常
     */
    List<InformationDTO> getByType(String type) throws CoreException;

    /**
     * 获取推荐信息列表
     *
     * @param isRecommend 是否推荐
     * @return 信息DTO列表
     * @throws CoreException 核心异常
     */
    List<InformationDTO> getByRecommend(Boolean isRecommend) throws CoreException;

    /**
     * 根据类型和推荐状态获取信息列表
     *
     * @param type 信息类型
     * @param isRecommend 是否推荐
     * @return 信息DTO列表
     * @throws CoreException 核心异常
     */
    List<InformationDTO> getByTypeAndRecommend(String type, Boolean isRecommend) throws CoreException;

    /**
     * 分页获取信息列表
     *
     * @return 信息DTO列表
     */
    List<InformationDTO> listAll(Integer offset, Integer limit) throws CoreException;
}
