package com.kikitrade.kweb.controller.v2.customer.delegate.impl;

import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.RemoteAuthService;
import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.AuthRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.api.RemoteLimitedTimeAssetService;
import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetOperateType;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.request.AssetLedgersListRequest;
import com.kikitrade.asset.model.request.AssetLedgersSumRequest;
import com.kikitrade.asset.model.response.AssetLaddersResponse;
import com.kikitrade.customer.generated.api.v2.CustomerV2ApiDelegate;
import com.kikitrade.customer.generated.model.v2.CurrentUserVO;
import com.kikitrade.customer.generated.model.v2.CustomerBindVO;
import com.kikitrade.customer.generated.model.v2.CustomerVO;
import com.kikitrade.customer.generated.model.v2.SocialTokenVO;
import com.kikitrade.customer.generated.model.v2.VerifyVO;
import com.kikitrade.customer.generated.model.v2.WebResultCurrentUserVO;
import com.kikitrade.customer.generated.model.v2.WebResultCustomerBindVO;
import com.kikitrade.customer.generated.model.v2.WebResultSocialTokenVO;
import com.kikitrade.customer.generated.model.v2.WebResultVerifyVO;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.OathLoginRequest;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.model.UpdateUserVerifiedRequest;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kcustomer.common.constants.CustomerConstants;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.IpUtil;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.TopRankDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Locale;
import java.util.concurrent.ForkJoinPool;

/**
 * @author: lizhifeng
 * @date: 2023/5/8 14:08
 */
@Slf4j
@Component
public class CustomerV2ApiDelegateImpl implements CustomerV2ApiDelegate {

    @Resource
    private QuestWebProperties questWebProperties;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @DubboReference
    private RemoteTaskService remoteTaskService;
    @Autowired
    private HttpServletRequest request;
    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteLimitedTimeAssetService remoteLimitedTimeAssetService;
    @DubboReference
    private RemoteAuthService remoteAuthService;
    @DubboReference
    private RemoteMemberService remoteMemberService;
    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private KwebBaseService kwebBaseService;

    private ForkJoinPool joinPool = new ForkJoinPool(16);

    @Override
    public ResponseEntity<WebResultSocialTokenVO> authToken(String app, String platform, String code, String idToken, String saasId, String redirectUri) {
        try{
            QCustomer qcustomer = CustomerHolder.qcustomer();

            AuthRequest authRequest = new AuthRequest();
            authRequest.setSaasId(saasId);
            authRequest.setPlatform(platform);
            authRequest.setCode(code);
            authRequest.setRedirectUri(redirectUri);
            authRequest.setCustomerId(qcustomer.getUid());
            authRequest.setAppId(qcustomer.getAppId());

            if("twitter".equals(platform) && StringUtils.isNotBlank(idToken)){
                cn.hutool.json.JSONObject payloads = JWTUtil.parseToken(idToken).getPayloads();
                if(payloads.containsKey("ospAddress")){
                    String platformSource = payloads.getStr("issuePlatformSource");
                    String issueId = payloads.getStr("issueId");
                    authRequest.setTwitterId(("twitter".equalsIgnoreCase(platformSource) || "x".equalsIgnoreCase(platformSource)) ? issueId : null);
                }else if(payloads.containsKey("verifierId")){
                    String verifierId = payloads.getStr("verifierId");
                    String[] verifierArr = verifierId.split("\\|");
                    authRequest.setTwitterId(("twitter".equalsIgnoreCase(verifierArr[0]) || "x".equalsIgnoreCase(verifierArr[0])) ? verifierArr[1] : null);
                }
            }

            Token token = remoteAuthService.auth(authRequest);
            if(token == null){
                log.info("[auth] error:{},{}", platform ,qcustomer);
                return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
            }
            SocialTokenVO tokenVO = new SocialTokenVO();
            tokenVO.setAccessToken(token.getRefreshToken());
            tokenVO.setRefreshToken(token.getRefreshToken());

            WebResultSocialTokenVO webResultSocialTokenVO = new WebResultSocialTokenVO();
            webResultSocialTokenVO.setResponseEnum(WebResponseEnum.SUCCESS);
            webResultSocialTokenVO.setObj(tokenVO);
            return ResponseEntityUtil.result(webResultSocialTokenVO);
        }catch (ActivityException ex){
            log.error("authToken error:{}", code, ex);
            if (ex.getCode() == ActivityResponseCode.AUTH_REPEAT) {
                return ResponseEntityUtil.result(WebResponseEnum.AUTH_REPEAT);
            } else if (ex.getCode() == ActivityResponseCode.AUTH_NO_SAME_LOGIN || ex.getCode() == ActivityResponseCode.AUTH_NO_SAME_LAST) {
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_AUTH_NOT_SAME_WITH_LOGIN);
            } else if (ex.getCode() == ActivityResponseCode.CHECK_CRATED_DAYS_FAIL) {
                return ResponseEntityUtil.result(WebResponseEnum.CHECK_CRATED_DAYS_FAIL);
            }
            return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
        }catch (Exception e) {
            log.error("authToken error:{}", code, e);
            return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
        }
    }

    @Override
    @Deprecated
    public ResponseEntity<WebResultCustomerBindVO> oauthLogin(String idToken, String publicKey, String walletAddress, String thirdAuthSource, String saasId, String walletType, Boolean autoRegister) {
        log.info("oauthLogin saasId:{}, idToken:{}, publicKey:{}, thirdAuthSource:{}, autoRegister:{}", saasId, idToken, publicKey, thirdAuthSource, autoRegister);

        String ip = IpUtil.getRemoteIP(request);
        ip = ip.split(",")[0];

        OathLoginRequest oathLoginRequest = OathLoginRequest.builder()
                .saasId(saasId)
                .thirdPlatformToken(idToken)
                .password(null)
                .country(null)
                .email(null)
                .emailToken(null)
                .ip(ip)
                .deviceId(null)
                .pushTokenType(null)
                .userAgent(null)
                .publicKey(publicKey)
                .walletAddress(walletAddress)
                .walletType(StringUtils.isEmpty(walletType) ? CustomerConstants.WalletType.sui.name() : walletType)
                .autoRegister(autoRegister != null && autoRegister)
                //后期所有三方认证来源使用这个字段
                .thirdAuthSource(StringUtils.isBlank(publicKey) ? null : CustomerConstants.ThirdAuthSource.fromName(thirdAuthSource))
                .build();
        try {
            //在 mugen-web 登陆，返回用户信息
            ResponseEntity<CustomerVO> responseEntity = threeOauthLogin(idToken, publicKey, walletAddress, thirdAuthSource, saasId, walletType, autoRegister);
            if (responseEntity.getStatusCode() != HttpStatusCode.valueOf(200)
                    || !responseEntity.hasBody()) {
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_LOGIN_INCORRECT);
            }
            CustomerVO customerVO = responseEntity.getBody();
            log.info("oauthLogin customerVO:{}", customerVO);
            CustomerDTO customerDTO = BeanUtil.copyProperties(customerVO, CustomerDTO::new);
            customerDTO.setSaasId(saasId);
            CustomerBindDTO bindDTO = remoteCustomerBindService.register(customerDTO);

            WebResultCustomerBindVO webResult = new WebResultCustomerBindVO();
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(buildLoginResponse(bindDTO, customerVO));
            return ResponseEntityUtil.result(webResult);
        } catch (CustomerException e) {
            log.warn("CustomerApiDelegateImpl#oauthLogin customerException, oathLoginRequest: {}", JSON.toJSONString(oathLoginRequest), e);
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_LOGIN_INCORRECT);
        } catch (Exception e) {
            log.error("CustomerApiDelegateImpl#oauthLogin exception, oathLoginRequest: {}", JSON.toJSONString(oathLoginRequest), e);
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_LOGIN_INCORRECT);
        }
    }

    private ResponseEntity<CustomerVO> threeOauthLogin(String idToken, String publicKey, String walletAddress,
                                                       String thirdAuthSource, String saasId, String walletType,
                                                       Boolean autoRegister) throws Exception{
        try{

            FormBody.Builder builder = new FormBody.Builder()
                    .add("idToken", idToken)
                    .add("publicKey", publicKey)
                    .add("walletAddress", walletAddress)
                    .add("thirdAuthSource", thirdAuthSource)
                    .add("walletType", walletType)
                    .add("autoRegister", "true");

            OkHttpClient httpClient = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(questWebProperties.getTWebCustomerLogin().get(saasId))
                    .addHeader("saas_id", saasId)
                    .post(builder.build())
                    .build();
            Call call = httpClient.newCall(request);
            Response response = call.execute();
            if(!response.isSuccessful()){
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_LOGIN_INCORRECT);
            }
            String responseBody = response.body().string();
            log.info("threeOauthLogin response:{}", responseBody);
            JSONObject parse = JSON.parseObject(responseBody);
            if(parse.getBoolean("success")){
                log.info("threeOauthLogin response:{}", responseBody);
                CustomerVO customerVO = JSON.parseObject(parse.get("obj").toString(), CustomerVO.class);
                return ResponseEntityUtil.result(customerVO);
            }else{
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_LOGIN_3RD_FAIL);
            }
        }catch (Exception ex){
            log.error("threeOauthLogin error:{}", request ,ex);
            throw ex;
        }
    }

    /**
     * GET /customer/me : get current time
     * 当前用户信息
     *
     * @param idToken      (optional)
     * @param JWT_TOKEN    (optional)
     * @param saasId       (optional)
     * @param businessType 默认返回 twitterName, 还支持 invite、asset、all (optional)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<WebResultCurrentUserVO> me(String idToken, String JWT_TOKEN, String saasId, String businessType, String handleName, String appId, String chainId) {
        CustomerBindDTO qcustomer;
        if(handleName != null && appId != null){
            qcustomer = kwebBaseService.getCustomerByHandleName(saasId, appId, handleName, chainId);
        }else{
            qcustomer = CustomerHolder.qcustomer();
        }
        if(qcustomer == null){
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
        }
        CurrentUserVO bindVO = new CurrentUserVO();
        bindVO.setSaasId(qcustomer.getSaasId());
        bindVO.setUid(qcustomer.getUid());
        bindVO.setTwitterName(qcustomer.getTwitterName());
        bindVO.setXverified(qcustomer.getXVerified());
        bindVO.setXverifiedType(qcustomer.getXVerifiedType());
        bindVO.setOspAvatar(qcustomer.getOspAvatar());
        if(StringUtils.isNotBlank(businessType)){
            joinPool.submit(() -> {
                Arrays.asList(businessType.split(",")).parallelStream().forEach(type -> {
                    switch (type){
                        case "divide":
                            BigDecimal divide = remoteAssetService.readAssetDivideSummary(qcustomer.getSaasId(), qcustomer.getUid());
                            bindVO.setDividePoint(divide);
                            break;
                        case "invite":
                            AssetLedgersSumRequest sumRequest = new AssetLedgersSumRequest();
                            sumRequest.setSaasId(qcustomer.getSaasId());
                            sumRequest.setCustomerId(qcustomer.getUid());
                            sumRequest.setBusinessTypeList(new ArrayList<>(){{add(AssetBusinessType.INVITED_REGISTER); add(AssetBusinessType.INVITED_PURCHASE_MEMBERSHIP);}});
                            BigDecimal ledger = remoteAssetService.sumAssetLedgerByBusiness(sumRequest);
                            bindVO.setInvitePoint(ledger);
                            break;
                        case "asset":
                            AssetDTO asset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.POINT, AssetCategory.NORMAL);
                            TCustomerDTO tCustomerDTO = remoteCustomerBindService.findTCustomerByUid(qcustomer.getSaasId(), qcustomer.getUid());
                            if(tCustomerDTO != null){
                                bindVO.setAvailable(asset.getAvailable()
                                        .add(vipAdd(asset.getAvailable(), tCustomerDTO))
                                        .add(pfpAdd(asset.getAvailable(), tCustomerDTO)));
                                if(tCustomerDTO.getPfpCount() != null && tCustomerDTO.getPfpCount() > 0){
                                    bindVO.setPfp(String.format("%s%s",tCustomerDTO.getPfpCount() * 100, "%"));
                                }
                                bindVO.setIsVip(false);
                                if(BooleanUtils.isTrue(tCustomerDTO.getVip())){
                                    bindVO.setVip(String.format("%s%s",tCustomerDTO.getVipCount() * 20, "%"));
                                    bindVO.setIsVip(tCustomerDTO.getVip());
                                }
                            }else{
                                bindVO.setAvailable(asset.getAvailable());
                            }
                            TopRankDTO topLadderDTO = remoteMemberService.topRank(qcustomer.getSaasId(), AssetType.POINT.name(), qcustomer.getUid());
                            bindVO.setRankValue(topLadderDTO.getRankValue());
                            bindVO.setRankPercent(topLadderDTO.getRankPercent());
                            break;
                        case "exp":
                            AssetDTO expAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.EXPERIENCE, AssetCategory.NORMAL);
                            if(expAsset != null){
                                bindVO.setExpValue(expAsset.getAvailable());
                            }
                            break;
                        case "ticket":
                            AssetDTO ticketAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.TICKET, AssetCategory.NORMAL);
                            if(ticketAsset != null){
                                bindVO.setTicketAvailable(ticketAsset.getAvailable());
                                bindVO.setTicketUsed(ticketAsset.getTotalSent());
                            }
                            break;
                        case "voucher":
                            AssetDTO voucherAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.VOUCHER, AssetCategory.NORMAL);
                            if(voucherAsset != null){
                                bindVO.setVoucherAvailable(voucherAsset.getAvailable());
                            }
                            break;
                        case "experience_voucher":
                            AssetDTO experienceVoucherAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.EXPERIENCE_VOUCHER, AssetCategory.NORMAL);
                            if(experienceVoucherAsset != null){
                                bindVO.setExperienceVoucherAvailable(experienceVoucherAsset.getAvailable());
                            }
                            break;
                        case "boost":
                            AssetDTO boostAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.BOOST, AssetCategory.NORMAL);
                            if(boostAsset != null){
                                bindVO.setBoostValue(boostAsset.getAvailable());
                            }
                            break;
                        case "credentials":
                        case "aura":
                            AssetDTO credentialsAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.valueOf(type.toUpperCase(Locale.ROOT)), AssetCategory.NORMAL);
                            if(credentialsAsset != null){
                                bindVO.setCredentialsAvailable(credentialsAsset.getAvailable());
                                if (qcustomer.getOspVerified() &&
                                        bindVO.getCredentialsAvailable().compareTo(BigDecimal.valueOf(questWebProperties.getCredentialsRiseLimit().get(saasId))) >= 0) {
                                    bindVO.setOsPVerified(true);
                                }
                            }
                            TopRankDTO topLadderDTOAura = remoteMemberService.topRank(qcustomer.getSaasId(), AssetType.AURA.name(), qcustomer.getUid());
                            bindVO.setRankValue(topLadderDTOAura.getRankValue());
                            bindVO.setRankPercent(topLadderDTOAura.getRankPercent());
                            break;
                        case "extra_boost":
                            bindVO.setExtraBoostValue(remoteLimitedTimeAssetService.sumExtraBoostByCustomerId(saasId, qcustomer.getUid()));
                            break;
                        case "ojo":
                            AssetDTO ojoAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.OJO, AssetCategory.NORMAL);
                            if(ojoAsset != null){
                                bindVO.setOjoValue(ojoAsset.getAvailable());
                            }
                            break;
                        case "season_point":
                            AssetDTO seasonPointAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.SEASON_POINT, AssetCategory.NORMAL);
                            bindVO.setSeasonPointAvailable(seasonPointAsset.getAvailable());
                            break;
                        default:
                            break;
                    }
                });
            }).join();
        }
        WebResultCurrentUserVO result = new WebResultCurrentUserVO();
        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(bindVO);
        return ResponseEntityUtil.result(result);
    }

    @Override
    public ResponseEntity<WebResultVerifyVO> refreshVerified(String idToken, String JWT_TOKEN, String saasId) {
        WebResultVerifyVO verifyVO = new WebResultVerifyVO();
        CustomerBindDTO qcustomer = CustomerHolder.qcustomer();
        VerifyVO verify = new VerifyVO();
        verifyVO.setSuccess(true);
        verify.setOsPVerified(qcustomer.getOspVerified());
        verify.setXverified(qcustomer.getXVerified());

        UpdateUserVerifiedRequest updateRequest = new UpdateUserVerifiedRequest();
        updateRequest.setSaasId(qcustomer.getSaasId());
        updateRequest.setUid(qcustomer.getUid());
        updateRequest.setCid(qcustomer.getCid());
        if (!qcustomer.getOspVerified()) {
            AssetDTO credentialsAsset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.AURA, AssetCategory.NORMAL);
            log.info("ospVerify saasId:{} customerId:{}, credentials available:{}", saasId, qcustomer.getUid(), credentialsAsset.getAvailable());
            if (credentialsAsset.getAvailable().compareTo(BigDecimal.valueOf(questWebProperties.getCredentialsRiseLimit().get(saasId))) >= 0) {
                updateRequest.setOspVerified(true);
            }
        }
        if (!qcustomer.getXVerified() && StringUtils.isNotBlank(qcustomer.getTwitterName())) {
            String xManagerServiceUrl = questWebProperties.xManagerServiceUrl;
            if(StringUtils.isNotBlank(xManagerServiceUrl)){
                Request request = new Request.Builder()
                        .url(String.format("%s%s?handle=%s", xManagerServiceUrl, "/twitter/user/info", qcustomer.getTwitterName()))
                        .get().build();
                Response response = null;
                try {
                    response = okHttpClient.newCall(request).execute();
                    JSONObject jsonObject = JSON.parseObject(response.body().string());
                    log.info("xManagerService response:{}", jsonObject);
                    if(jsonObject.getString("code").equals("0000")){
                        JSONObject data = jsonObject.getJSONObject("data");
                        if (data != null && data.getBoolean("verified")) {
                            updateRequest.setXVerified(true);
                            updateRequest.setXVerifiedType(data.getString("verified_type"));
                            updateRequest.setXProfileImageUrl(data.getString("profile_image_url"));
                        }
                    }
                } catch (IOException e) {
                    log.error("ospVerify error:{}", request, e);
                    return ResponseEntityUtil.result(verifyVO);
                }
            }
        }
        log.info("refreshVerified updateRequest:{}", updateRequest);
        remoteCustomerBindService.updateUserVerified(updateRequest);

        CustomerBindDTO byUid = remoteCustomerBindService.findById(qcustomer.getSaasId(), qcustomer.getCid());
        verify.setOsPVerified(byUid.getOspVerified());
        verify.setXverified(byUid.getXVerified());
        verifyVO.setObj(verify);
        return ResponseEntityUtil.result(verifyVO);
    }

    private CustomerBindVO buildLoginResponse(CustomerBindDTO bindDTO, CustomerVO customerVO) {
        CustomerBindVO vo = new CustomerBindVO();
        vo.setCid(bindDTO.getCid());
        vo.setUid(bindDTO.getUid());
        vo.setSaasId(bindDTO.getSaasId());
        vo.setJwt(customerVO.getJwtKey());
        vo.setMugenJwt(customerVO.getJwtKey());
        AssetLedgersListRequest assetRequest = new AssetLedgersListRequest();
        assetRequest.setCustomerId(bindDTO.getUid());
        assetRequest.setAssetCategory(AssetCategory.NORMAL);
        assetRequest.setAssetType(AssetType.POINT);
        assetRequest.setSaasId(bindDTO.getSaasId());
        assetRequest.setAssetOperateType(AssetOperateType.TRANSFER_IN);
        assetRequest.setLimit(0);
        assetRequest.setOffset(1);

        AssetLaddersResponse assetLaddersResponse = remoteAssetService.assetLadders(assetRequest);
        if(assetLaddersResponse != null && assetLaddersResponse.getAssetDTO() != null){
            vo.setAvailable(assetLaddersResponse.getAssetDTO().getAvailable());
        }else{
            vo.setAvailable(BigDecimal.ZERO);
        }
        return vo;
    }

    private BigDecimal vipAdd(BigDecimal originalAvailable, TCustomerDTO customer){
        if(customer != null && BooleanUtils.isTrue(customer.getVip())){
            return originalAvailable.multiply(new BigDecimal(0.2).multiply(new BigDecimal(customer.getVipCount())));
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal pfpAdd(BigDecimal originalAvailable, TCustomerDTO customer) {
        if (customer.getPfpCount() == null) {
            return BigDecimal.ZERO;
        }
        return originalAvailable.multiply(new BigDecimal(customer.getPfpCount()));
    }

    @Builder
    @Data
    public static class AuthTokenRequest {
        private String url;
        private String param;
        private String accessToken;
    }
}
