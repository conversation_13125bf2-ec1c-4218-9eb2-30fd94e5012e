package com.kikitrade.kcustomer.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.PaymentTermDTO;
import com.kikitrade.kcustomer.api.model.PaymentTermSearchRequest;
import com.kikitrade.kcustomer.common.constants.PaymentTermConstant;
import com.kikitrade.kcustomer.dal.builder.PaymentTermBuilder;
import com.kikitrade.kcustomer.service.model.PaymentTermV2Request;
import com.kikitrade.kcustomer.service.remote.RemotePaymentTermServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest
@Slf4j
@TestPropertySource("classpath:application-default.properties")
public class PaymentTermTest {
    @Autowired
    RemotePaymentTermServiceImpl remotePaymentTermService;
//    @Autowired
//    CustomerController customerController;
    final String customerId = "********";

    @Autowired
    PaymentTermBuilder paymentTermBuilder;

    @Test
    public void save() {
        try {
            for (int i =3;i<6;i++) {
                PaymentTermV2Request paymentTermV2Request = new PaymentTermV2Request();
                paymentTermV2Request.setAccountNumber("***********"+i);
                paymentTermV2Request.setBankBranch("中国银行支行"+i);
                paymentTermV2Request.setBankName("中国银行"+i);
                paymentTermV2Request.setUserName("ly"+i);
                paymentTermV2Request.setCity("中国");
                List<String> streets = new ArrayList<>();
                streets.add("街道1"+i);
                streets.add("街道2"+i);
                paymentTermV2Request.setStreets(streets);
                paymentTermV2Request.setType(PaymentTermConstant.Type.BANK.ordinal());
                PaymentTermDTO paymentTermDTOAdd = convertDTO(paymentTermV2Request,"ly-test-22","kiki");
                remotePaymentTermService.addPaymentTerm(paymentTermDTOAdd);
            }

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Test
    public void getById() {
        try {
            PaymentTermDTO paymentTermDTO = remotePaymentTermService.getByPaymentTermId("kiki","3526527e4b434566a3062831bd144684");
            System.out.println(JSONObject.toJSONString(paymentTermDTO));

            PaymentTermDTO paymentTermDTO2 = remotePaymentTermService.getByPaymentTermId("kiki","8cdc4d64a0f44688b9f922514c2aaadd");
            System.out.println(JSONObject.toJSONString(paymentTermDTO2));

        }catch (Exception e){
            e.printStackTrace();
        }
    }



    @Test
    public void delete() {
        try {
            remotePaymentTermService.delete("kiki","ly-test","0ddb887b6bc64133b1bb048eb9d899f7");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Test
    public void modify() {
        try {
            PaymentTermV2Request paymentTermV2Request = new PaymentTermV2Request();
            paymentTermV2Request.setAccountNumber("*********");
            paymentTermV2Request.setBankBranch("中国银行支行2");
            paymentTermV2Request.setBankName("中国银行3");
            paymentTermV2Request.setUserName("ly3");
            paymentTermV2Request.setCity("中国");
            List<String> streets = new ArrayList<>();
            streets.add("街道1");
            streets.add("街道2");
            paymentTermV2Request.setStreets(streets);
            paymentTermV2Request.setType(PaymentTermConstant.Type.BANK.ordinal());
            PaymentTermDTO paymentTermDTO = convertDTO(paymentTermV2Request,"ly-test","kiki");
            paymentTermDTO.setId("0ddb887b6bc64133b1bb048eb9d899f7");
            remotePaymentTermService.modify(paymentTermDTO);
        }catch (CustomerException customerException){

        }catch (Exception e){
            e.printStackTrace();
        }
    }


    // TODO: 2023/3/13 罗梅
    @Test
    public void modifyBusiness() {
        try {


            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","358bd7aed1ab41bfbc78d1dde61ca47a", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","50a8b57f60744a80a04a7bd01a3624a1", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
//            remotePaymentTermService.modifyBusiness("kiki","ly-test","0ddb887b6bc64133b1bb048eb9d899f7", PaymentTermConstant.Business.c2c, PaymentTermConstant.OperateType.DELETE);

            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","921d563da6934122a53c6b41191d3a5a", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","868e8065c61f43b3940199b785fc0ecf", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
//
            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","b4148c6d36ab43e4929f70ce398992c3", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","50a8b57f60744a80a04a7bd01a3624a1", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
//
            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","f2c2ce22c9dc4b2e8d0956a946125cc4", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
//            remotePaymentTermService.modifyBusiness("kiki","2021111712573532704001","b72ad2201c864da7bf56fac948a2713a", PaymentTermConstant.Usage.c2c, PaymentTermConstant.OperateType.ADD);
//
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Test
    public void getByCustomerIdAndAccountNumber (){
        com.kikitrade.kcustomer.dal.model.PaymentTermDO paymentTermDO = paymentTermBuilder.getByCustomerIdAndAccountNumber("kiki", customerId,"accountNumber");

    }
    // TODO: 2023/3/13 罗梅
    @Test
    public void search() {
        try {
            JSONObject jsonObject = new JSONObject();
            //查询用户的卡数据
            PaymentTermSearchRequest paymentTermSearchRequest = new PaymentTermSearchRequest();
            paymentTermSearchRequest.setCustomerId("2021111712573532704001");
            List<PaymentTermDTO> paymentTermDTOSTemp = remotePaymentTermService.search("kiki",paymentTermSearchRequest);
            //数据分组
            Map<String, List<PaymentTermDTO>> map  = paymentTermDTOSTemp.stream().collect(Collectors.groupingBy(x -> CollectionUtils.isEmpty(x.getBusiness())?"excludeBusiness":"business"));
            List<PaymentTermDTO> paymentTermDTOS = Lists.newArrayList();
            if (map.get("business") != null){
                paymentTermDTOS.addAll(map.get("business"));
            }
            if (map.get("excludeBusiness") != null){
                paymentTermDTOS.addAll(map.get("excludeBusiness"));
            }

            paymentTermDTOS.stream()
                    .collect(Collectors.groupingBy(PaymentTermDTO::getType));





            paymentTermSearchRequest.setBusiness(PaymentTermConstant.Usage.c2c);
            List<PaymentTermDTO> paymentTermDTOS2 = remotePaymentTermService.search("kiki",paymentTermSearchRequest);

            Map<PaymentTermConstant.Type, List<PaymentTermDTO>> dataListMap = paymentTermDTOS2.stream()
                    .collect(Collectors.groupingBy(PaymentTermDTO::getType));
            //数据分组
            jsonObject.put("business",dataListMap);
            //查询业务更多卡
            PaymentTermSearchRequest paymentTermSearchMoreRequest = new PaymentTermSearchRequest();
            paymentTermSearchMoreRequest.setCustomerId("ly-test");
            paymentTermSearchMoreRequest.setExcludeBusiness(PaymentTermConstant.Usage.c2c);
            List<PaymentTermDTO> paymentBankMoreDTOS = remotePaymentTermService.search("kiki",paymentTermSearchMoreRequest);
            Map<PaymentTermConstant.Type, List<PaymentTermDTO>> moreDataListMap = paymentBankMoreDTOS.stream()
                    .collect(Collectors.groupingBy(PaymentTermDTO::getType));
            //数据分组
            jsonObject.put("other",moreDataListMap);

        }catch (Exception e){
            e.printStackTrace();
        }
    }



    private static PaymentTermDTO convertDTO(PaymentTermV2Request paymentTermV2Request, String customerId, String saasId) {
        PaymentTermDTO paymentTermDTO = new PaymentTermDTO();
        paymentTermDTO.setSaasId(saasId);
        BeanUtil.copyProperties(paymentTermV2Request, paymentTermDTO);
        paymentTermDTO.setType(PaymentTermConstant.Type.fromCode(paymentTermV2Request.getType()));
        paymentTermDTO.setCustomerId(customerId);
        return paymentTermDTO;
    }

//    public static void main(String[] args) {
//        PaymentTermV2Request paymentTermV2Request = new PaymentTermV2Request();
//        paymentTermV2Request.setAccountNumber("*********");
//        paymentTermV2Request.setBankBranch("中国银行支行");
//        paymentTermV2Request.setBankName("中国银行");
//        paymentTermV2Request.setUserName("ly");
//        paymentTermV2Request.setCity("中国");
//        List<String> streets = new ArrayList<>();
//        streets.add("街道1");
//        streets.add("街道2");
//        paymentTermV2Request.setStreets(streets);
//        paymentTermV2Request.setType(PaymentTermConstant.Type.BANK.ordinal());
//        PaymentTermDTO paymentTermDTO = convertDTO(paymentTermV2Request,"ly-test","kiki");
//        System.out.println(paymentTermDTO);
//    }

}
