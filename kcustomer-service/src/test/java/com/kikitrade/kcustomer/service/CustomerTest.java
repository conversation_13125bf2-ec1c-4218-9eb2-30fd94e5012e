package com.kikitrade.kcustomer.service;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.UpdateCustomerKyc2AddressRequest;
import com.kikitrade.kcustomer.api.service.RemoteCustomerIdentityService;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import com.kikitrade.kcustomer.dal.builder.CustomerIdentityBuilder;
import com.kikitrade.kcustomer.dal.builder.CustomerStoreBuilder;
import com.kikitrade.kcustomer.dal.model.CustomerDO;
import com.kikitrade.kcustomer.dal.model.CustomerIdentityDO;
import com.kikitrade.kcustomer.facade.QueryCustomerRequest;
import com.kikitrade.kcustomer.facade.QueryInviterResponse;
import com.kikitrade.kcustomer.service.configuration.KCustomerProperties;
import com.kikitrade.kcustomer.service.controller.CustomerController;
import com.kikitrade.kcustomer.service.customer.CustomerService;
import com.kikitrade.kcustomer.service.facade.impl.CustomerFacadeImpl;
import com.kikitrade.kcustomer.service.kyc.CustomerIdentityService;
import com.kikitrade.kcustomer.service.kyc.KycProperties;
import com.kikitrade.kcustomer.service.kyc.v1.onfido.OnfidoKycApiServiceImpl;
import com.kikitrade.kcustomer.service.kyc.ocr.OcrDataIndexDTO;
import com.onfido.exceptions.OnfidoException;
import io.grpc.stub.StreamObserver;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * @Author: ZhangPengfei
 * @Date: 2021/11/3 10:26 上午
 */
@SpringBootTest
@TestPropertySource("classpath:application-default.properties")
public class CustomerTest {
    @Autowired
    CustomerService customerService;
    //    @Autowired
//    RemoteCustomerService remoteCustomerService;
    @Autowired
    CustomerFacadeImpl customerFacade;
    @Autowired
    CustomerIdentityService customerIdentityService;
    @Autowired
    CustomerController customerController;
    @Resource
    KCustomerProperties properties;
    @Resource
    RemoteCustomerIdentityService remoteCustomerIdentityService;
    @Autowired
    CustomerStoreBuilder customerStoreBuilder;

    @Autowired
    CustomerIdentityBuilder customerIdentityBuilder;

    @Autowired
    OnfidoKycApiServiceImpl onfidoKycApiService;
    @Autowired
    KycProperties kycProperties;



    @Test
    public void testUpdateCustomer() throws InterruptedException {
        QueryCustomerRequest request = QueryCustomerRequest.newBuilder()
                .setId("2020102302412791177520").setSaasId("kiki").build();
        StreamObserver<QueryInviterResponse> responseObserver = new StreamObserver<QueryInviterResponse>() {
            @Override
            public void onNext(QueryInviterResponse queryInviterResponse) {
                System.out.println("onnext");
            }

            @Override
            public void onError(Throwable throwable) {

            }

            @Override
            public void onCompleted() {
                System.out.println("onCompleted");
            }
        };
        customerFacade.queryCustomerInviter(request, responseObserver);
        Thread.sleep(3000);
        System.out.println("end");
    }


    @Test
    public void changeIporDeviceTest() throws CustomerException {
//        OathLoginRequest request = OathLoginRequest.builder().ip("123456").deviceId("11112").build();
//        remoteCustomerService.oathLogin(request);
//        System.out.println("123");
    }

    @Test
    public void testGetByPhoneCertified() {
        String nextToken = "";
        for (int i = 0; i < 6; i++) {
            TokenPage<CustomerDO> customerDOS = customerService.getByPhoneCertified(nextToken, 10, "");
            nextToken = customerDOS.getNextToken();
            System.out.println(i + "------" + customerDOS);
        }
    }

    @Test
    public void level1Audit() {
        String parameter = "0";
        int day = NumberUtils.toInt(parameter);
        Date today0H = DateUtils.truncate(new Date(), Calendar.DATE);
        Date today24H = DateUtils.addDays(today0H, 1);

        Date end = DateUtils.addDays(today24H, -day);
        Date start = DateUtils.addDays(end, -1);
        if (day == 4) {//最后一个节点跑5天以外的
            start = new Date(0);
        }
        int offset = 0;
        // 查询kyc1审核中（证件识别+活体检测）的用户
        List<CustomerIdentityDO> list = customerIdentityService.listKyc1Auditing("kiki", CustomerIdentityConstants.KycStatus.L1_AUDITING, start, end, offset, 100);
        System.out.println(list);
    }

    @Test
    public void tets() {
        Map<String, String> regionConfig = properties.getLocationIndexConfig();
        String indexConfig = regionConfig.get(CustomerIdentityConstants.DocType.PH_ID.name());
        OcrDataIndexDTO ocrDataIndexDTO = JSONObject.parseObject(indexConfig, OcrDataIndexDTO.class);
        System.out.println(ocrDataIndexDTO);
    }

    @Test
    public void identify() {
        //台湾证件
        String file0Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/sean_front_1.jpg";
        String file1Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/sean_backgroup_1.jpg";
        CustomerIdentityConstants.DocType docType = CustomerIdentityConstants.DocType.TW_ID;
        //String file0Url = "http://upload.dipbit.xyz/2022042505435605258501/apply/4beb9efc6994411b8f3d87c8b3a1dbf2.jpg";
//        String file0Url = "http://upload.dipbit.xyz/2022042503070686758500/apply/7ff230311979434e82f444f87878b9cc.jpg";
//        String file1Url = "http://upload.dipbit.xyz/2022042408575450757502/apply/5e383a430d6440528572624bb83d9a91.jpg";

        // 护照
//        String file0Url = "https://upload.kikitrade.com/2020031813375019061502/apply/8749b1570d1a4ee2b00ae0f84b9febc5.jpg";
//        String file1Url = null;
//        CustomerIdentityConstants.DocType docType = CustomerIdentityConstants.DocType.PASSPORT;
        // 香港证件
//        String file0Url = "https://upload.kikitrade.com/2020091710493406077504/apply/f1cf9befcaca4fceb6a410e4c2b3c243.jpg";
//        String file1Url = null;
//        CustomerIdentityConstants.DocType docType = CustomerIdentityConstants.DocType.HK_ID;
        //马来证件
        //String file0Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/mykad-1.png";
        //String file0Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/my-1.JPG";
//        String file0Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/my_1.png";
//        //String file1Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/mykad-2.png";
//        String file1Url = null;
//        CustomerIdentityConstants.DocType docType = CustomerIdentityConstants.DocType.MY_ID;

        // 菲律宾
//        String file0Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/ph-1.png";
//        String file1Url = "https://kiki-dev.oss-ap-southeast-1.aliyuncs.com/ocr/ph-2.png";
//        CustomerIdentityConstants.DocType docType = CustomerIdentityConstants.DocType.PH_ID;
        CustomerIdentityDO identity = new CustomerIdentityDO();
        identity.setSaasId("kiki");
        identity.setCustomerId("0000001");
        //CustomerCommonResponse result = yundunService.uploadOcrFile(file0Url, file1Url, docType);
        //CustomerCommonResponse(result={"docType":14,"ocrFront":{"ocrInfo":["MyKad,021030-12-0330,CRYSTAL CHONG YING SAN,LOT NO E86,TAMAN T&G,JALAN LOTONG,WARGANEGARA ,89100 KOTA MARUDU,PEREMPUAN,SABAH"]},"ocrBack":{"ocrInfo":["KETUA PENGARAH,PENDAFTARAN NEGARA,000000-00-0000,64K,支有限,ATM,chip ,BELAKANG"]}})
        //CustomerCommonResponse(result={"docType":14,"ocrFront":{"ocrInfo":["MyKad,041210-13-0841,AWANG FADHLAN FAKRI BIN AWANG,DRAHMAN,LOT 1074,LORONG5,JALAN ALLAMANDA,WARGANEGARA,96100 SARIKEI,LELAKI,ISLAM,SARAWAK"]},"ocrBack":{"ocrInfo":["KETUA PENGARAH,PENDAFTARAN NEGARA,000000-00-0000,64K,支有限,ATM,chip ,BELAKANG"]}})
        //CustomerCommonResponse(result={"docType":14,"ocrFront":{"ocrInfo":["KADPENGENALAN,MyKad ,MALAYSIA,840403-12-5089,WONG KEH KAE ,NO1LORONG SELADANG,JALAN MAKTAB GAYA,WARGANEGARA,88300 KOTA KINABALU,LELAKI,SABAH"]},"ocrBack":{"ocrInfo":["KETUA PENGARAH,PENDAFTARAN NEGARA,000000-00-0000,64K,支有限,ATM,chip ,BELAKANG"]}})
        //CustomerCommonResponse(result={"docType":14,"ocrFront":{"ocrInfo":["MyKad,021030-12-0330,CRYSTAL CHONG YING SAN,LOT NO E86,TAMAN T&G,JALAN LOTONG,WARGANEGARA ,89100 KOTA MARUDU,PEREMPUAN,SABAH"]},"ocrBack":{"ocrInfo":["KETUA PENGARAH,PENDAFTARAN NEGARA,000000-00-0000,64K,支有限,ATM,chip ,BELAKANG"]}})
        // "ocrLocations":[{"w":128.0,"h":45.0,"x":593.0,"y":75.0,"text":"MyKad"},{"w":286.0,"h":37.0,"x":67.0,"y":171.0,"text":"021030-12-0330"},{"w":419.0,"h":37.0,"x":83.0,"y":413.0,"text":"CRYSTAL CHONG YING SAN"},{"w":148.0,"h":24.0,"x":85.0,"y":472.0,"text":"LOT NO E86"},{"w":162.0,"h":21.0,"x":83.0,"y":493.0,"text":"TAMAN T&G"},{"w":186.0,"h":30.0,"x":86.0,"y":512.0,"text":"JALAN LOTONG"},{"w":189.0,"h":21.0,"x":684.0,"y":537.0,"text":"WARGANEGARA "},{"w":268.0,"h":32.0,"x":83.0,"y":535.0,"text":"89100 KOTA MARUDU"},{"w":155.0,"h":22.0,"x":768.0,"y":558.0,"text":"PEREMPUAN"},{"w":119.0,"h":21.0,"x":87.0,"y":561.0,"text":"SABAH"}],"rate":99.91,"suggestion":"review","label":"ocr","scene":"ocr"}]
        // "ocrLocations":[{"w":120.0,"h":36.0,"x":545.0,"y":36.0,"text":"MyKad"},{"w":292.0,"h":33.0,"x":22.0,"y":119.0,"text":"041210-13-0841"},{"w":549.0,"h":30.0,"x":18.0,"y":366.0,"text":"AWANG FADHLAN FAKRI BIN AWANG"},{"w":158.0,"h":25.0,"x":18.0,"y":397.0,"text":"DRAHMAN"},{"w":129.0,"h":25.0,"x":14.0,"y":435.0,"text":"LOT 1074"},{"w":134.0,"h":24.0,"x":15.0,"y":460.0,"text":"LORONG5"},{"w":267.0,"h":25.0,"x":12.0,"y":484.0,"text":"JALAN ALLAMANDA"},{"w":187.0,"h":23.0,"x":652.0,"y":494.0,"text":"WARGANEGARA"},{"w":191.0,"h":24.0,"x":14.0,"y":509.0,"text":"96100 SARIKEI"},{"w":99.0,"h":22.0,"x":727.0,"y":518.0,"text":"LELAKI"},{"w":99.0,"h":24.0,"x":610.0,"y":519.0,"text":"ISLAM"},{"w":141.0,"h":24.0,"x":12.0,"y":533.0,"text":"SARAWAK"}],"rate":99.91,"suggestion":"review","label":"ocr","scene":"ocr"}]
        // "ocrLocations":[{"w":407.0,"h":36.0,"x":115.0,"y":33.0,"text":"KADPENGENALAN"},{"w":162.0,"h":46.0,"x":588.0,"y":52.0,"text":"MyKad "},{"w":398.0,"h":54.0,"x":118.0,"y":67.0,"text":"MALAYSIA"},{"w":330.0,"h":37.0,"x":42.0,"y":134.0,"text":"840403-12-5089"},{"w":259.0,"h":28.0,"x":44.0,"y":400.0,"text":"WONG KEH KAE "},{"w":368.0,"h":26.0,"x":42.0,"y":483.0,"text":"NO1LORONG SELADANG"},{"w":315.0,"h":23.0,"x":43.0,"y":509.0,"text":"JALAN MAKTAB GAYA"},{"w":200.0,"h":22.0,"x":679.0,"y":529.0,"text":"WARGANEGARA"},{"w":317.0,"h":24.0,"x":42.0,"y":535.0,"text":"88300 KOTA KINABALU"},{"w":114.0,"h":21.0,"x":748.0,"y":551.0,"text":"LELAKI"},{"w":101.0,"h":24.0,"x":44.0,"y":560.0,"text":"SABAH"}],
    }

    @Test
    public void update() {
        Map<String, Object> map = new HashMap<>();
        map.put("operate_user", "test");
        customerStoreBuilder.updateExtensionFields("2021112302425178205000", map);
    }

    @Test
    public void updateIdentity() {
        Map<String, Object> map = new HashMap<>();
        map.put("region", "HK");
        customerIdentityService.updateExtensionFields("kiki", "2021112302425178205000", map);
    }


    @Test
    public void getByStatusAndGoogleStatus() {
        List<CustomerDO> kiki = customerStoreBuilder.getByStatusAndGoogleStatus("kiki", null, null, 0, 100);
        System.out.println(kiki);
    }

    @Test
    public void clean() {
        boolean cleanCustomerById = customerService.cleanCustomerById("kiki", "1234", "test", null);
        System.out.println(cleanCustomerById);
    }

    @Test
    public void modifyKyc2Address(){
        boolean kiki = customerIdentityService.modifyCustomerKyc2Address(UpdateCustomerKyc2AddressRequest.builder().customerId("2020030403535126115500").sassId("kiki").build());
        System.out.println(kiki);
    }
}
