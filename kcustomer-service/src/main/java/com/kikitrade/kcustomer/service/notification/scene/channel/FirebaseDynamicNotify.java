package com.kikitrade.kcustomer.service.notification.scene.channel;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kikitrade.kcustomer.api.constants.FirebaseTemplateType;
import com.kikitrade.kcustomer.api.model.FirebaseMessageDTO;
import com.kikitrade.kcustomer.service.notification.scene.AbstractDynamicNotify;
import com.kikitrade.kcustomer.service.notification.scene.NotifySceneConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.kikitrade.kcustomer.common.constants.CustomerConstants.EMAIL;
import static com.kikitrade.kcustomer.common.constants.CustomerConstants.PHONE;

/**
 * <h3>返回返回值统一使用 {@link FirebaseMessageDTO}</h3>
 *
 * <AUTHOR>
 */
@Service
public class FirebaseDynamicNotify extends AbstractDynamicNotify {


    @Override
    public NotifySceneConstants.NotifyTypeEnum getNotifyType() {
        return NotifySceneConstants.NotifyTypeEnum.Firebase;
    }

    public FirebaseMessageDTO example(Map<String, Object> parameterMap) {
        return FirebaseMessageDTO.builder()
                .templateType(null)
                .customerId(getCustomer().getId())
                .build();
    }

    public FirebaseMessageDTO beforePhoneReplace(Map<String, Object> parameterMap) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(PHONE, getPhone());
        return FirebaseMessageDTO.builder()
                .type(FirebaseTemplateType.before_phone_replace.name())
                .titleKey(FirebaseTemplateType.before_phone_replace.titleKey())
                .bodyKey(FirebaseTemplateType.before_phone_replace.bodyKey())
                .templateType(FirebaseTemplateType.custom)
                .customerId(getCustomer().getId())
                .bodyArgs(Collections.singletonList(getPhone()))
                .params(jsonObject)
                .build();
    }

    public FirebaseMessageDTO beforeEmailReplace(Map<String, Object> parameterMap) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(EMAIL, getEmail());
        return FirebaseMessageDTO.builder()
                .type(FirebaseTemplateType.before_email_replace.name())
                .titleKey(FirebaseTemplateType.before_email_replace.titleKey())
                .bodyKey(FirebaseTemplateType.before_email_replace.bodyKey())
                .templateType(FirebaseTemplateType.custom)
                .customerId(getCustomer().getId())
                .bodyArgs(Collections.singletonList(getEmail()))
                .params(jsonObject)
                .build();
    }


    public FirebaseMessageDTO replaceBindEmailSuccess(Map<String, Object> parameterMap) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(EMAIL, getEmail());
        return FirebaseMessageDTO.builder()
                .type(FirebaseTemplateType.replace_bind_email_success.name())
                .titleKey(FirebaseTemplateType.replace_bind_email_success.titleKey())
                .bodyKey(FirebaseTemplateType.replace_bind_email_success.bodyKey())
                .templateType(FirebaseTemplateType.custom)
                .customerId(getCustomer().getId())
                .bodyArgs(Collections.singletonList(getEmail()))
                .params(jsonObject)
                .build();
    }

    public FirebaseMessageDTO replaceBindPhoneSuccess(Map<String, Object> parameterMap) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(PHONE, getPhone());
        return FirebaseMessageDTO.builder()
                .type(FirebaseTemplateType.replace_bind_phone_success.name())
                .titleKey(FirebaseTemplateType.replace_bind_phone_success.titleKey())
                .bodyKey(FirebaseTemplateType.replace_bind_phone_success.bodyKey())
                .templateType(FirebaseTemplateType.custom)
                .customerId(getCustomer().getId())
                .bodyArgs(Collections.singletonList(getPhone()))
                .params(jsonObject)
                .build();
    }


    public FirebaseMessageDTO kycSuccess(Map<String, Object> parameterMap) {
        JSONObject obj = new JSONObject();
        // kiki使用的消息参数
        obj.put("customerId", getCustomer().getId());
        obj.put("verified", true);
        // aspen使用的消息参数
        obj.put("userName", parameterMap.get("userName"));
        obj.put("time", System.currentTimeMillis());
        obj.put("deniedReason", parameterMap.get("deniedReason"));
        return FirebaseMessageDTO.builder()
                // aspen成功后跳转入金页面
                .type(FirebaseTemplateType.kyc_result_success.name())
                .titleKey(FirebaseTemplateType.kyc_result_success.titleKey())
                .bodyKey(FirebaseTemplateType.kyc_result_success.bodyKey())
                .templateType(FirebaseTemplateType.kyc_result_success)
                .customerId(getCustomer().getId())
                .params(obj)
                .build();
    }

    public FirebaseMessageDTO kycFail(Map<String, Object> parameterMap) {
        JSONObject obj = new JSONObject();
        // kiki使用的消息参数
        obj.put("customerId", getCustomer().getId());
        obj.put("verified", false);
        // aspen使用的消息参数
        obj.put("userName", parameterMap.get("userName"));
        obj.put("time", System.currentTimeMillis());
        obj.put("deniedReason", parameterMap.get("deniedReason"));
        return FirebaseMessageDTO.builder()
                .titleKey(FirebaseTemplateType.kyc_result_fail.titleKey())
                .bodyKey(FirebaseTemplateType.kyc_result_fail.bodyKey())
                .templateType(FirebaseTemplateType.custom)
                .customerId(getCustomer().getId())
                .params(obj)
                .bodyArgs(Collections.singletonList(parameterMap.get("deniedReason").toString()))
                .build();
}

    public FirebaseMessageDTO kyc2Success(Map<String, Object> parameterMap) {
        // 发送push
        JSONObject obj = new JSONObject();
        obj.put("customerId", getCustomer().getId());
        obj.put("verified", true);
        // aspen使用的消息参数
        obj.put("userName", parameterMap.get("userName"));
        obj.put("time", System.currentTimeMillis());
        obj.put("deniedReason", parameterMap.get("deniedReason"));
        return FirebaseMessageDTO.builder()
                .titleKey(FirebaseTemplateType.kyc2_result_success.titleKey())
                .bodyKey(FirebaseTemplateType.kyc2_result_success.bodyKey())
                .templateType(FirebaseTemplateType.kyc2_result_success)
                .customerId(getCustomer().getId())
                .params(obj)
                .build();
    }

    public FirebaseMessageDTO kyc2Fail(Map<String, Object> parameterMap) {
        // 发送push
        JSONObject obj = new JSONObject();
        obj.put("customerId", getCustomer().getId());
        obj.put("verified", false);
        // aspen使用的消息参数
        Optional.ofNullable(parameterMap.get("userName")).ifPresent(userName -> obj.put("userName", userName));
        Object deniedReason = parameterMap.get("deniedReason");
        List<String> bodyArgs = Lists.newArrayList();
        Optional.ofNullable(deniedReason).ifPresent(reason -> {
            obj.put("deniedReason", reason);
            bodyArgs.add(reason.toString());
        });
        obj.put("time", System.currentTimeMillis());
        return FirebaseMessageDTO.builder()
                .titleKey(FirebaseTemplateType.kyc2_result_fail.titleKey())
                .bodyKey(FirebaseTemplateType.kyc2_result_fail.bodyKey())
                .templateType(FirebaseTemplateType.custom)
                .customerId(getCustomer().getId())
                .params(obj)
                .bodyArgs(bodyArgs)
                .build();
    }

}
