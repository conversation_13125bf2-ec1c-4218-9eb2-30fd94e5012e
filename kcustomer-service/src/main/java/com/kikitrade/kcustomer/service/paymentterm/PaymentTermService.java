package com.kikitrade.kcustomer.service.paymentterm;

import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.PaymentTermDTO;
import com.kikitrade.kcustomer.api.model.PaymentTermSearchRequest;
import com.kikitrade.kcustomer.common.constants.PaymentTermConstant;
import com.kikitrade.kcustomer.dal.model.PaymentTermDO;

import java.util.List;

/**
 * 所有代码封装在service层，
 * 1. 不要给外部保留直接修改库的任何逻辑；
 * 2. 也不要使用给外部暴露一个大而全的update，发生的修改无法控制；
 * 注意各系统和服务之间的边界
 * remote里如果是做修改，只提供针对性的目的明确的业务处理
 */
public interface PaymentTermService {

    /**
     * 审核支付方式
     * @param paymentTermId
     * @param status status只支持
     * @param statusDescList
     * @return
     * @throws CustomerException
     */
    boolean auditPaymentTerm(String paymentTermId, String customerId, String outerId, PaymentTermConstant.Status status, List<String> statusDescList) throws CustomerException;

    /**
     * 通知管理端审核
     * @param paymentTermDTO
     * @return
     */
    boolean notifyManage(PaymentTermDTO paymentTermDTO);

    /**
     * 添加卡管理
     * @param paymentTermDO
     * @return
     * @throws CustomerException
     */
    PaymentTermDO addPaymentTerm(PaymentTermDO paymentTermDO) throws CustomerException;

    /**
     * 修改卡
     * @param paymentTermDO
     * @return
     * @throws CustomerException
     */
    Boolean modify(PaymentTermDO paymentTermDO) throws CustomerException;


    /**
     * 修改业务标签
     * @param saasId
     * @param customerId
     * @param id
     * @param business
     * @param operateType
     * @return
     * @throws CustomerException
     */
    Boolean modifyBusiness(String saasId, String customerId, String id, PaymentTermConstant.Usage business, PaymentTermConstant.OperateType operateType) throws CustomerException;

    /**
     * 用户删除卡
     * @param saasId
     * @param customerId
     * @param id
     * @return
     * @throws CustomerException
     */
    Boolean delete(String saasId,String customerId,String id) throws CustomerException;

    /**
     * 查询卡
     * @param saasId
     * @param paymentTermSearchRequest
     * @return
     * @throws CustomerException
     */
    List<PaymentTermDO> search(String saasId, PaymentTermSearchRequest paymentTermSearchRequest) throws CustomerException;

}
