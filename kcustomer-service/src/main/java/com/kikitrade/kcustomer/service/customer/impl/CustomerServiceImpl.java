package com.kikitrade.kcustomer.service.customer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.google.firebase.auth.*;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.framework.common.util.SecurityUtil;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.redis.lock.RedisDistributedLock;
import com.kikitrade.kcustomer.api.constants.*;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.*;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyRequest;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyResponse;
import com.kikitrade.kcustomer.common.constants.SmsTemplateType;
import com.kikitrade.kcustomer.common.constants.*;
import com.kikitrade.kcustomer.common.util.DateUtil;
import com.kikitrade.kcustomer.common.util.LocaleUtil;
import com.kikitrade.kcustomer.common.util.PhoneNumUtil;
import com.kikitrade.kcustomer.common.util.RegexChecker;
import com.kikitrade.kcustomer.dal.builder.CustomerBackupStoreBuilder;
import com.kikitrade.kcustomer.dal.builder.CustomerIdentityBuilder;
import com.kikitrade.kcustomer.dal.builder.CustomerOpenAuthBuilder;
import com.kikitrade.kcustomer.dal.builder.CustomerStoreBuilder;
import com.kikitrade.kcustomer.dal.model.*;
import com.kikitrade.kcustomer.dal.builder.*;
import com.kikitrade.kcustomer.dal.model.*;
import com.kikitrade.kcustomer.service.block.CustomerBlockBusinessService;
import com.kikitrade.kcustomer.service.cache.CustomerCacheKey;
import com.kikitrade.kcustomer.service.configuration.KCustomerProperties;
import com.kikitrade.kcustomer.service.customer.*;
import com.kikitrade.kcustomer.service.kyc.CountryUtil;
import com.kikitrade.kcustomer.service.mq.CustomerDelayEventService;
import com.kikitrade.kcustomer.service.notification.NotificationService;
import com.kikitrade.kcustomer.service.notification.scene.NotificationSceneService;
import com.kikitrade.kcustomer.service.notification.scene.NotifySceneConstants;
import com.kikitrade.kcustomer.service.reference.NotifyServiceReference;
import com.kikitrade.kcustomer.service.reference.SearchServiceReference;
import com.kikitrade.kcustomer.service.util.CommonUtil;
import com.kikitrade.kcustomer.service.util.CryptoUtils;
import com.kikitrade.kcustomer.service.util.ThirdAuthUtil;
import com.kikitrade.kcustomer.service.util.CustomerLoginContext;
import com.kikitrade.knotify.api.model.NotifyConstants;
import com.kikitrade.knotify.api.model.im.UserDTO;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.rule.CustomerSeqRuleBuilder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.kikitrade.kcustomer.api.constants.BusinessType.CUSTOMER_LOGIN;
import static com.kikitrade.kcustomer.common.constants.CustomerConstants.AccountType.EMAIL;
import static com.kikitrade.kcustomer.common.constants.CustomerConstants.AccountType.PHONE;
import static com.kikitrade.kcustomer.common.constants.CustomerConstants.*;

/**
 * @author: penuel
 * @date: 2021/7/14 16:14
 * @desc: TODO
 */
@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {


    @Autowired
    private CustomerVerifyService customerVerifyService;
    @Resource
    private CustomerInviteService customerInviteService;
    @Resource
    private CustomerStoreBuilder customerStoreBuilder;
    @Resource
    CustomerBackupStoreBuilder customerBackupStoreBuilder;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private NotificationService notificationService;
    @Resource
    private CustomerOpenAuthBuilder customerOpenAuthBuilder;
    @Resource
    private SeqClient seqClient;
    @Resource
    private KCustomerProperties kCustomerProperties;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private DeviceInfoService deviceInfoService;
    @Resource
    private CustomerIdentityBuilder customerIdentityBuilder;

    @Resource
    private NotifyServiceReference notifyServiceReference;

    @Resource
    private InviteCodeRuleService inviteCodeRuleService;

    @Resource
    private CustomerDelayEventService delayEventService;
    @Resource
    private RedisDistributedLock redisDistributedLock;
    @Autowired
    private CustomerBlockBusinessService customerBlockBusinessService;
    @Autowired
    private SearchServiceReference searchServiceReference;
    @Resource
    private NotificationSceneService notificationSceneService;

    @Resource
    private CustomerAuthAddressBuilder customerAuthAddressBuilder;

    @Autowired
    private ThirdAuthUtil thirdAuthUtil;

    @Override
    public CustomerDO getByAccountName(String saasId, String accountName) {
        boolean mail = RegexChecker.isMail(accountName);
        if (mail) {
            return getByEmail(saasId, accountName);
        } else {
            return getByPhone(saasId, accountName);
        }
    }

    @Override
    public CustomerDO getByPhone(String saasId, String phone) {
        return customerStoreBuilder.getByPhone(saasId, phone);
    }

    @Override
    public CustomerDO getByEmail(String saasId, String email) {
        return customerStoreBuilder.getByEmail(saasId, email);
    }

    @Override
    public CustomerDO register(CustomerRegisterRequest request) throws CustomerException {

        CustomerInviteDO inviter = null;
        if (StringUtils.isNotBlank(request.getInviteCode())) {
            inviter = customerInviteService.inviterByInviteCode(request.getSaasId(), request.getInviteCode());
            log.info("[inviterByInviteCode] inviteCode:{} inviter:{}", request.getInviteCode(), inviter);

            //duom逻辑：判断邀请码是否还有可用数量
            if (kCustomerProperties.isInviteCodeRule() && !inviteCodeRuleService.isAvailable(request.getInviteCode().toUpperCase())) {
                inviter = null;
            }
            if (null == inviter) {
                throw new CustomerException(CustomerMessageEnum.INCORRECT_INVITATION_CODE);
            }
        } else if (kCustomerProperties.isInviteCodeRule()) {
            throw new CustomerException(CustomerMessageEnum.INCORRECT_INVITATION_CODE);
        }

        //第三方登录不验证token
        if (CustomerConstants.Source.OPEN_AUTH != request.getSource()) {
            boolean verify = customerVerifyService.tokenVerify(request.getSaasId(), request.getVerifyToken(), request.getAccountName(), BusinessType.CUSTOMER_REGISTER);
            if (!verify) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_VERIFY_TYPE_INVALID);
            }
        }

        CustomerDO customerDO;
        switch (request.getAccountType()) {
            case EMAIL:
                customerDO = customerStoreBuilder.getByEmail(request.getSaasId(), request.getAccountName());
                break;
            case PHONE:
                customerDO = customerStoreBuilder.getByPhone(request.getSaasId(), request.getAccountName());
                break;
            case PUBLIC_KEY:
                customerDO = customerStoreBuilder.getByPublicKey(request.getSaasId(), request.getAccountName(), RegisterType.valueOf(request.getAccountType().name()));
                break;
            case DEVICE_ID:
                customerDO = this.getByDeviceId(request.getSaasId(), request.getDeviceId());
                break;
            default:
                throw new CustomerException(CustomerMessageEnum.SYSTEM_ERROR);
        }
        if (null != customerDO) {
            log.info("register customer is null accountName:{}", request.getAccountName());
            //三方注册时，如果已经注册则跳过，不应该抛错。因为customer_open_auth表可能还没生成绑定此用户信息的记录
            if (CustomerConstants.Source.OPEN_AUTH.equals(request.getSource())) {
                return customerDO;
            }
            throw new CustomerException(request.getAccountType() == CustomerConstants.AccountType.EMAIL ? CustomerMessageEnum.CUSTOMER_REG_EMAIL_DUPLICATE : CustomerMessageEnum.CUSTOMER_PHONE_DUPLICATE);
        }

        String customerId = seqClient.next(CustomerSeqRuleBuilder.rule());
        customerDO = new CustomerDO();
        customerDO.setId(customerId);
        customerDO.setSaasId(request.getSaasId());
        if (StringUtils.isNotBlank(request.getPassword())) {
            String md5Pwd = SecurityUtil.encrypt(request.getPassword(), SecurityUtil.ALGORITHM.MD5);
            customerDO.setPassword(md5Pwd);
        }
        if (request.getAccountType() == PHONE && StringUtils.isBlank(request.getCountry())) {
            try {
                request.setCountry(PhoneNumUtil.getPhoneNumberInfo(request.getAccountName()).getRegionCode());
            } catch (Exception e) {
                log.error("phone number format is incorrect, phone:{}", request.getAccountName());
                //throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_FORMAT_INCORRECT);
            }
        }
        customerDO.setRegion(StringUtils.isNotBlank(request.getCountry()) ? request.getCountry().toUpperCase() : request.getCountry());
        customerDO.setSource(null == request.getSource() ? CustomerConstants.Source.APP.getCode() : request.getSource().getCode());
        //设置注册设备类型
        String deviceType = request.getDeviceType();
        if (StringUtils.isNotBlank(deviceType)) {
            customerDO.setRegisterDeviceType(DeviceType.getByName(deviceType.toLowerCase()).getName());
        }
        Locale locale = null == request.getLocale() ? LocaleContextHolder.getLocale() : request.getLocale();
        customerDO.setLocale(CommonUtil.convertLanguageTag(locale));
        customerDO.setStatus(Optional.ofNullable(request.getActive()).orElse(true) ? CustomerConstants.Status.ACTIVATED.getCode() : CustomerConstants.Status.INACTIVE.getCode());
        if (CustomerConstants.Source.OPEN_AUTH.equals(request.getSource())
                && StringUtils.isBlank(customerDO.getRegion())
                && locale != null) {
            customerDO.setRegion(locale.getCountry());
        }
        customerDO.setOpenId(UUID.randomUUID().toString().replace("-", ""));
        String nickName = request.getNickName();
        if (StringUtils.isNotBlank(nickName)) {
            customerDO.setNickName(nickName);
            customerDO.setRegisterName(nickName);
        } else {
            String registerName = request.getRegisterName();
            customerDO.setRegisterName(registerName);
            customerDO.setNickName(registerName);
        }
        customerDO.setRegisterType(RegisterType.valueOf(request.getAccountType().name()).getCode());
        switch (request.getAccountType()) {
            case EMAIL:
                customerDO.setEmail(request.getAccountName());
                customerDO.setIsEmailCertified(true);
                break;
            case PHONE:
                customerDO.setPhone(request.getAccountName());
                customerDO.setIsPhoneCertified(true);
                break;
            case PUBLIC_KEY:
                customerDO.setPublicKey(request.getPublicKey());
                customerDO.setWalletAddress(request.getWalletAddress());
                break;
            case DEVICE_ID:
                break;
            default:
                throw new CustomerException(CustomerMessageEnum.SYSTEM_ERROR);
        }
        boolean create = customerStoreBuilder.create(customerDO);
        if (!create) {
            log.info("register create customer fail :{}", JSON.toJSONString(customerDO));
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_REG_FAIL);
        } else {
            log.info("register create customer success :{}", customerDO);
            CustomerDO finalCustomerDO = customerDO;
            CommonUtil.ifSecurityOpenOp(customerDO.getId(), () -> {
                if (StringUtils.isNotBlank(request.getDeviceId())) {
                    //数据库标记为常用设备
                    deviceInfoService.updateCommon(request.getDeviceId(), finalCustomerDO.getId());
                    log.info("register update device to common,deviceId:{},customerId:{}", request.getDeviceId(), finalCustomerDO.getId());
                    //设置redis 登陆时候校验设备使用 不用查库 校验完成删除
                    redisTemplate.opsForValue().set(CustomerCacheKey.REGISTER_DEVICE + request.getDeviceId(), "1", 1, TimeUnit.DAYS);
                }
            });
        }
        //发送注册事件
        onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                CustomerEvent
                        .builder()
                        .customerId(customerDO.getId())
                        .saasId(customerDO.getSaasId())
                        .eventTime(System.currentTimeMillis())
                        .eventType(request.getAccountType().equals(PHONE)
                                ? CustomerEvent.EventType.PhoneRegister
                                : (request.getAccountType().equals(EMAIL) ? CustomerEvent.EventType.EmailRegister : CustomerEvent.EventType.ThirdRegister))
                        .attachment(request.getAttachment())
                        .build().toJSONString()
        );

        log.info("[inviteCodeRuleService] reduceStock-begin:{}", customerDO);

        //duom逻辑：邀请码消费后，对可用次数进行扣减
        if (kCustomerProperties.isInviteCodeRule()) {
            InviteCodeRuleDTO inviteCodeRuleDTO = inviteCodeRuleService.reduceStock(inviter.getCode());
            log.info("[remoteInviteRuleService:reduceStock] inviteCodeRuleDTO :{}", inviteCodeRuleDTO);
            if (inviteCodeRuleDTO == null) {
                log.error("[remoteInviteRuleService:reduceStock] code:{} error! inviteCodeRuleDTO :{}", inviter.getCode(), inviteCodeRuleDTO);
                throw new CustomerException(CustomerMessageEnum.INVITE_CODE_USEUP);
            }
        }

        //async generate invite code
        customerInviteService.generateInviteCode(customerDO, inviter);

        //注册成功发送延时消息
        delayEventService.send(
                CustomerDelayEvent.builder()
                        .saasId(kCustomerProperties.getSaasId())
                        .customerId(customerDO.getId())
                        .data(System.currentTimeMillis())
                        .delayTime((new Date()).getTime())
                        .eventType(CustomerEvent.EventType.RegisteredForImmediately)
                        .build()
        );
        if (StringUtils.isNotBlank(request.getDeviceId())) {
            deviceInfoService.bindCustomerId(request.getDeviceId(), customerDO.getId());
            //场景化通知
            Map<String, Object> map = Maps.newHashMap();
            map.put(CUSTOMER_ID, customerId);
            map.put(IP, request.getIp());
            map.put(DEVICE_ID, request.getDeviceId());
            map.put(DEVICE_TOKEN, request.getDeviceToken());
            notificationSceneService.noticeDynamic(NotifySceneConstants.NotifySceneEnum.REGISTER, map);
        }
        log.info(SLSAlertConstants.DataMonitorEnum.REGISTER.getMonitor(), JSON.toJSONString(customerDO));
        return customerDO;
    }


    @Override
    public CustomerDO login(CustomerLoginRequest request) throws CustomerException {
        CustomerDO customer = null;
        if (request.getAccountType().equals(CustomerConstants.AccountType.DEVICE_ID)) {
            customer = this.getByDeviceId(request.getSaasId(), request.getDeviceId());
        } else {
            customer = this.getByAccountName(request.getSaasId(), request.getAccountName());
        }

        if (customer == null) {
            log.warn("customer login not found, loginName={}", request.getAccountName());
            JSONObject object = new JSONObject();
            object.put("errorCount", 1);
            object.put("updateTime", new Date());
            object.put("userName", request.getAccountName());
            throw new CustomerException(request.getAccountType() == CustomerConstants.AccountType.EMAIL ? CustomerMessageEnum.CUSTOMER_LOGIN_EMAIL_PASSWORD_ERROR : CustomerMessageEnum.CUSTOMER_LOGIN_PHONE_PASSWORD_ERROR, object);
        }

        long errorCount;
        if (isLocking(customer)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_LOCKED);
        }
        if(!CustomerLoginContext.LOGIN_TYPE_WEB.equals(request.getPushTokenType())&&(CustomerConstants.Type.COMPANY_SUB.getCode()==customer.getType()||
            CustomerConstants.Type.THIRD_PARTY_MERCHANT.getCode()==customer.getType()||
                    CustomerConstants.Type.THIRD_PARTY_MERCHANT_SUB.getCode()==customer.getType())){
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_LOGIN_INCORRECT);
        }
        boolean loginSuccess = false;
        if (StringUtils.isNotEmpty(request.getPassword())) {
            if (StringUtils.isNotEmpty(customer.getPassword())) {
                String md5Pwd = SecurityUtil.encrypt(request.getPassword(), SecurityUtil.ALGORITHM.MD5);
                if ((md5Pwd).equals(customer.getPassword())) {
                    loginSuccess = true;
                }
            } else {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_PASSWORD_NOT_SET);
            }
        }
        //vibra增加手机号验证码登录功能
        String saasId = customer.getSaasId();
        if (StringUtils.isNotEmpty(request.getVerifyToken()) && StringUtils.isEmpty(request.getPassword())) {
            CustomerConstants.VerifyType verifyType = request.getVerifyType();
            CustomerCommonResponse response = null;
            switch (verifyType) {
                case PHONE -> {
                    if (request.getAccountType() == PHONE) {
                        response = customerVerifyService.verifySmsCode(saasId, request.getAccountName(), request.getVerifyToken(), CUSTOMER_LOGIN, request.getSmsType());
                    }
                }
                case EMAIL -> {
                    response = customerVerifyService.verifyEmailCode(saasId, request.getAccountName(), request.getVerifyToken(), CUSTOMER_LOGIN);
                }
            }

            if (response != null) {
                if (response.isSuccess()) {
                    loginSuccess = true;
                }
                //清除已经验证成功的验证码
                switch (verifyType) {
                    case PHONE -> {
                        if (request.getAccountType() == PHONE) {
                            redisTemplate.delete(CustomerCacheKey.VERIFY_CODE_BY_PHONE + customer.getPhone());
                        }
                    }
                    case EMAIL -> redisTemplate.delete(CustomerCacheKey.VERIFY_CODE_BY_EMAIL + customer.getEmail());
                }
            }

        }
        // 设备id登录
        if (request.getAccountType().equals(CustomerConstants.AccountType.DEVICE_ID)) {
            loginSuccess = true;
        }

        String customerId = customer.getId();
        SecurityVerifyResponse securityVerifyResponse = customerBlockBusinessService.securityVerify(new SecurityVerifyRequest(customer.getId(), null, null, SecurityVerifyScene.LOGIN, false));
        if (!securityVerifyResponse.isSuccess()) {
            throw new CustomerException(securityVerifyResponse.getMsg());
        }
        if (loginSuccess) {
            if (null != customer.getIsLock() && customer.getIsLock()) {
                customerStoreBuilder.updateLock(saasId, customerId, false, new Date());
            }
            //成功登录清除错误记录
            redisTemplate.delete(CustomerCacheKey.LOGIN_ERROR_COUNT + customerId);
            redisTemplate.delete(CustomerCacheKey.LOGIN_ERROR_TIME + customerId);
            //发送登陆事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent
                            .builder()
                            .customerId(customerId)
                            .saasId(saasId)
                            .eventTime(System.currentTimeMillis())
                            .eventType(request.getAccountType().equals(PHONE)
                                    ? CustomerEvent.EventType.PhoneLogin
                                    : CustomerEvent.EventType.EmailLogin)
                            .build().toJSONString()
            );
            //场景化通知
            Map<String, Object> map = Maps.newHashMap();
            map.put(CUSTOMER_ID, customerId);
            map.put(IP, request.getIp());
            map.put(DEVICE_ID, request.getDeviceId());
            map.put(DEVICE_TOKEN, request.getDeviceToken());
            notificationSceneService.noticeDynamic(NotifySceneConstants.NotifySceneEnum.LOGIN, map);
        } else {
            String latestTimeValue = redisTemplate.opsForValue().get(CustomerCacheKey.LOGIN_ERROR_TIME + customerId);
            long currentTimeMillis = System.currentTimeMillis();
            redisTemplate.opsForValue().set(CustomerCacheKey.LOGIN_ERROR_TIME + customerId, String.valueOf(currentTimeMillis), Duration.of(CustomerConstants.ERROR_FREEZE_INTERVAL_IN_MIN * 60, ChronoUnit.SECONDS));

            if (StringUtils.isNotBlank(latestTimeValue)) {
                redisTemplate.opsForValue().set(CustomerCacheKey.LOGIN_ERROR_TIME + customerId, String.valueOf(currentTimeMillis), Duration.of(CustomerConstants.ERROR_FREEZE_INTERVAL_IN_MIN * 60, ChronoUnit.SECONDS));
                errorCount = Objects.requireNonNull(redisTemplate.opsForValue().increment(CustomerCacheKey.LOGIN_ERROR_COUNT + customerId, 1));

            } else {
                //超过临时冻结累计时间了， redis自动清除掉之后 要从1开始重新计数， 所以要把以前的累计计数清除
                redisTemplate.delete(CustomerCacheKey.LOGIN_ERROR_COUNT + customerId);
                errorCount = Objects.requireNonNull(redisTemplate.opsForValue().increment(CustomerCacheKey.LOGIN_ERROR_COUNT + customerId, 1));
            }

            if (errorCount >= CustomerConstants.LOGIN_MAX_ERROR_COUNT) {
                Date lockTime = new Date();
                customerStoreBuilder.updateLock(saasId, customerId, true, lockTime);
                //锁定后， 删除临时错误次数
//                redisTemplate.delete(CustomerCacheKey.LOGIN_ERROR_COUNT + customer.getUserName());
                if (request.getAccountType() == CustomerConstants.AccountType.EMAIL) {
                    //email time VIBRA使用
                    Map<String, Object> param = new HashMap<>();
                    param.put("email", customer.getEmail());
                    param.put("time", DateUtil.dateToUTCString(new Date()));
                    param.put(NotifyConstants.CONTENT, DateFormatUtils.format(lockTime, "yyyy-MM-dd HH:mm:ss"));
                    notificationService.send(EmailNotificationDTO.builder()
                            .saasId(saasId).templateId(EmailTemplateType.LOGIN_LOCK.name())
                            .customerId(customerId)
                            .parameterMap(param).build());
                } else {
                    Map<String, Object> param = new HashMap<>();
                    param.put("time", DateFormatUtils.format(lockTime, "yyyy-MM-dd HH:mm:ss"));
                    notificationService.send(SmsNotificationDTO.builder()
                            .customerId(customerId).saasId(saasId)
                            .templateId(SmsTemplateType.PWD_INCORRECT_LOCK.name())
                            .parameterMap(param).build());
                }
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_LOCKED);
            } else {
                JSONObject object = new JSONObject();
                object.put("errorCount", errorCount);
                object.put("updateTime", new Date());
                object.put("userName", request.getAccountName());
                throw new CustomerException(request.getAccountType() == CustomerConstants.AccountType.EMAIL ? CustomerMessageEnum.CUSTOMER_LOGIN_EMAIL_PASSWORD_ERROR : CustomerMessageEnum.CUSTOMER_LOGIN_PHONE_PASSWORD_ERROR, object);
            }
        }
        return customer;
    }

    @Override
    public boolean resetPassword(String saasId, String customerId, String oldPassword, String newPassword) throws CustomerException {
        CustomerDO customer = customerStoreBuilder.getById(saasId, customerId);
        if (null == customer) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_RESET_PASSWORD_ERROR);
        }
        if (StringUtils.isNotBlank(oldPassword)) {
            String md5Pwd = SecurityUtil.encrypt(oldPassword, SecurityUtil.ALGORITHM.MD5);
            if (!customer.getPassword().equals(md5Pwd)) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_RESET_PASSWORD_ERROR);
            }
        }
        String md5NewPwd = SecurityUtil.encrypt(newPassword, SecurityUtil.ALGORITHM.MD5);
        customer.setPassword(md5NewPwd);
        //设置修改密码时间
        Date now = new Date();
        customer.setModifyPasswordTime(now);
        return customerStoreBuilder.update(customer);
    }

    @Override
    public CustomerDO getById(String saasId, String customerId) {
        return customerStoreBuilder.getById(saasId, customerId);
    }

    @Override
    public List<CustomerDO> getByLocale(Integer offset, Integer limit, String locale) {
        return customerStoreBuilder.getByLocale(offset, limit, locale);
    }

    @Override
    public void setAssetPwd(String saasId, String customerId, String assetPwd, AssetPwdOpType type) throws CustomerException {
        CustomerDO customer = customerStoreBuilder.getById(saasId, customerId);
        if (Objects.isNull(customer)) {
            throw new CustomerException(CustomerMessageEnum.SYSTEM_ERROR);
        }
        if (AssetPwdOpType.SET.equals(type) && Objects.nonNull(customer.getAssetPassword())) {
            throw new CustomerException(CustomerMessageEnum.SYSTEM_ERROR);
        }
        String md5NewPwd = SecurityUtil.encrypt(assetPwd, SecurityUtil.ALGORITHM.MD5);
        //只有修改资金密码更新最后修改时间
        boolean updateModifyTime = !AssetPwdOpType.SET.equals(type);
        boolean result = customerStoreBuilder.updateAssetPassword(customerId, md5NewPwd, updateModifyTime);
        if (updateModifyTime) {
            //异步添加发红包拦截
            customerBlockBusinessService.createDailySystemBlocks(
                    customerId,CustomerMessageEnum.CUSTOMER_NOT_ALLOWED_RED_PACKET_AFTER_ASSET_PWD_RESET.getKey(),
                    SecurityVerifyScene.RED_POCKET);
        }
        if (!result) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_SET_ASSET_PWD_FAIL);
        }
    }

    @Override
    public TokenPage<CustomerDO> listCustomer(String saasId, Integer limit, String nextToken) {
        return customerStoreBuilder.listCustomer(saasId, limit, nextToken);
    }

    @Override
    public boolean updateMiscInfo(UpdateInfoRequest request, CustomerDO customerDO) throws CustomerException {
        boolean locked = redisDistributedLock.tryLock(CustomerCacheKey.NICK_NAME_LIMIT + request.getCustomerId(), Duration.ofSeconds(3));//防止因前端按钮频繁点击，触发多次
        if (!locked) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MODIFY_NICKNAME_FAIL);
        }
        boolean result;
        try {
            Map<String, Object> fieldsMap = new HashMap<>();
            allowNickName(customerDO, request.getNickName(), fieldsMap);
            Optional.ofNullable(request.getAvatar()).ifPresent(avatar -> fieldsMap.put("avatar", avatar));
            Optional.ofNullable(request.getGender()).ifPresent(gender -> fieldsMap.put("gender", gender));
            result = customerStoreBuilder.updateExtensionFields(request.getCustomerId(), fieldsMap);
        } catch (Exception e) {
            throw e;
        } finally {
            redisDistributedLock.unlock(CustomerCacheKey.NICK_NAME_LIMIT + request.getCustomerId());
        }
        if (result) {
            //发送用户信息更新事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent
                            .builder().customerId(request.getCustomerId()).saasId(request.getSaasId())
                            .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.UpdateMiscInfo)
                            .build().toJSONString()
            );
            //异步更新IM用户资料
            CompletableFuture.runAsync(() -> updateImUserAsync(request.getSaasId(), request.getCustomerId()));
        }
        return result;
    }

    private void updateImUserAsync(String saasId, String customerId) {
        CustomerOpenAuthDO openAuth = this.buildCustomerOpenAuthDO(customerId, saasId);
        CustomerOpenAuthDO existOpenAuth = customerOpenAuthBuilder.get(openAuth);
        if (existOpenAuth == null) {
            log.warn("updateImUserAsync auth not exists,customerId:{}", customerId);
            return;
        }
        try {
            updateImUser(existOpenAuth, customerId, getCustomerLocale(saasId, customerId), customerId);
        } catch (CustomerException e) {
            log.warn("updateImUserAsync auth error,customerId:{}", customerId);
        }
    }

    @Override
    public TokenPage<CustomerDO> getByPhoneCertified(String nextToken, Integer limit, String customerId) {
        return customerStoreBuilder.getByPhoneCertified(nextToken, limit, customerId);
    }

    @Override
    public boolean update(CustomerDO customer) {
        return customerStoreBuilder.update(customer);
    }

    /**
     * 调用者需要对type进行枚举校验，此接口内部不进行校验
     *
     * @param customer
     * @return
     */
    @Override
    public boolean updateType(CustomerDO customer) {
        return customerStoreBuilder.updateType(customer.getSaasId(), customer.getType(), customer.getId());
    }

    @Override
    public List<CustomerDO> listByIds(String saasId, List<String> customerId) {
        return customerStoreBuilder.getByIds(saasId, customerId);
    }

    @Override
    public List<CustomerDO> getByUsernames(String saasId, List<String> usernames) {
        //这就就不单独提供根据手机号和邮箱批量查询的方法了
        return usernames.parallelStream().map(username -> {
            boolean mail = RegexChecker.isMail(username);
            if (mail) {
                return customerStoreBuilder.getByEmail(saasId, username);
            } else {
                return customerStoreBuilder.getByPhone(saasId, username);
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public CustomerOpenAuthDO verifyOpenAuthIdToken(String saasId, String thirdPlatformToken, String publicKey, CustomerConstants.ThirdAuthSource thirdAuthSource) {
        return verifyIdToken(saasId, thirdPlatformToken, publicKey, thirdAuthSource);
    }

    @Override
    public CustomerDO bindOpenAuthUser(CustomerOpenAuthDO openAuth, CustomerDO customer, String country, CustomerConstants.ThirdAuthSource thirdAuthSource, String walletAddress, String walletType) throws CustomerException {
        if (StringUtils.isNotBlank(openAuth.getCustomerId())) {
            //已经绑定过了
            customer = getById(openAuth.getSaasId(), openAuth.getCustomerId());
            return customer;
        }
        // 计算 walletAddress
        if (StringUtils.isBlank(walletAddress) && StringUtils.isNotBlank(walletType)) {
            walletAddress = thirdAuthUtil.getAddressByWeb3authPublicKey(openAuth.getUid(), WalletType.fromName(walletType));
            log.info("bindOpenAuthUser, walletAddress is null, re-calculate walletAddress, result={}", walletAddress);
        }

        if (customer == null) {
            String md5Pwd = SecurityUtil.encrypt(RandomStringUtils.randomAlphabetic(10), SecurityUtil.ALGORITHM.MD5);
            CustomerRegisterRequest registerRequest = CustomerRegisterRequest.builder()
                    .accountName(openAuth.getEmail()).accountType(CustomerConstants.AccountType.EMAIL)
                    .country(country)
                    .source(CustomerConstants.Source.OPEN_AUTH)
                    .password(md5Pwd).saasId(openAuth.getSaasId()).build();
            //其他外部注册方式
            if (thirdAuthSource != null) {
                registerRequest.setAccountName(openAuth.getUid());
                registerRequest.setAccountType(CustomerConstants.AccountType.PUBLIC_KEY);
                registerRequest.setPassword("");
                registerRequest.setPublicKey(openAuth.getUid());
                if (StringUtils.isBlank(walletType) || StringUtils.equals(walletType, WalletType.sui.name())) {
                    registerRequest.setWalletAddress(walletAddress);
                }
            }

            customer = register(registerRequest);
        }

        openAuth.setCustomerId(customer.getId());
        openAuth.setWalletAddresses(JSON.toJSONString(Collections.singletonMap(walletType, walletAddress)));

        if (!customerOpenAuthBuilder.create(openAuth)) {
            log.error("bindOpenAuthUser, create openAuth fail, openAuth={}", JSON.toJSONString(openAuth));
            return null;
        }

        // 构造并保存 CustomerAuthAddressDO
        boolean bind = true;
        if (StringUtils.isNotBlank(walletAddress)) {
            CustomerAuthAddressDO addressDO = buildCustomerAuthAddress(openAuth, walletType, walletAddress);
            log.info("bindOpenAuthUser, addressDO={}, openAuth={}, walletType={}, walletAddress={}", JSON.toJSONString(addressDO), JSON.toJSONString(openAuth), walletType, walletAddress);
            bind = customerAuthAddressBuilder.create(addressDO);
        }
        return bind ? customer : null;
    }

    private CustomerAuthAddressDO buildCustomerAuthAddress(CustomerOpenAuthDO openAuth, String walletType, String walletAddress) {
        CustomerAuthAddressDO addressDO = new CustomerAuthAddressDO();
        addressDO.setCustomerId(openAuth.getCustomerId());
        addressDO.setPlatform(openAuth.getPlatform());
        addressDO.setChannel(walletType);
        addressDO.setPublicKey(openAuth.getUid());
        addressDO.setAddress(walletAddress);
        addressDO.setStatus(AddressStatus.active.name());
        addressDO.setBindTime(new Date());
        addressDO.setSaasId(openAuth.getSaasId());
        return addressDO;
    }

    @Override
    public TokenPage<CustomerDO> listByIdCertifiedStatus(String saasId, List<Integer> idStatus, String nextToken, int limit) {
        return customerStoreBuilder.listByIdCertifiedStatus(saasId, idStatus, nextToken, limit);
    }

    @Override
    public boolean updateIdCertified(UpdateIdCertifiedRequest request) throws CustomerException {
        CustomerIdentityConstants.KycLevel kycLevel = request.getKycLevel();
        log.info("updateIdCertified start, request:{}", request);
        CustomerDO customer = customerStoreBuilder.getById(request.getSaasId(), request.getCustomerId());
        if (null == customer) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
        }
        String region = request.getRegion();
        if (StringUtils.isNotBlank(region)) {
            CountryUtil.Country countryAlpha = CountryUtil.parseCountry(region, CountryUtil.CountryCodeType.ALPHA2);
            if (null == countryAlpha) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_KYC_REJECT_UNSUPPORTED_ID_COUNTRY);
            }
            customer.setRegion(countryAlpha.getAlpha2());
        }
        String gender = request.getGender();
        if (StringUtils.isNotBlank(gender)) {
            customer.setGender(CustomerConstants.Gender.fromValue(gender).getCode());
        }
        if (request.getIdStatus() != null) {
            customer.setIdCertifiedStatus(request.getIdStatus().getCode());
        }
        if (CustomerConstants.IdStatus.SUCCESS.equals(request.getIdStatus())) {
            customer.setKycLevel(CustomerIdentityConstants.KycLevel.L1.name());
        } else {
            customer.setKycLevel(CustomerIdentityConstants.KycLevel.L0.name());
        }
        if (request.getKycLevel() != null) {
            customer.setKycLevel(request.getKycLevel().name());
            if (null == request.getIdStatus() && request.getKycLevel() == CustomerIdentityConstants.KycLevel.L2) {
                customer.setIdCertifiedStatus(CustomerConstants.IdStatus.SUCCESS.getCode());
            }
        }
        if (null != request.getCertifiedTime()) {
            customer.setIdCertifiedTime(request.getCertifiedTime());
        }
        return customerStoreBuilder.update(customer);
    }

    @Override
    public void checkRegisterParams(CustomerRegisterRequest registerRequest) throws CustomerException {
        checkaccountNameAndPassword(registerRequest.getAccountType(), registerRequest.getAccountName(), registerRequest.getPassword());
    }

    @Override
    public void checkQuickRegisterParams(CustomerRegisterRequest registerRequest) throws CustomerException {
        String smsCodeRegister = null;
        if (registerRequest.getAccountType() == PHONE) {
            smsCodeRegister = "1";
        }
        checkAccountNamePasswordAndSmsCode(registerRequest.getAccountType(), registerRequest.getAccountName(), registerRequest.getPassword(), smsCodeRegister);
    }

    @Override
    public void checkLoginParams(CustomerLoginRequest loginRequest) throws CustomerException {
        checkaccountNameAndPassword(loginRequest.getAccountType(), loginRequest.getAccountName(), loginRequest.getPassword());
//        TODO kiki must
//        if (StringUtils.isEmpty(loginRequest.getDeviceId()) || StringUtils.isEmpty(loginRequest.getPushToken()) || StringUtils.isEmpty(loginRequest.getPushTokenType()) || "android,web,ios".indexOf(loginRequest.getPushToken()) < 0) {
//            throw new CustomerException(CustomerMessageEnum.CUSTOMER_LOGIN_PARAMETER_INCORRECT);
//        }
    }

    @Override
    public void checkQuickLoginParams(CustomerLoginRequest loginRequest) throws CustomerException {
        checkAccountNamePasswordAndSmsCode(loginRequest.getAccountType(), loginRequest.getAccountName(), loginRequest.getPassword(), loginRequest.getVerifyToken());
    }

    private void checkaccountNameAndPassword(CustomerConstants.AccountType accountType, String accountName, String password) throws CustomerException {
        switch (accountType) {
            case PHONE:
                if (StringUtils.isBlank(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_REQUIRED);
                }
                if (!RegexChecker.isMobile(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_FORMAT_INCORRECT);
                }
                break;
            case EMAIL:
                if (StringUtils.isBlank(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_EMAIL_REQUIRED);
                }
                if (!RegexChecker.isMail(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_EMAIL_FORMAT_INCORRECT);
                }
                break;
            default:
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_REG_FAIL);
        }

        if (StringUtils.isBlank(password)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PASSWORD_REQUIRED);
        }
        //密码前端加密成MD5，这里MD5码不符合正则规则
//        if (!RegexChecker.isPassword(password)) {
//            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PASSWORD_FORMAT_INCORRECT);
//        }
    }


    private void checkAccountNamePasswordAndSmsCode(CustomerConstants.AccountType accountType, String accountName, String password, String smsCode) throws CustomerException {
        switch (accountType) {
            case PHONE:
                if (StringUtils.isBlank(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_REQUIRED);
                }
                if (!RegexChecker.isMobile(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_FORMAT_INCORRECT);
                }
                break;
            case EMAIL:
                if (StringUtils.isBlank(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_EMAIL_REQUIRED);
                }
                if (!RegexChecker.isMail(accountName)) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_EMAIL_FORMAT_INCORRECT);
                }
                break;
            case DEVICE_ID:
                break;
            default:
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_REG_FAIL);
        }
        if (!accountType.equals(CustomerConstants.AccountType.DEVICE_ID) && StringUtils.isBlank(password) && StringUtils.isBlank(smsCode)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PASSWORD_REQUIRED);
        }
    }

    @Override
    public boolean isLocking(CustomerDO customer) {
        if (null == customer) {
            return false;
        }
        boolean isLock = null != customer.getIsLock() && customer.getIsLock();
        if (null == customer.getLockTime()) {
            return false;
        }
        return isLock && customer.getLockTime().after(DateUtils.addHours(new Date(), -1 * CustomerConstants.LOCK_INTERVAL_IN_HOUR));
    }

    @Override
    public Locale getCustomerLocale(String saasId, String customerId) throws CustomerException {
        return customerStoreBuilder.getCustomerLocale(saasId, customerId);
    }


    @Override
    public boolean updateLocale(String saasId, String customerId, Locale locale) throws CustomerException {
        locale = LocaleUtil.convertLocal(locale.toLanguageTag());
        CustomerDO customer = customerStoreBuilder.getById(saasId, customerId);
        if (null == customer) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
        }
        String languageTag = CommonUtil.convertLanguageTag(locale);
        if (languageTag.equals(customer.getLocale())) {
            return true;
        }
        boolean result = customerStoreBuilder.updateLocale(saasId, customer.getId(), languageTag);
        //异步更新IM用户资料
        CompletableFuture.runAsync(() -> updateImUserAsync(saasId, customerId));
        return result;
    }


    @Override
    public boolean addPassword(String saasId, String customerId, String password) throws CustomerException {
        CustomerDO customer = customerStoreBuilder.getById(saasId, customerId);
        if (StringUtils.isBlank(customer.getPassword()) && StringUtils.isNotBlank(password)) {
            String md5Pwd = SecurityUtil.encrypt(password, SecurityUtil.ALGORITHM.MD5);
            customer.setPassword(md5Pwd);

        }
        return customerStoreBuilder.update(customer);
    }

    @Override
    public boolean cleanCustomerById(String saasId, String customerId, String operator, CustomerDO customerDO) {
        if (customerDO == null) {
            customerDO = customerStoreBuilder.getById(saasId, customerId);
        }
        customerDO.setOperator(operator);
        CustomerBackupDO customerBackupDO = new CustomerBackupDO();
        BeanUtil.copyProperties(customerDO, customerBackupDO);
        boolean createResult = customerBackupStoreBuilder.create(customerBackupDO);
        boolean result = false;
        if (createResult) {
            result = customerStoreBuilder.cleanById(customerDO, operator);
            customerOpenAuthBuilder.cleanByCustomerId(customerId);
        }
        if (result) {
            // 更新kycStatus为-1
            result = customerIdentityBuilder.updateKycStatus(saasId, customerId, CustomerIdentityConstants.KycStatus.DELETED);
            searchServiceReference.deleteFace(customerId, ImgSearchKeyConstants.KYC1_FACE);
        }
        return result;
    }

    @SuppressWarnings(value = "all")
    private CustomerOpenAuthDO verifyIdToken(String saasId, String idToken, String publicKey, CustomerConstants.ThirdAuthSource thirdAuthSource) {
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            CustomerOpenAuthDO openAuth = null;
            if (StringUtils.isNotBlank(publicKey)) {
                openAuth = thirdAuthUtil.web3AuthVerify(saasId, publicKey, idToken, thirdAuthSource);
            } else {
                openAuth = thirdAuthUtil.firebaseVerify(saasId, idToken);
            }
            log.info(SLSAlertConstants.DataMonitorEnum.OAUTH_LOGIN.getMonitor(), JSONObject.toJSONString(idToken), JSONObject.toJSONString(openAuth), stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return openAuth;
        } catch (FirebaseAuthException e) {
            log.warn("verifyIdToken idToken fail: {}", idToken, e);
            return null;
        } catch (Throwable e) {
            log.warn("verifyIdToken exception, idToken:{},input publicKey:{}", idToken, publicKey, e);
            return null;
        }
    }


    @Override
    public CustomerDO tryUpdateVipLevel(UpdateVipRequest vipRequest) throws CustomerException {
        CustomerDO customer = customerStoreBuilder.getById(vipRequest.getSaasId(), vipRequest.getCustomerId());
        if (customer == null) {
            throw new CustomerException(CustomerMessageEnum.SYSTEM_DATA_NOT_FOUND);
        }

        if (StringUtils.isBlank(customer.getVipLevel()) && !CustomerConstants.VipLevel.L0.equals(vipRequest.getVipLevel())) {
            log.warn("tryUpdateVipLevel customer oldVipLevel not match,vipRequest:{},CustomerDO:{}", JSON.toJSONString(vipRequest), JSON.toJSONString(customer));
            throw new CustomerException(CustomerMessageEnum.SYSTEM_PARAMETER_INVALID);
        }

        if (StringUtils.isNotBlank(customer.getVipBusinessId())) {
            if (customer.getVipBusinessId().equals(vipRequest.getVipBusinessId())) {
                return customerStoreBuilder.getById(vipRequest.getSaasId(), vipRequest.getCustomerId());
            }
            log.warn("tryUpdateVipLevel customer has been lock,vipRequest:{},CustomerDO:{}", JSON.toJSONString(vipRequest), JSON.toJSONString(customer));
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_VIP_TRY_UPDATE_ERROR);
        }
        //L0代表目前的普通用户
        String oldVipLevel = CustomerConstants.VipLevel.getLevelStr(vipRequest.getVipLevel());
        if (customer.getVipLevel() == null) {
            oldVipLevel = null;
        }
        return customerStoreBuilder.lockOrReleaseVipLevel(vipRequest.getSaasId(), vipRequest.getCustomerId(), oldVipLevel, vipRequest.getVipBusinessId(), "", false);
    }

    @Override
    public CustomerDO updateVipLevel(UpdateVipRequest vipRequest) throws CustomerException {
        CustomerDO customer = customerStoreBuilder.getById(vipRequest.getSaasId(), vipRequest.getCustomerId());
        if (customer == null) {
            throw new CustomerException(CustomerMessageEnum.SYSTEM_DATA_NOT_FOUND);
        }
        //取消更新
        if (vipRequest.isCancel()) {
            return cancelDeal(customer, vipRequest);
        }

        //已经处理过的认为成功
        if (StringUtils.isBlank(customer.getVipBusinessId()) && vipRequest.getVipLevel().equals(CustomerConstants.VipLevel.get(customer.getVipLevel()))) {
            return customer;
        }

        String newVipLevel = CustomerConstants.VipLevel.getLevelStr(vipRequest.getVipLevel());
        Date vipEndTime = new Date(vipRequest.getVipEndTime());
        //如果最低级则设置已到期
        if (CustomerConstants.VipLevel.getRemovalLevel().equals(vipRequest.getVipLevel())) {
            vipEndTime = new Date();
        }
        CustomerDO customerDO = customerStoreBuilder.updateVipLevel(vipRequest.getSaasId(), vipRequest.getCustomerId(), newVipLevel, "", vipRequest.getVipBusinessId(), vipEndTime, null, null);

        //到期时间变更发送延时消息
        if (customerDO != null && customerDO.getVipEndTime() != null) {
            delayEventService.send(
                    CustomerDelayEvent.builder()
                            .saasId(kCustomerProperties.getSaasId())
                            .customerId(customerDO.getId())
                            .data(customerDO)
                            .delayTime(customerDO.getVipEndTime().getTime())
                            .eventType(CustomerEvent.EventType.VipExpired)
                            .build());
        }

        //发送用户信息变成消息
        onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                CustomerEvent
                        .builder().customerId(customer.getId()).saasId(customer.getSaasId())
                        .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.UpdateVipLevel)
                        .build().toJSONString()
        );
        return customerDO;
    }

    private CustomerDO cancelDeal(CustomerDO customer, UpdateVipRequest vipRequest) {
        if (!vipRequest.getVipBusinessId().equals(customer.getVipBusinessId())) {
            return customer;
        }
        return customerStoreBuilder.lockOrReleaseVipLevel(vipRequest.getSaasId(), vipRequest.getCustomerId(), null, "", vipRequest.getVipBusinessId(), true);
    }

    @Override
    public boolean processVipExpire(CustomerDO customerDO) {
        try {
            log.info("processVipExpire params,customerDO:{}", JSON.toJSONString(customerDO));
            //判断是否未到期，继续延期发送mq；上层listener已经做了继续发送延期消息，为了兼容老逻辑先保留，后面可以删掉这个判断
            if (new Date().before(customerDO.getVipEndTime())) {
                log.info("processVipExpire resend delay msg,customerDO:{}", JSON.toJSONString(customerDO));
                if (customerDO.getVipEndTime() != null) {
                    delayEventService.send(
                            CustomerDelayEvent.builder()
                                    .saasId(kCustomerProperties.getSaasId())
                                    .customerId(customerDO.getId())
                                    .data(customerDO)
                                    .delayTime(customerDO.getVipEndTime().getTime())
                                    .eventType(CustomerEvent.EventType.VipExpired)
                                    .build()
                    );
                }
                return true;
            }

            //降级策略是否合法
            String vipExpired = kCustomerProperties.getVipExpired();
            if (StringUtils.isBlank(vipExpired)) {
                log.info("processVipExpire vipExpired is empty,customerDO:{},vipExpired:{}", JSON.toJSONString(customerDO), vipExpired);
                return true;
            }
            CustomerConstants.VipExpiredStrategy vipExpiredStrategy = CustomerConstants.VipExpiredStrategy.get(vipExpired);
            if (vipExpiredStrategy == null) {
                log.warn("processVipExpire VipExpiredStrategy illegal,customerDO:{},VipExpiredStrategy:{}", JSON.toJSONString(customerDO), vipExpired);
                return false;
            }

            CustomerDO customerFromDb = customerStoreBuilder.getById(customerDO.getSaasId(), customerDO.getId());
            if (customerFromDb == null) {
                log.info("processVipExpire customerFromDb is null,customerDO:{}", JSON.toJSONString(customerDO));
                return false;
            }
            //已是最低级不需要降级
            if (CustomerConstants.VipLevel.getRemovalLevel().equals(CustomerConstants.VipLevel.get(customerFromDb.getVipLevel()))) {
                log.info("processVipExpire vipLevel already lowest, customerDO:{},customerFromDb:{}", JSON.toJSONString(customerDO), JSON.toJSONString(customerFromDb));
                return true;
            }

            //db已经被更新过
            if (!customerDO.getVipLevel().equals(customerFromDb.getVipLevel()) || !customerDO.getVipEndTime().equals(customerFromDb.getVipEndTime())) {
                log.info("processVipExpire customerFromDb has been modified,{}", JSON.toJSONString(customerDO));
                return true;
            }

            //已被锁定or还没到过期时间
            if (customerFromDb.getVipEndTime().after(new Date()) || StringUtils.isNotBlank(customerFromDb.getVipBusinessId())) {
                log.error("processVipExpire customer locked or vipEndTime not reach, customerDO:{},customerFromDb:{}", JSON.toJSONString(customerDO), JSON.toJSONString(customerFromDb));
                return false;
            }

            CustomerConstants.VipLevel newLevel = null;
            switch (vipExpiredStrategy) {
                //降至最低
                case removal:
                    newLevel = CustomerConstants.VipLevel.getRemovalLevel();
                    //等级一致不需要更新
                    if (customerFromDb.getVipLevel().equals(CustomerConstants.VipLevel.getLevelStr(newLevel))) {
                        log.info("processVipExpire removal, vipLevel is removal ,{}", JSON.toJSONString(customerFromDb));
                        return true;
                    }
                    break;
                default:
                    log.error("processVipExpire VipExpiredStrategy is invalid ,customer:{},VipExpiredStrategy:{}", JSON.toJSONString(customerFromDb), vipExpired);
                    return false;
            }

            //L0代表目前的普通用户
            String newVipLevel = CustomerConstants.VipLevel.getLevelStr(newLevel);
            CustomerDO updatedCustomer = customerStoreBuilder.updateVipLevel(customerDO.getSaasId(), customerDO.getId(), newVipLevel, "", "", new Date(), customerFromDb.getVipLevel(), customerFromDb.getVipEndTime());
            //发送用户信息变更事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent
                            .builder().customerId(customerDO.getId()).saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.UpdateVipLevel)
                            .build().toJSONString()
            );
            log.info("processVipExpire consume success,customerFromDb:{},updatedCustomer:{}", JSON.toJSONString(customerFromDb), JSON.toJSONString(updatedCustomer));
            return true;
        } catch (Exception e) {
            log.error("processVipExpire consume failed, message:{}", JSON.toJSONString(customerDO), e);
            return false;
        }
    }


    @Override
    public SignInfoDTO sign(String saasId, String deviceId, String customerId) throws CustomerException {
        String deviceImUid = SecurityUtil.encrypt(deviceId + "uid", SecurityUtil.ALGORITHM.MD5);
        String tencentImUserId = this.getTencentImUid(deviceImUid, customerId);

        String userSig = getUserSig(tencentImUserId);
        if (StringUtils.isBlank(userSig)) {
            throw new CustomerException(CustomerMessageEnum.USER_SIGN_FAIL);
        }

        CustomerOpenAuthDO openAuth = this.buildCustomerOpenAuthDO(tencentImUserId, saasId);
        customerOpenAuthBuilder.create(openAuth);
        if (StringUtils.isNotBlank(customerId)) {
            CustomerOpenAuthDO deviceOpenAuth = this.buildCustomerOpenAuthDO(deviceId, saasId);
            customerOpenAuthBuilder.delete(deviceOpenAuth);
        }

        return SignInfoDTO.builder().sign(userSig).imUid(tencentImUserId).build();
    }

    /**
     * 生成IM usersign
     *
     * @param tencentImUserId
     * @return
     */
    private String getUserSig(String tencentImUserId) {
//        String key = CustomerCacheKey.IM_USER_SIGN + tencentImUserId;
//        String userSig = redisTemplate.opsForValue().get(key);
//        if(StringUtils.isNotBlank(userSig)){
//            return userSig;
//        }
        return notifyServiceReference.getUserSig(tencentImUserId);
//        if(StringUtils.isNotBlank(userSig)){
//            redisTemplate.opsForValue().set(key, userSig, Duration.ofDays(7));
//            return userSig;
//        }
//        return "";
    }

    private CustomerOpenAuthDO buildCustomerOpenAuthDO(String tencentImUserId, String saasId) {
        CustomerOpenAuthDO openAuth = new CustomerOpenAuthDO();
        openAuth.setUid(tencentImUserId);
        openAuth.setProvider(CustomerConstants.Platform.tencent_im.name());
        openAuth.setPlatform(CustomerConstants.Platform.tencent_im.name());
        openAuth.setSaasId(saasId);
        openAuth.setBindTime(new Date());
        return openAuth;
    }

    @Override
    public boolean bind(String saasId, String deviceId, String customerId, String imUid, Locale locale) throws CustomerException {
        //locale字段只有未登录kiki时才需要传入更新
        if (StringUtils.isBlank(customerId) && locale == null) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_LOGIN_REQUIRED);
        }
        if (StringUtils.isBlank(imUid)) {
            throw new CustomerException(CustomerMessageEnum.USER_SIGN_NOT_EXIST);
        }
        if (StringUtils.isNotBlank(customerId) && !customerId.equals(imUid)) {
            log.warn("bind customerId not match imUid, deviceId:{}, customerId:{}, imUid:{}", deviceId, customerId, imUid);
            return false;
        }
        CustomerOpenAuthDO openAuth = this.buildCustomerOpenAuthDO(imUid, saasId);
        CustomerOpenAuthDO existOpenAuth = customerOpenAuthBuilder.get(openAuth);
        if (existOpenAuth == null) {
            log.warn("bind imUid of db not exists, deviceId:{}, customerId:{}, imUid:{}", deviceId, customerId, imUid);
            throw new CustomerException(CustomerMessageEnum.USER_SIGN_NOT_EXIST);
        }
        if (locale != null) {
            try {
                locale = LocaleUtil.convertLocal(locale.toLanguageTag());
            } catch (Exception e) {
                log.warn("im bind, locale is illegal, deviceId:{}, customerId:{}, imUid:{}, locale:{}", deviceId, customerId, imUid, locale, e);
            }
        }
        Locale finalLocale = locale;
        CompletableFuture.runAsync(() -> updateImUser(existOpenAuth, customerId, finalLocale, imUid));
        return true;
    }

    private String getTencentImUid(String deviceImUid, String customerId) {
        //默认用设备ID生成tencentImUserId
        String tencentImUserId = deviceImUid;
        if (StringUtils.isNotBlank(customerId)) {
            //已是登录状态，使用customerId作为tencentImUserId去签名，并返回给客户端imUid为customerId
            tencentImUserId = customerId;
        }
        return tencentImUserId;
    }

    /**
     * 更新个人资料、打标签，并绑定customerId至customer_open_auth表
     *
     * @param existOpenAuth
     * @param imUid
     */
    private void updateImUser(CustomerOpenAuthDO existOpenAuth, String customerId, Locale locale, String imUid) {
        log.info("updateImUser,existOpenAuth:{},customerId:{},imUid:{}", JSON.toJSONString(existOpenAuth), customerId, imUid);
        if (StringUtils.isBlank(customerId) && locale != null) {
            boolean updateImUser = notifyServiceReference.updateImUser(UserDTO.builder().userId(existOpenAuth.getUid()).locale(locale).build());
            log.info("updateImUser,imUid:{},customerId:{},result:{}", existOpenAuth.getUid(), customerId, updateImUser);
            return;
        }
        //等昵称头像数据存表有数据
        CustomerDO customerDO = getById(existOpenAuth.getSaasId(), customerId);
        UserDTO userDTO = UserDTO.builder().userId(existOpenAuth.getUid()).avatar(customerDO.getAvatar()).nickName(customerDO.getNickName()).locale(convertLocal(customerDO.getLocale())).build();
        //第一期默认不打标签
        boolean updateImUser = notifyServiceReference.updateImUser(userDTO);
        log.info("updateImUser,imUid:{},customerId:{},userDTO:{},result:{}", existOpenAuth.getUid(), customerId, JSON.toJSONString(userDTO), updateImUser);
        if (updateImUser && !customerId.equals(existOpenAuth.getCustomerId())) {
            existOpenAuth.setCustomerId(customerId);
            customerOpenAuthBuilder.updateCustomerId(existOpenAuth);
        }
    }

    private Locale convertLocal(String localeStr) {
        Locale locale = Locale.US;
        if (StringUtils.isNotBlank(localeStr)) {
            String languageTag = localeStr.replace("_", "-");
            locale = LocaleUtil.convertLocal(languageTag);
        }
        return locale;
    }

    @Override
    public boolean processUserConversion(String saasId, String customerId, CustomerEvent.EventType eventType) {
        log.info("processUserConversion customerId [{}],eventType [{}],", customerId, eventType);
        CustomerDO customerDO = getById(saasId, customerId);
        if (customerDO == null) {
            return true;
        }
        switch (eventType) {
            case RegisteredForImmediately:
                if (checkAndSend(customerDO, saasId, eventType, EmailTemplateType.REGISTERED_FOR_IMMEDIATELY)) {
                    //send delay
                    delayEventService.send(
                            CustomerDelayEvent.builder()
                                    .saasId(kCustomerProperties.getSaasId())
                                    .customerId(customerDO.getId())
                                    .data(System.currentTimeMillis())
                                    .delayTime(DateUtils.addDays(customerDO.getCreateTime(), 1).getTime())
                                    .eventType(CustomerEvent.EventType.RegisteredForOneDay)
                                    .build()
                    );
                }
                break;
            case RegisteredForOneDay:
                if (checkAndSend(customerDO, saasId, eventType, EmailTemplateType.REGISTERED_FOR_ONE_DAY)) {
                    //send delay
                    delayEventService.send(
                            CustomerDelayEvent.builder()
                                    .saasId(kCustomerProperties.getSaasId())
                                    .customerId(customerDO.getId())
                                    .data(System.currentTimeMillis())
                                    .delayTime(DateUtils.addDays(customerDO.getCreateTime(), 7).getTime())
                                    .eventType(CustomerEvent.EventType.RegisteredForSevenDays)
                                    .build()
                    );
                }
                break;
            case RegisteredForSevenDays:
                if (checkAndSend(customerDO, saasId, eventType, EmailTemplateType.REGISTERED_FOR_SEVEN_DAYS)) {
                    //send delay
                    delayEventService.send(
                            CustomerDelayEvent.builder()
                                    .saasId(kCustomerProperties.getSaasId())
                                    .customerId(customerDO.getId())
                                    .data(System.currentTimeMillis())
                                    .delayTime(DateUtils.addDays(customerDO.getCreateTime(), 14).getTime())
                                    .eventType(CustomerEvent.EventType.RegisteredForFourteenDays)
                                    .build()
                    );
                }
                break;
            case RegisteredForFourteenDays:
                checkAndSend(customerDO, saasId, eventType, EmailTemplateType.REGISTERED_FOR_FOURTEEN_DAYS);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public CustomerDO getByDeviceId(String saasId, String deviceId) {
        DeviceInfoDO deviceInfoDO = deviceInfoService.findById(deviceId);
        if (deviceInfoDO == null) {
            return null;
        }
        return customerStoreBuilder.getById(saasId, deviceInfoDO.getCustomerId());
    }

    @Override
    public CustomerDO getByPublicKey(String saasId, String publicKey, RegisterType registerType) {
        return customerStoreBuilder.getByPublicKey(saasId, publicKey, registerType);
    }

    @Override
    public Map<String, String> queryWalletAddress(CustomerDO customer) {
        // 这里为了兼容老版本sdk，代码暂时先不移除
        CustomerOpenAuthDO existOpenAuth = customerOpenAuthBuilder.query(customer.getSaasId(), Platform.web3auth.name(), customer.getPublicKey(), customer.getId());
        if (existOpenAuth == null) {
            return null;
        }
        Map addressMap = JSON.parseObject(existOpenAuth.getWalletAddresses(), Map.class);
        if (addressMap == null) {
            addressMap = new HashMap();
        }
        // 覆盖老数据，如果是新版本sdk，这里会覆盖并返回最新的地址
        List<CustomerAuthAddressDO> list = customerAuthAddressBuilder.query(customer.getId(), Platform.web3auth.name());
        for (CustomerAuthAddressDO addressDO : list) {
            addressMap.put(addressDO.getChannel(), addressDO.getAddress());
        }
        return addressMap;
    }

    @Override
    public boolean bindWalletAddress(AddressBindRequest request) throws CustomerException {
        CustomerDO customer = getById(request.getSaasId(), request.getCustomerId());
        if (customer == null) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
        }

        // 解析publicKey
        String publicKey = thirdAuthUtil.getPublicKeyFromWeb3auth(request.getPlatform(), request.getThirdAuthSource(), request.getThirdPlatformToken());

        // 校验该publicKey是否已经被绑定过了
        List<CustomerAuthAddressDO> addressList = customerAuthAddressBuilder.queryByPublicKey(publicKey);
        boolean exist = false;
        CustomerAuthAddressDO authAddressDO = null;
        if (addressList != null && !addressList.isEmpty()) {
            for (CustomerAuthAddressDO addressDO : addressList) {
                // 其他人已经绑定了
                if (!addressDO.getCustomerId().equals(request.getCustomerId())) {
                    throw new CustomerException(CustomerMessageEnum.CUSTOMER_LOGIN_3RD_EXISTED);
                }
                // 完全一样，说明之前绑定过了
                if (addressDO.getPlatform().equalsIgnoreCase(request.getPlatform().name())
                        && addressDO.getChannel().equalsIgnoreCase(request.getWalletType().name())) {
                    // 该地址目前正在被使用，直接返回
                    if (addressDO.getStatus().equalsIgnoreCase(CustomerConstants.AddressStatus.active.name())) {
                        return true;
                    } else {
                        // 有可能是 上上次绑定的，比如：最开始是evm，然后换绑成sui，然后又换绑回evm，此时evm是inactive状态，需要更新成active
                        exist = true;
                        authAddressDO = addressDO;
                    }
                }
            }
        }

        // 获取换绑之前正在使用的地址
        CustomerAuthAddressDO oldAddress = customerAuthAddressBuilder.getByStatus(request.getCustomerId(), AddressStatus.active.name());

        // upsert
        if (exist) {
            authAddressDO.setStatus(AddressStatus.active.name());
            customerAuthAddressBuilder.updateStatus(authAddressDO);
        } else {
            authAddressDO = new CustomerAuthAddressDO();
            authAddressDO.setCustomerId(request.getCustomerId());
            authAddressDO.setPlatform(request.getPlatform().name());
            authAddressDO.setChannel(request.getWalletType().name());
            authAddressDO.setPublicKey(publicKey);
            authAddressDO.setAddress(thirdAuthUtil.getAddressByWeb3authPublicKey(publicKey, request.getWalletType()));
            authAddressDO.setStatus(AddressStatus.active.name());
            authAddressDO.setBindTime(new Date());
            authAddressDO.setSaasId(request.getSaasId());
            authAddressDO.setModified(new Date());
            customerAuthAddressBuilder.create(authAddressDO);
        }

        // 更新用户的publicKey,address
        customerStoreBuilder.updatePublicKeyAndAddress(request.getCustomerId(), publicKey, authAddressDO.getAddress());

        // 把换绑之前的地址记录改成inactive状态
        if (oldAddress != null) {
            oldAddress.setStatus(AddressStatus.inactive.name());
            customerAuthAddressBuilder.updateStatus(oldAddress);
        }
        return true;
    }

    private boolean checkSignature(String address, String walletType, String nonce, String publicKey, String signature) {
        String message = String.format("address=%s&nonce=%s&publicKey=%s&walletType=%s", address, nonce, publicKey, walletType);
        return CryptoUtils.checkEvmPersonalSign(signature, message, address);
    }

    @Override
    public CustomerDO bindAuth(AuthBindRequest request) throws CustomerException {
        // 验签
        if (!checkSignature(request.getAddress(), request.getWalletType().name(), request.getNonce(), request.getPublicKey(), request.getSignature())) {
            log.error("bindAuth fail, signature check fail, request={}", JSON.toJSONString(request));
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_3RD_SIGNATURE_ERROR);
        }

        // 同一个账户，从evm和bsc上计算出的地址，字母的大小写不一样，这里统一处理。walletAddress保存原始的数值，因为要提币用
        String formattedPublicKey = formatPublicKey(request.getPublicKey());

        // 校验该publicKey是否已经被绑定过了
        List<CustomerAuthAddressDO> addressList = customerAuthAddressBuilder.queryByPublicKey(formattedPublicKey);
        if (addressList != null && !addressList.isEmpty()) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PUBLIC_KEY_ALREADY_BINDED);
        }

        // 校验用户是否已经绑定过了
        CustomerDO customer = getById(request.getSaasId(), request.getCustomerId());
        if (customer == null) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
        } else if (StringUtils.isNotBlank(customer.getPublicKey())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_ALREADY_BINDED);
        }

        // 插入绑定记录
        CustomerAuthAddressDO authAddressDO = new CustomerAuthAddressDO();
        authAddressDO.setCustomerId(request.getCustomerId());
        authAddressDO.setPlatform(request.getPlatform().name());
        authAddressDO.setChannel(request.getWalletType().name());
        authAddressDO.setPublicKey(formattedPublicKey);
        authAddressDO.setAddress(request.getAddress());
        authAddressDO.setStatus(AddressStatus.active.name());
        authAddressDO.setBindTime(new Date());
        authAddressDO.setSaasId(request.getSaasId());
        authAddressDO.setModified(new Date());
        customerAuthAddressBuilder.create(authAddressDO);

        // 更新用户的publicKey
        customerStoreBuilder.updatePublicKeyAndAddress(request.getCustomerId(), formattedPublicKey, request.getAddress());

        customer.setPublicKey(formattedPublicKey);
        customer.setWalletAddress(request.getAddress());
        return customer;
    }

    // 同一个账户，从evm和bsc上计算出的地址，字母的大小写不一样，这里统一处理
    private String formatPublicKey(String publicKey) {
        return publicKey != null ? publicKey.toLowerCase() : null;
    }

    private boolean checkSignature(String walletType, String nonce, String publicKey, String signature) {
        String message = String.format("nonce=%s&publicKey=%s&walletType=%s", nonce, publicKey, walletType);
        return CryptoUtils.checkEvmPersonalSign(signature, message, publicKey);
    }

    @Override
    public CustomerDO loginAuth(AuthLoginRequest request) throws CustomerException {
        if (!checkSignature(request.getWalletType().name(), request.getNonce(), request.getPublicKey(), request.getSignature())) {
            log.error("loginAuth fail, signature check fail, request={}", JSON.toJSONString(request));
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_3RD_SIGNATURE_ERROR);
        }

        log.info("loginAuth loginAuthRequest:{}", JSON.toJSONString(request));
        try {
            // 同一个账户，从evm和bsc上计算出的地址，字母的大小写不一样，这里统一处理。walletAddress保存原始的数值，因为要提币用
            String formattedPublicKey = formatPublicKey(request.getPublicKey());
            String walletAddress = request.getPublicKey();

            // 因为用户的regist_type不一定是PUBLIC_KEY，所以不能直接用this.getByPublicKey方法进行查询
            List<CustomerAuthAddressDO> list = customerAuthAddressBuilder.queryByPublicKey(formattedPublicKey);
            if (list != null && !list.isEmpty()) {
                CustomerAuthAddressDO customerAuthAddress = list.get(0);
                return getById(customerAuthAddress.getSaasId(), customerAuthAddress.getCustomerId());
            }

            // 不存在时注册新用户
            CustomerRegisterRequest registerRequest = CustomerRegisterRequest.builder()
                    .source(Source.OPEN_AUTH)
                    .accountName(formattedPublicKey)
                    .accountType(AccountType.PUBLIC_KEY)
                    .password("")
                    .publicKey(formattedPublicKey)
                    .walletAddress(walletAddress)
                    .saasId(request.getSaasId())
                    .build();

            CustomerDO customer = register(registerRequest);
            log.info("loginAuth, customer register finish, customer={}", JSON.toJSONString(customer));

            // 插入绑定记录
            CustomerAuthAddressDO authAddressDO = new CustomerAuthAddressDO();
            authAddressDO.setCustomerId(customer.getId());
            authAddressDO.setPlatform(request.getPlatform().name());
            authAddressDO.setChannel(request.getWalletType().name());
            authAddressDO.setPublicKey(formattedPublicKey);
            authAddressDO.setAddress(walletAddress);
            authAddressDO.setStatus(AddressStatus.active.name());
            authAddressDO.setBindTime(new Date());
            authAddressDO.setSaasId(request.getSaasId());
            authAddressDO.setModified(new Date());
            customerAuthAddressBuilder.create(authAddressDO);

            return customer;
        } catch (CustomerException ce) {
            log.error("loginAuth exception, request={}", JSON.toJSONString(request), ce);
            throw ce;
        } catch (Exception e) {
            log.error("loginAuth exception, request:{}", JSON.toJSONString(request), e);
            throw new CustomerException(CustomerMessageEnum.SYSTEM_ERROR);
        }
    }

    private boolean checkAndSend(CustomerDO customerDO, String saasId, CustomerEvent.EventType eventType, EmailTemplateType emailTemplateType) {
        if (new Date().after(DateUtils.addDays(customerDO.getCreateTime(), 15))) {
            log.error("processUserConversion check error,the maximum sending time was exceeded,{},{}", customerDO.getId(), eventType);
            return false;
        }
        if (CustomerIdentityConstants.KycLevel.L0.name().equals(customerDO.getKycLevel())) {
            boolean sendEmail = notificationService.send(
                    EmailNotificationDTO.builder().saasId(saasId).customerId(customerDO.getId()).templateId(emailTemplateType.name()).build());
            log.info("processUserConversion customerId [{}],eventType [{}],sendEmail [{}],", customerDO.getId(), eventType, sendEmail);
            return true;
        }
        return false;
    }

    private void allowNickName(CustomerDO customerDO, String nickName, Map<String, Object> fieldsMap) throws CustomerException {
        if (StringUtils.isEmpty(nickName)) {
            return;
        }
        if (StringUtils.isEmpty(kCustomerProperties.getNicknameModifyRule())) {
            fieldsMap.put("nick_name", nickName);
            fieldsMap.put("modify_nickname_time", Arrays.asList(System.currentTimeMillis()));
            return;
        }

        if (CollectionUtils.isEmpty(customerDO.getModifyNicknameTime())) {
            fieldsMap.put("nick_name", nickName);
            fieldsMap.put("modify_nickname_time", Arrays.asList(System.currentTimeMillis()));
            return;
        }
        String[] rule = kCustomerProperties.getNicknameModifyRule().split("/");
        int limitTimes = Integer.parseInt(rule[0]);
        int limitTime = Integer.parseInt(rule[1]) * 24 * 60 * 60 * 1000;
        Long currentTime = System.currentTimeMillis();
        List<Long> times = customerDO.getModifyNicknameTime().stream().filter(time -> currentTime - time <= limitTime).collect(Collectors.toList());
        if (times.size() < limitTimes) {
            times.add(System.currentTimeMillis());
            fieldsMap.put("nick_name", nickName);
            fieldsMap.put("modify_nickname_time", times);
            return;
        } else {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MODIFY_NICKNAME_FAIL, times.get(0));
        }

    }

    @Override
    public boolean upsertCustomer(UpsertCustomerRequest request) throws CustomerException {
        log.info("createCustomer request {}", JSON.toJSONString(request));
        CustomerDO customerDO;
        if (StringUtils.isNotEmpty(request.getId())) {
            customerDO = customerStoreBuilder.getById(request.getSaasId(), request.getId());
            if (null == customerDO) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
            }
            setUpsertCustomer(request, customerDO);
            CustomerIdentityDO customerIdentityDO = customerIdentityBuilder.getByCustomerId(request.getSaasId(), customerDO.getId());
            customerIdentityDO.setContactAddress(request.getContactAddress());
            log.info("update customerDO {}", JSON.toJSONString(customerDO));
            log.info("update customerIdentityDO {}", JSON.toJSONString(customerIdentityDO));
            customerIdentityBuilder.update(customerIdentityDO);
            return customerStoreBuilder.update(customerDO);
        } else {

            switch (request.getAccountType()) {
                case EMAIL:
                    customerDO = customerStoreBuilder.searchByAccountName(request.getSaasId(), request.getAccountType(), request.getEmail());
                    break;
                case PHONE:
                    customerDO = customerStoreBuilder.searchByAccountName(request.getSaasId(), request.getAccountType(), request.getPhone());
                    break;
                default:
                    throw new CustomerException(CustomerMessageEnum.SYSTEM_ERROR);
            }
            if (null != customerDO) {
                log.info("create customer is not null accountName:{}", CustomerConstants.AccountType.EMAIL.equals(request.getAccountType()) ? request.getEmail() : request.getPhone());
                throw new CustomerException(request.getAccountType() == CustomerConstants.AccountType.EMAIL ? CustomerMessageEnum.CUSTOMER_REG_EMAIL_DUPLICATE : CustomerMessageEnum.CUSTOMER_PHONE_DUPLICATE);
            }
            customerDO = new CustomerDO();
            setUpsertCustomer(request, customerDO);
            String customerId = seqClient.next(CustomerSeqRuleBuilder.rule());
            customerDO.setId(customerId);
            customerDO.setRegisterType(CustomerConstants.AccountType.EMAIL.equals(request.getAccountType()) ? RegisterType.EMAIL.getCode() : RegisterType.PHONE.getCode());
            CustomerIdentityDO customerIdentityDO = customerIdentityBuilder.getByCustomerId(request.getSaasId(), customerDO.getId());
            customerIdentityDO.setContactAddress(request.getContactAddress());
            log.info("create customerDO {}", JSON.toJSONString(customerDO));
            log.info("create customerIdentityDO {}", JSON.toJSONString(customerIdentityDO));
            customerIdentityBuilder.update(customerIdentityDO);
            return customerStoreBuilder.create(customerDO);
        }
    }

    private void setUpsertCustomer(UpsertCustomerRequest request, CustomerDO customerDO) {
        CountryUtil.Country regionAlpha = CountryUtil.parseCountry(request.getRegion());
        customerDO.setSaasId(request.getSaasId());
        customerDO.setEmail(request.getEmail());
        customerDO.setPhone(request.getPhone());
        customerDO.setNickName(request.getNickName());
        customerDO.setGender(request.getGender().getCode());
        customerDO.setKycLevel(request.getKycAuthLevel().name());
        if (null != regionAlpha) {
            customerDO.setRegion(regionAlpha.getAlpha2());
        }
        customerDO.setType(request.getType().getCode());
        customerDO.setStatus(request.getStatus().getCode());
    }

    @Override
    public List<CustomerDO> getByEmailLike(String saasId, String email) {
        return customerStoreBuilder.getByEmailLike(saasId, email);
    }

    @Override
    public TokenPage<CustomerDO> pageByEmailLike(String saasId, String email, String nextToken, int limit) {
        return customerStoreBuilder.pageByEmailLike(saasId, email, nextToken, limit);
    }

    @Override
    public CustomerDTO convertToCustomerDTO(CustomerDO customerDO) {
        if (null == customerDO) {
            return null;
        }
        CustomerDTO customerDTO = BeanUtil.copyProperties(customerDO, CustomerDTO::new);
        if (StringUtils.isNotEmpty(customerDO.getKycLevel())) {
            customerDTO.setKycAuthLevel(CustomerIdentityConstants.KycLevel.valueOf(customerDO.getKycLevel()));
        }
        if (StringUtils.isNotEmpty(customerDO.getVipLevel())) {
            customerDTO.setVipLevel(CustomerConstants.VipLevel.valueOf(customerDO.getVipLevel()));
        }
        return customerDTO;
    }

    @Override
    public CustomerDO searchByAccountName(String saasId, AccountType accountType, String accountName) {
        return customerStoreBuilder.searchByAccountName(saasId, accountType, accountName);
    }
}
