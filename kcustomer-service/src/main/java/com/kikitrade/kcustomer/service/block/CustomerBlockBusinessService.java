package com.kikitrade.kcustomer.service.block;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kcustomer.api.constants.SecurityVerifyScene;
import com.kikitrade.kcustomer.api.model.block.*;
import com.kikitrade.kcustomer.dal.model.CustomerBlockBusinessDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomerBlockBusinessService {

    /**
     * 系统添加拦截记录
     *
     * @param customerId customerId
     * @param scene 业务场景
     * @param endTime 截止时间
     * @param reasonKey 原因
     */
    void createSystemBlock(String customerId, SecurityVerifyScene scene, Date endTime, String reasonKey);

    void createDailySystemBlocks(String customerId, String reasonKey, SecurityVerifyScene... scene);

    /**
     * 更新/创建黑名单记录
     * 如果已存在时间更长的记录，则返回更新失败提示
     *
     * @param createBlockRequest updateBlockRequest
     * @return 更新结果
     */
    CreateBlockResponse createOrUpdate(CreateBlockRequest createBlockRequest);

    /**
     * 解除用户封禁
     *
     * @param customerId customerId
     * @param business   场景
     */
    void remove(String customerId, String business);

    /**
     * 获取用户封禁业务
     *
     * @param customerId customerId
     * @return list
     */
    List<CustomerBlockBusinessDO> listByCustomerId(String customerId);

    /**
     * 通过主键获取一条封禁记录
     *
     * @param customerId customerId
     * @param business   场景
     * @return one
     */
    CustomerBlockBusinessDO getById(String customerId, String business);

    /**
     * 场景校验
     *
     * @param securityVerifyRequest securityVerifyRequest
     * @return SecurityVerifyResponse 允许-true，不允许-false
     */
    SecurityVerifyResponse securityVerify(SecurityVerifyRequest securityVerifyRequest);

    /**
     * 风控决策结果检查
     *
     * @param securityVerifyRequest
     * @return
     */
    SecurityVerifyResponse securityDecision(SecurityVerifyRequest securityVerifyRequest);

    /**
     * 管理端分页查询
     *
     * @param customerIds customerIds
     * @param business   业务场景
     * @param token      nextToken
     * @param limit      查询个数
     * @return page list
     */
    TokenPage<CustomerBlockBusinessDO> pageList(List<String> customerIds, String business, String token, int limit);



}
