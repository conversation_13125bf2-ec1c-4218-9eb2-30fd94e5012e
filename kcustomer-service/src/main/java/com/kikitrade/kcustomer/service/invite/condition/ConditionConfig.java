package com.kikitrade.kcustomer.service.invite.condition;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2023/4/1 13:26
 */
@Component
public class ConditionConfig {

    @Autowired
    private List<Condition> conditionList;

    private Map<String, Condition> conditions;

    public List<Condition> conditions(String inviteConditions) {
        List<Condition> list = new ArrayList<>();

        if (conditions == null) {
            conditions = new HashMap<>();
            conditionList.forEach(c -> conditions.put(c.name(), c));
        }

        if (StringUtils.isBlank(inviteConditions)) {
            list.add(conditions.get("CommonCondition"));
            return list;
        }

        for (String condition : StringUtils.split(inviteConditions, ",")) {
            list.add(conditions.get(condition));
        }
        list = list.stream().sorted(Comparator.comparing(Condition::sort)).collect(Collectors.toList());
        return list;
    }

}
