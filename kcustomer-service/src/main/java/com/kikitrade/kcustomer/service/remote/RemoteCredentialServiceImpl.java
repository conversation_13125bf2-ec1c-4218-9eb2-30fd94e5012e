package com.kikitrade.kcustomer.service.remote;

import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.credential.GetStsCredentialRequest;
import com.kikitrade.kcustomer.api.model.credential.OssObjectDTO;
import com.kikitrade.kcustomer.api.model.credential.SignOssUrlRequest;
import com.kikitrade.kcustomer.api.model.credential.StsCredentialDTO;
import com.kikitrade.kcustomer.api.model.file.FileDTO;
import com.kikitrade.kcustomer.api.model.file.SignFileUrlRequest;
import com.kikitrade.kcustomer.api.service.RemoteCredentialService;
import com.kikitrade.kcustomer.service.credentials.ICredentialsService;
import com.kikitrade.kcustomer.service.credentials.IFileService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * @author: penuel
 * @date: 2022/7/18 11:50
 * @desc: TODO
 */
@DubboService
public class RemoteCredentialServiceImpl implements RemoteCredentialService {

    @Autowired
    private ICredentialsService credentialsService;
    @Autowired
    private IFileService fileService;


    @Override
    public StsCredentialDTO stsCredential(GetStsCredentialRequest getStsCredentialRequest) throws CustomerException {
        return credentialsService.stsCredential(getStsCredentialRequest);
    }

    @Deprecated
    @Override
    public List<OssObjectDTO> signOssUrl(SignOssUrlRequest signOssUrlRequest) {
        List<FileDTO> objects = fileService.signFileUrl(SignFileUrlRequest.builder()
                .expirationInSecond(signOssUrlRequest.getExpirationInSecond())
                .owner(signOssUrlRequest.getOwner()).urls(signOssUrlRequest.getUrls())
                .saasId(signOssUrlRequest.getSaasId()).build());
        return BeanUtil.copyProperties(objects, OssObjectDTO::new);
    }

}
