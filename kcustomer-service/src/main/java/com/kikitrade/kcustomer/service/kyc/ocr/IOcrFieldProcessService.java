package com.kikitrade.kcustomer.service.kyc.ocr;

import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;

/**
 * @author: wang
 * @date: 2022/4/12
 * @desc:
 */
public interface IOcrFieldProcessService {
    CustomerIdentityConstants.DocType getDocType();

    String getBirthDate(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getExpireDate(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getIssueDate(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getFirstName(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getMiddleName(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getLastName(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getDocumentNumber(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getGender(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getAddress(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);

    String getExtraInfo(String ocrDataStr, OcrDataIndexDTO ocrDataIndexDTO, CustomerIdentityConstants.DocType docType);
}
