package com.kikitrade.kcustomer.service.kyc.v1.model;

import com.google.common.collect.Lists;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 人脸识别结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FaceResult implements Serializable {

    private CustomerIdentityConstants.LivenessStatus livenessStatus = CustomerIdentityConstants.LivenessStatus.NOT_MATCHED;
    private String facePicUrl;
    private List<String> failCause = Lists.newArrayList();
}
