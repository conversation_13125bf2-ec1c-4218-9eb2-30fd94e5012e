package com.kikitrade.kcustomer.service.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/4/11 18:58
 * @description:
 */
@Configuration
@ConfigurationProperties(prefix = "three")
@Data
public class ThreePlatformProperties {

    @NestedConfigurationProperty
    private DeekProperties deek;

}
