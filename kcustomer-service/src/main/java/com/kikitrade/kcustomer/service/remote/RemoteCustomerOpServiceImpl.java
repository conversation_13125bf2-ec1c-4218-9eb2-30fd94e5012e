package com.kikitrade.kcustomer.service.remote;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.kcustomer.api.constants.*;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.*;
import com.kikitrade.kcustomer.api.service.RemoteCustomerOpService;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import com.kikitrade.kcustomer.common.util.DateUtil;
import com.kikitrade.kcustomer.common.util.RegexChecker;
import com.kikitrade.kcustomer.dal.model.CustomerDO;
import com.kikitrade.kcustomer.service.block.CustomerBlockBusinessService;
import com.kikitrade.kcustomer.service.cache.CustomerCacheKey;
import com.kikitrade.kcustomer.service.configuration.KCustomerProperties;
import com.kikitrade.kcustomer.service.customer.CustomerService;
import com.kikitrade.kcustomer.service.customer.CustomerVerifyService;
import com.kikitrade.kcustomer.service.notification.scene.NotificationSceneService;
import com.kikitrade.kcustomer.service.notification.scene.NotifySceneConstants;
import com.kikitrade.kcustomer.service.risk.CustomerRiskService;
import com.kikitrade.kcustomer.service.util.CommonUtil;
import com.kikitrade.kcustomer.service.util.GoogleAuthenticatorUtil;
import com.kikitrade.knotify.api.model.NotifyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.kikitrade.kcustomer.common.constants.CustomerConstants.APPLE_HIDDEN_EMAIL_SUFFIX;
import static com.kikitrade.kcustomer.common.constants.CustomerConstants.CUSTOMER_ID;
import static com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants.KycLevel.L1;
import static com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants.KycLevel.L2;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class RemoteCustomerOpServiceImpl implements RemoteCustomerOpService {

    private final CustomerService customerService;
    private final CustomerVerifyService customerVerifyService;
    private final CustomerRiskService customerRiskService;
    private final RedisTemplate<String, String> redisTemplate;
    private final OnsProducer onsProducer;
    private final KCustomerProperties kCustomerProperties;
    private final NotificationSceneService notificationSceneService;
    private final CustomerBlockBusinessService customerBlockBusinessService;

    public RemoteCustomerOpServiceImpl(CustomerService customerService,
                                       CustomerVerifyService customerVerifyService,
                                       CustomerRiskService customerRiskService,
                                       RedisTemplate<String, String> redisTemplate,
                                       OnsProducer onsProducer,
                                       KCustomerProperties kCustomerProperties, NotificationSceneService notificationSceneService, CustomerBlockBusinessService customerBlockBusinessService) {
        this.customerService = customerService;
        this.customerVerifyService = customerVerifyService;
        this.customerRiskService = customerRiskService;
        this.redisTemplate = redisTemplate;
        this.onsProducer = onsProducer;
        this.kCustomerProperties = kCustomerProperties;
        this.notificationSceneService = notificationSceneService;
        this.customerBlockBusinessService = customerBlockBusinessService;
    }


    @Override
    public CustomerCommonResponse setPassword(SetPwdRequest request) throws CustomerException {
        log.info("setPassword request:{}", JSON.toJSONString(request));
        PwdOpType pwdOpType = request.getPwdOpType();
        String saasId = request.getSaasId();
        String customerId = request.getCustomerId();
        String tokenId = request.getTokenId();
        String password = request.getPassword();
        if (pwdOpType == PwdOpType.RESET) {
            if (StringUtils.isBlank(request.getOldPassword())) {
                throw new CustomerException(CustomerMessageEnum.SYSTEM_PARAMETER_REQUIRED);
            }
            boolean verify = customerVerifyService.tokenVerify(saasId, tokenId, customerId, BusinessType.CUSTOMER_RESET_PWD);
            if (!verify) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_VERIFY_CODE_EXPIRE);
            }
            CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
            return executeSetPwd(saasId, customerDO, request.getOldPassword(), password, request.getIp());
        } else if (pwdOpType == PwdOpType.ADD) {
            boolean verify = customerVerifyService.tokenVerify(saasId, tokenId, customerId, BusinessType.CUSTOMER_RESET_PWD);
            if (!verify) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_VERIFY_CODE_EXPIRE);
            }
            boolean address = customerService.addPassword(saasId, customerId, password);
            if (!address) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_ADD_PASSWORD_ERROR);
            }
            return CustomerCommonResponse.success();
        } else if (pwdOpType == PwdOpType.FORGOT) {
            String key = CommonUtil.verifyTokenPrefix(BusinessType.CUSTOMER_FORGOTPWD) + tokenId;
            String accountName = redisTemplate.opsForValue().get(key);
            CustomerDO byAccountName = customerService.getByAccountName(request.getSaasId(), accountName);
            if (byAccountName == null) {
                throw new CustomerException(CustomerMessageEnum.CUSTOMER_RESET_PASSWORD_ERROR);
            }
            return executeSetPwd(saasId, byAccountName, null, password, request.getIp());
        }
        throw new CustomerException(CustomerMessageEnum.SYSTEM_ERROR);
    }


    @Override
    public CustomerCommonResponse bindEmail(BindEmailRequest request) throws CustomerException {
        log.info("bindEmailRequest request:{}", JSON.toJSONString(request));
        String saasId = request.getSaasId();
        String email = request.getEmail();
        String customerId = request.getCustomerId();
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (!RegexChecker.isMail(email)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EMAIL_FORMAT_INCORRECT);
        }
        //如果邮箱不为空 且不是apple隐藏邮箱
        if (StringUtils.isNotBlank(customerDO.getEmail()) && !customerDO.getEmail().endsWith(APPLE_HIDDEN_EMAIL_SUFFIX)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_DUPLICATE);
        }
        CustomerDO exist = customerService.getByEmail(saasId, email);
        //是否已经被其他人绑定
        if (exist != null) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_DUPLICATE);
        }
        String code = redisTemplate.opsForValue().get(CustomerCacheKey.VERIFY_CODE_BY_EMAIL + email);
        if (!request.getVerifyCode().equals(code)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_CODE_INCORRECT);
        }
        boolean bind = customerVerifyService.bindEmail(saasId, customerDO.getId(), email, false);
        if (bind) {
            //发送邮箱绑定事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent
                            .builder()
                            .customerId(customerDO.getId()).saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.BindEmail)
                            .build().toJSONString());
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(bind);
        return response;
    }

    @Override
    public CustomerCommonResponse replaceBindEmail(ReplaceBindEmailRequest request) throws CustomerException {
        log.info("replaceBindEmailRequest request:{}", JSON.toJSONString(request));
        String saasId = request.getSaasId();
        String customerId = request.getCustomerId();
        String email = request.getEmail();
        String newEmailVerifyCode = request.getNewEmailVerifyCode();
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (!RegexChecker.isMail(email)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EMAIL_FORMAT_INCORRECT);
        }
        if (email.equals(customerDO.getEmail())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_DUPLICATE);
        }
        CustomerDO exist = customerService.getByEmail(saasId, email);
        if (exist != null) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_DUPLICATE);
        }
        String code = redisTemplate.opsForValue().get(CustomerCacheKey.VERIFY_CODE_BY_EMAIL + email);
        if (StringUtils.isBlank(newEmailVerifyCode) || !newEmailVerifyCode.equalsIgnoreCase(code)) {
            log.info("replaceBindEmail error customerId:{},email:{},newEmailVerifyCode:{},code:{}", customerId, email, newEmailVerifyCode, code);
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_CODE_INCORRECT);
        }
        boolean verify = customerVerifyService.tokenVerify(saasId, request.getOldEmailToken(), customerId, BusinessType.EMAIL_REPLACE_BINDING_VERIFY);
        if (!verify) {
            log.info("replaceBindEmail error customerId:{},email:{}", customerId, email);
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_CODE_INCORRECT);
        }
        String oldEmail = customerDO.getEmail();
        boolean bind = customerVerifyService.bindEmail(saasId, customerDO.getId(), email, true);
        if (bind) {
            customerBlockBusinessService.createDailySystemBlocks(customerDO.getId(), CustomerMessageEnum.CUSTOMER_NOT_ALLOWED_WITHDRAW_AFTER_MODIFY_BIND.getKey()
                    , SecurityVerifyScene.WITHDRAW, SecurityVerifyScene.RED_POCKET);
            //发送邮箱换绑事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent.builder()
                            .customerId(customerDO.getId()).saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.ReplaceBindEmail)
                            .build().toJSONString());
            //场景化通知
            Map<String, Object> map = Maps.newHashMap();
            map.put(CUSTOMER_ID, customerId);
            map.put("ip", request.getIp());
            //给旧的邮箱发送通知
            map.put("oldEmail", oldEmail);
            notificationSceneService.noticeDynamic(NotifySceneConstants.NotifySceneEnum.REPLACE_BIND_EMAIL_SUCCESS, map);
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(bind);
        return response;
    }

    @Override
    public CustomerCommonResponse unBindEmail(UnBindRequest request) throws CustomerException {
        log.info("unBindEmailRequest request:{}", JSON.toJSONString(request));
        String saasId = request.getSaasId();
        String customerId = request.getCustomerId();
        String token = request.getTokenId();
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (StringUtils.isBlank(customerDO.getEmail())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_VERIFY_EMAIL_UNBIND);
        }
        //手机号为空的情况下不允许解绑邮箱
        if (StringUtils.isBlank(customerDO.getPhone())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_UNBIND_NOT_ALLOWED);
        }
        boolean verify = customerVerifyService.tokenVerify(saasId, token, customerDO.getId(), BusinessType.EMAIL_REMOVE_VERIFY);
        if (!verify) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_VERIFY_CODE_EXPIRE);
        }
        // kyc认证通过只允许换绑不允许解绑
        CustomerIdentityConstants.KycLevel kycLevel = CustomerIdentityConstants.KycLevel.fromName(customerDO.getKycLevel());
        if (Arrays.asList(L1, L2).contains(kycLevel)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_MAIL_UNBIND_NOT_ALLOWED);
        }
        boolean remove = customerVerifyService.removeBindEmail(saasId, customerDO.getId());
        if (remove) {
            customerBlockBusinessService.createDailySystemBlocks(customerDO.getId(), CustomerMessageEnum.CUSTOMER_NOT_ALLOWED_WITHDRAW_AFTER_MODIFY_BIND.getKey()
                    , SecurityVerifyScene.WITHDRAW, SecurityVerifyScene.RED_POCKET);
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent.builder()
                            .customerId(customerDO.getId()).saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.RemoveBindEmail)
                            .build().toJSONString());
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(remove);
        return response;
    }

    @Override
    public CustomerCommonResponse bindPhone(BindPhoneRequest request) throws CustomerException {
        log.info("bindPhoneRequest request:{}", JSON.toJSONString(request));
        String phone = request.getPhone();
        String customerId = request.getCustomerId();
        String verifyCode = request.getVerifyCode();
        String saasId = request.getSaasId();
        SmsType smsType = request.getSmsType();
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (StringUtils.isNotBlank(customerDO.getPhone())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_DUPLICATE);
        }
        CustomerDO exist = customerService.getByPhone(saasId, phone);
        if (exist != null) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_DUPLICATE);
        }
        String code = redisTemplate.opsForValue().get(CustomerCacheKey.VERIFY_CODE_BY_PHONE + phone);
        if (smsType == SmsType.VOICE) {
            customerVerifyService.verifyVoiceCode(saasId, phone, verifyCode, code);
        } else if (!verifyCode.equals(code)) {
            log.info("bindPhone is code:{}, verifyCode:{} ,incorrect", code, verifyCode);
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_CODE_INCORRECT);
        }
        boolean bind = customerVerifyService.bindPhone(saasId, customerDO.getId(), phone, false);
        if (bind) {
            //发送手机绑定事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent.builder()
                            .customerId(customerDO.getId())
                            .saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis())
                            .eventType(CustomerEvent.EventType.BindPhone)
                            .build().toJSONString());
        }
        // 场景化通知 VIBRA使用
        if (bind) {
            notificationSceneService.noticeDynamic(NotifySceneConstants.NotifySceneEnum.BIND_PHONE, customerId);
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(bind);
        return response;
    }

    @Override
    public CustomerCommonResponse replaceBindPhone(ReplaceBindPhoneRequest request) throws CustomerException {
        log.info("replaceBindPhoneRequest request:{}", JSON.toJSONString(request));
        String newPhone = request.getNewPhone();
        String customerId = request.getCustomerId();
        String oldPhoneToken = request.getOldPhoneToken();
        String newPhoneVerifyCode = request.getNewPhoneVerifyCode();
        String saasId = request.getSaasId();
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (newPhone.equals(customerDO.getPhone())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_DUPLICATE);
        }
        CustomerDO exist = customerService.getByPhone(saasId, newPhone);
        if (exist != null) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_DUPLICATE);
        }

        String code = redisTemplate.opsForValue().get(CustomerCacheKey.VERIFY_CODE_BY_PHONE + newPhone);
        if (StringUtils.isBlank(newPhoneVerifyCode) || !newPhoneVerifyCode.equalsIgnoreCase(code)) {
            log.info("replaceBindPhone error code:{},newPhoneVerifyCode:{}", code, newPhoneVerifyCode);
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_CODE_INCORRECT);
        }
        //如果是VIBRA  token里面的值其实是未换绑前的手机号 从else分支取
        boolean verify = customerVerifyService.tokenVerify(saasId, oldPhoneToken, customerId, BusinessType.PHONE_REPLACE_BINDING_VERIFY);
        if (!verify) {
            log.info("replaceBindPhone error customerId:{},newPhone:{},oldPhoneToken:{},newPhoneVerifyCode:{}", customerId, newPhone, oldPhoneToken, newPhoneVerifyCode);
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_CODE_INCORRECT);
        }
        String oldPhone = customerDO.getPhone();
        boolean bind = customerVerifyService.bindPhone(saasId, customerDO.getId(), newPhone, true);
        if (bind) {
            customerBlockBusinessService.createDailySystemBlocks(customerDO.getId(), CustomerMessageEnum.CUSTOMER_NOT_ALLOWED_WITHDRAW_AFTER_MODIFY_BIND.getKey()
                    , SecurityVerifyScene.WITHDRAW, SecurityVerifyScene.RED_POCKET);
            //发送手机换绑事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent.builder()
                            .customerId(customerDO.getId()).saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.ReplaceBindPhone)
                            .build().toJSONString()
            );
            //场景化通知
            Map<String, Object> map = Maps.newHashMap();
            map.put(CUSTOMER_ID, customerId);
            map.put("ip", request.getIp());
            //给旧的手机发送通知
            map.put("oldPhone", oldPhone);
            notificationSceneService.noticeDynamic(NotifySceneConstants.NotifySceneEnum.REPLACE_BIND_PHONE_SUCCESS, map);
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(bind);
        return response;
    }

    @Override
    public CustomerCommonResponse unBindPhone(UnBindRequest request) throws CustomerException {
        log.info("unBindPhoneRequest request:{}", JSON.toJSONString(request));
        String saasId = request.getSaasId();
        String customerId = request.getCustomerId();
        String tokenId = request.getTokenId();
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (StringUtils.isBlank(customerDO.getPhone())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_VERIFY_PHONE_UNBIND);
        }
        //邮箱为空的时候不允许解绑手机
        if (StringUtils.isBlank(customerDO.getEmail())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_UNBIND_NOT_ALLOWED);
        }
        boolean verify = customerVerifyService.tokenVerify(saasId, tokenId, customerId, BusinessType.PHONE_REMOVE_VERIFY);
        if (!verify) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_CODE_INCORRECT);
        }
        // kyc认证通过只允许换绑不允许解绑
        CustomerIdentityConstants.KycLevel kycLevel = CustomerIdentityConstants.KycLevel.fromName(customerDO.getKycLevel());
        if (Arrays.asList(L1, L2).contains(kycLevel)) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_PHONE_UNBIND_NOT_ALLOWED);
        }
        boolean remove = customerVerifyService.removeBindPhone(saasId, customerDO.getId());
        if (remove) {
            customerBlockBusinessService.createDailySystemBlocks(customerDO.getId(), CustomerMessageEnum.CUSTOMER_NOT_ALLOWED_WITHDRAW_AFTER_MODIFY_BIND.getKey()
                    , SecurityVerifyScene.WITHDRAW, SecurityVerifyScene.RED_POCKET);
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent.builder()
                            .customerId(customerDO.getId()).saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis()).eventType(CustomerEvent.EventType.RemoveBindPhone)
                            .build().toJSONString()
            );
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(remove);
        return response;
    }

    @Override
    public CustomerCommonResponse generateGoogleKey(String saasId, String customerId) throws CustomerException {
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (StringUtils.isNotBlank(customerDO.getGoogleKey())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_GOOGLE_AUTH_FAIL);
        }
        String secret = GoogleAuthenticatorUtil.generateSecretKey();
        CustomerCommonResponse response = CustomerCommonResponse.builder().result(secret).build();
        response.setSuccess(true);
        return response;
    }

    @Override
    public CustomerCommonResponse bindGoogle(BindGoogleRequest request) throws CustomerException {
        log.info("bindGoogleRequest request:{}", JSON.toJSONString(request));
        String saasId = request.getSaasId();
        String customerId = request.getCustomerId();
        String googleKey = request.getGoogleKey();
        long googleCode = request.getGoogleCode();
        CustomerDO customerDO = getAndCheckCustomer(saasId, customerId);
        if (StringUtils.isNotBlank(customerDO.getGoogleKey())) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_GOOGLE_AUTH_FAIL);
        }
        long t = System.currentTimeMillis();
        boolean result = GoogleAuthenticatorUtil.check_code(googleKey, googleCode, t);
        if (result) {
            result = customerVerifyService.bindGoogle(saasId, customerDO.getId(), googleKey);
        } else {
            log.info("bindGoogle googleKey:{},googleCode:{},result:{},time:{}", googleKey, googleCode, result, t);

        }
        if (result) {
            //google绑定事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent
                            .builder()
                            .customerId(customerDO.getId())
                            .saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis())
                            .eventType(CustomerEvent.EventType.BindGoogle)
                            .build().toJSONString());
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(result);
        response.setMessageEnum(result ? CustomerMessageEnum.CUSTOMER_GOOGLE_BIND_SUCCESS : CustomerMessageEnum.CUSTOMER_GOOGLE_BIND_FAIL);
        return response;
    }

    @Override
    public CustomerCommonResponse unBindGoogle(UnBindRequest request) throws CustomerException {
        log.info("unBindGoogleRequest request:{}", JSON.toJSONString(request));
        String saasId = request.getSaasId();
        String customerId = request.getCustomerId();
        String tokenId = request.getTokenId();
        CustomerDO customerDO = customerService.getById(saasId, customerId);
        if (null == customerDO) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
        }
        boolean verify = customerVerifyService.tokenVerify(saasId, tokenId, customerId, BusinessType.UNBIND_GOOGLE);
        if (!verify) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_VERIFY_CODE_EXPIRE);
        }
        boolean remove = customerVerifyService.removeBindGoogle(saasId, customerDO.getId());
        if (remove) {
            customerBlockBusinessService.createDailySystemBlocks(customerDO.getId(), CustomerMessageEnum.CUSTOMER_NOT_ALLOWED_WITHDRAW_AFTER_MODIFY_BIND.getKey()
                    , SecurityVerifyScene.WITHDRAW, SecurityVerifyScene.RED_POCKET);
            //google解绑事件
            onsProducer.asyncSend(kCustomerProperties.getOnsTopicCustomerEvent(),
                    CustomerEvent
                            .builder()
                            .customerId(customerDO.getId())
                            .saasId(customerDO.getSaasId())
                            .eventTime(System.currentTimeMillis())
                            .eventType(CustomerEvent.EventType.RemoveBindGoogle)
                            .build().toJSONString());
        }
        CustomerCommonResponse response = CustomerCommonResponse.builder().success(remove).build();
        response.setMessageEnum(remove ? CustomerMessageEnum.CUSTOMER_GOOGLE_UNBIND_SUCCESS : CustomerMessageEnum.CUSTOMER_GOOGLE_UNBIND_FAIL);
        response.setSuccess(remove);
        return response;
    }

    @Override
    public CustomerCommonResponse updateMiscInfo(UpdateInfoRequest request) throws CustomerException {
        log.info("updateMiscInfoRequest request:{}", JSON.toJSONString(request));
        CustomerDO customerDO = customerService.getById(request.getSaasId(), request.getCustomerId());
        if (null == customerDO) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
        }
        boolean update = customerService.updateMiscInfo(request, customerDO);
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(update);
        return response;
    }

    private CustomerCommonResponse executeSetPwd(String saasId, CustomerDO customerDO, String oldPassword, String newPassword, String ip) throws CustomerException {

        customerRiskService.checkIpRisk(customerDO, ip, CustomerRiskConstant.RiskType.CUST_PWD_RESET);
        //如果重置成功，需要前端调用logout登出
        boolean reset = customerService.resetPassword(saasId, customerDO.getId(), oldPassword, newPassword);
        if (!reset) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_RESET_PASSWORD_ERROR);
        }

        //异步添加出入金拦截黑名单
        customerBlockBusinessService.createDailySystemBlocks(customerDO.getId(), CustomerMessageEnum.CUSTOMER_NOT_ALLOWED_WITHDRAW_AFTER_PASSWORD_RESET.getKey()
                , SecurityVerifyScene.WITHDRAW, SecurityVerifyScene.RED_POCKET);
        //发送邮件 只有VIBRA使用
        Map<String, Object> map = new HashMap<>();
        map.put("email", customerDO.getEmail());
        map.put("modifyTime", DateUtil.dateToUTCString(new Date()));
        map.put("ip", ip);
        map.put(NotifyConstants.CONTENT, customerDO.getEmail());
        notificationSceneService.noticeDynamic(NotifySceneConstants.NotifySceneEnum.FORGOT_SUCCESS, map);
        CustomerCommonResponse response = CustomerCommonResponse.builder().build();
        response.setSuccess(true);
        return response;
    }

    private CustomerDO getAndCheckCustomer(String saasId, String customerId) throws CustomerException {
        CustomerDO customerDO = customerService.getById(saasId, customerId);
        if (null == customerDO) {
            throw new CustomerException(CustomerMessageEnum.CUSTOMER_EXIST_NONE);
        }
        return customerDO;
    }

}
