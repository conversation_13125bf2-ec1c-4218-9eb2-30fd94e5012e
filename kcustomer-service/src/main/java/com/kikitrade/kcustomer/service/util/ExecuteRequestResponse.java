package com.kikitrade.kcustomer.service.util;

import com.aliyuncs.AcsResponse;
import com.aliyuncs.transform.UnmarshallerContext;

public class ExecuteRequestResponse extends AcsResponse {

    private Integer code;

    private String data;

    private String message;

    private String requestId;

    public Integer getCode() {
        return this.code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getData() {
        return this.data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRequestId() {
        return this.requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public ExecuteRequestResponse getInstance(UnmarshallerContext context) {
        return	ExecuteRequestResponseUnmarshaller.unmarshall(this, context);
    }

    @Override
    public boolean checkShowJsonItemName() {
        return false;
    }
}
