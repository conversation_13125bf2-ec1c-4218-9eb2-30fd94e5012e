package com.kikitrade.kcustomer.service.kyc.ocr;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.constants.CustomerMessageEnum;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import com.kikitrade.kcustomer.dal.model.CustomerIdentityDO;
import com.kikitrade.kcustomer.service.kyc.ocr.impl.identify.ChinaOcrIdentifyServiceImpl;
import com.kikitrade.kcustomer.service.kyc.ocr.impl.identify.LocationOcrIndetifyServiceImpl;
import com.kikitrade.kcustomer.service.kyc.ocr.impl.identify.PassportOcrIdentifyServiceImpl;
import com.kikitrade.kcustomer.service.kyc.ocr.impl.identify.TemplateOcrIndetifyServiceImpl;
import com.kikitrade.kcustomer.service.kyc.v1.KycException;
import com.kikitrade.kcustomer.service.kyc.v1.model.DocumentResult;
import com.kikitrade.kcustomer.service.kyc.v1.yundun.YundunKycApiServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wang
 * @date: 2022/4/12
 * @desc:
 */
@Service
public class OcrServiceAdapter {

    private List<IOcrIdentifyService> ocrIdentifyServiceList;

    /**
     * 证件识别 按照不同方式
     *
     * @param docType
     * @return
     * @throws CustomerException
     */
    private IOcrIdentifyService ocrIdentify(CustomerIdentityConstants.DocType docType) {
        return ocrIdentifyServiceList.stream().filter(ch -> ch.getIdentifyType() == docType.getIdentifyType()).findFirst().orElse(null);
    }

    public JSONObject getFrontExtrasInfo(CustomerIdentityConstants.DocType docType) {
        IOcrIdentifyService iOcrIdentifyService = ocrIdentify(docType);
        return iOcrIdentifyService.getFrontExtrasInfo(docType);
    }

    public JSONObject getBackExtrasInfo(CustomerIdentityConstants.DocType docType) {
        IOcrIdentifyService iOcrIdentifyService = ocrIdentify(docType);
        return iOcrIdentifyService.getBackExtrasInfo(docType);
    }

    public JSONObject getOcrFileResult(Object sceneResult, CustomerIdentityConstants.DocType docType) {
        IOcrIdentifyService iOcrIdentifyService = ocrIdentify(docType);
        return iOcrIdentifyService.getOcrFileResult(sceneResult, docType);
    }

    public DocumentResult parseOcrResult(YundunKycApiServiceImpl.OcrFileResultBean ocrResult, CustomerIdentityConstants.DocType docType) {
        IOcrIdentifyService iOcrIdentifyService = ocrIdentify(docType);
        return iOcrIdentifyService.parseOcrResult(ocrResult, docType);
    }

    @Autowired
    public void setOcrIdentifyServiceList(List<IOcrIdentifyService> ocrIdentifyServiceList) {
        this.ocrIdentifyServiceList = ocrIdentifyServiceList;
    }

    private List<IOcrIdentifyService> testOcridentifyService() {
        List<IOcrIdentifyService> iOcrIdentifyServices = new ArrayList<>();
        iOcrIdentifyServices.add(new ChinaOcrIdentifyServiceImpl());
        iOcrIdentifyServices.add(new LocationOcrIndetifyServiceImpl());
        iOcrIdentifyServices.add(new PassportOcrIdentifyServiceImpl());
        iOcrIdentifyServices.add(new TemplateOcrIndetifyServiceImpl());
        return iOcrIdentifyServices;
    }
}
