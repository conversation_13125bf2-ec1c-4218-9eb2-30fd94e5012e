package com.kikitrade.kcustomer.service.invite.condition.impl;

import com.kikitrade.kcustomer.api.constants.CustomerMessageEnum;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.dal.model.CustomerDO;
import com.kikitrade.kcustomer.dal.model.CustomerInviteDO;
import com.kikitrade.kcustomer.service.customer.CustomerInviteService;
import com.kikitrade.kcustomer.service.customer.CustomerService;
import com.kikitrade.kcustomer.service.invite.condition.Condition;
import com.kikitrade.kcustomer.service.invite.model.InviteConditionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: lizhi<PERSON>
 * @date: 2023/6/28 11:42
 */
@Slf4j
@Component
public class CommonCondition implements Condition {

    @Autowired
    private CustomerInviteService inviteService;
    @Autowired
    private CustomerService customerService;

    @Override
    public int sort() {
        return 1;
    }

    @Override
    public String name() {
        return this.getClass().getSimpleName();
    }

    @Override
    public boolean check(InviteConditionDTO conditionDTO) throws CustomerException {

        // 邀请码是否存在
        CustomerInviteDO inviterDO = inviteService.inviterByInviteCode(conditionDTO.getSaasId(), conditionDTO.getInviteCode());
        if (inviterDO == null) {
            throw new CustomerException(CustomerMessageEnum.INVITE_CODE_NOT_EXIST);
        }
        conditionDTO.setInviterId(inviterDO.getInviteeId());

        // 判断是不是自己邀请的自己
        if (StringUtils.equals(conditionDTO.getInviteeId(), inviterDO.getInviteeId())) {
            throw new CustomerException(CustomerMessageEnum.INVITE_USER_IS_YOURSELF);
        }

        // 如果被邀请人存在邀请关系，且邀请人不为空，不可建立邀请关系
        CustomerInviteDO inviteeDO = inviteService.getByCustomerId(conditionDTO.getSaasId(), conditionDTO.getInviteeId());
        if (inviteeDO != null && !StringUtils.equalsIgnoreCase("null", inviteeDO.getInviterId())) {
            throw new CustomerException(CustomerMessageEnum.INVITE_BIND_RELATION_EXIST);
        }
        conditionDTO.setInviteeDO(inviteeDO);

        // 判断邀请人是否存在
        CustomerDO customerInviterDO = customerService.getById(conditionDTO.getSaasId(), inviterDO.getInviteeId());
        if (null == customerInviterDO) {
            throw new CustomerException(CustomerMessageEnum.INVITE_BIND_INVITER_NOT_EXIST);
        }
        conditionDTO.setCustomerInviterDO(customerInviterDO);

        // 判断被邀请人是否存在
        CustomerDO customerInviteeDO = customerService.getById(conditionDTO.getSaasId(), conditionDTO.getInviteeId());
        if (null == customerInviteeDO) {
            throw new CustomerException(CustomerMessageEnum.INVITE_BIND_INVITEE_NOT_EXIST);
        }
        conditionDTO.setCustomerInviteeDO(customerInviteeDO);

        return true;
    }
}
