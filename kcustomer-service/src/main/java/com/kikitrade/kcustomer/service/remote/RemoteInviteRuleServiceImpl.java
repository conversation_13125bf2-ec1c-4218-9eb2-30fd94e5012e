package com.kikitrade.kcustomer.service.remote;

import com.kikitrade.kcustomer.api.constants.InviteCodeRuleConstant;
import com.kikitrade.kcustomer.api.exception.CustomerException;
import com.kikitrade.kcustomer.api.model.BatchInviteCodeDTO;
import com.kikitrade.kcustomer.api.model.InviteCodeRuleDTO;
import com.kikitrade.kcustomer.api.service.RemoteInviteRuleService;
import com.kikitrade.kcustomer.service.cache.CustomerCacheKey;
import com.kikitrade.kcustomer.service.customer.InviteCodeRuleService;
import com.kikitrade.kcustomer.service.util.AssertUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Date;

/**
 * Description:
 *
 * @Author: simon.shan
 * DateTime: 2022-10-31 10:57
 */
@Slf4j
@DubboService
public class RemoteInviteRuleServiceImpl implements RemoteInviteRuleService {

    @Resource
    private InviteCodeRuleService inviteCodeRuleService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public InviteCodeRuleDTO create(InviteCodeRuleDTO inviteCodeRuleDTO) throws CustomerException {
        log.info("[RemoteInviteRuleService.create] inviteCodeRuleDTO:{}", inviteCodeRuleDTO);

        AssertUtils.isNotBlank(inviteCodeRuleDTO.getInviter(), "inviter can not be empty");
        AssertUtils.isNotNull(inviteCodeRuleDTO.getInviteType(), "invite type can not be empty");

        return inviteCodeRuleService.create(inviteCodeRuleDTO);
    }

    @Override
    public Boolean changeState(String code, InviteCodeRuleConstant.RuleStatus status) throws CustomerException {
        log.info("[RemoteInviteRuleService.changeState] code:{} status:{}", code, status);

        AssertUtils.isNotBlank(code, "inviteCode can not be empty");
        AssertUtils.isNotNull(status, "status can not be empty");

        return inviteCodeRuleService.changeState(code, status);
    }

    @Override
    @Cacheable(value = CustomerCacheKey.INVITE_CODE_INFO, key = "'code:' + #inviteCode", condition = "#inviteCode != null")
    public InviteCodeRuleDTO get(String inviteCode) throws CustomerException {
        log.info("[RemoteInviteRuleService.get] inviteCode:{}", inviteCode);

        AssertUtils.isNotBlank(inviteCode, "inviteCode can not be empty");

        return inviteCodeRuleService.getInviteCodeRule(inviteCode);
    }

    @Override
    @Cacheable(value = CustomerCacheKey.INVITE_CODE_INFO, key = "'inviter:' + #inviter", condition = "#inviter != null")
    public InviteCodeRuleDTO getByInviter(String inviter) throws CustomerException {
        log.info("[RemoteInviteRuleService.get] inviter:{}", inviter);

        AssertUtils.isNotBlank(inviter, "inviter can not be empty");

        return inviteCodeRuleService.getCodeByInviter(inviter);
    }

    @Override
    public InviteCodeRuleDTO reduceStock(String inviteCode) throws CustomerException {
        log.info("[RemoteInviteRuleService.reduceStock] inviteCode:{}", inviteCode);

        AssertUtils.isNotBlank(inviteCode, "inviteCode can not be empty");

        return inviteCodeRuleService.reduceStock(inviteCode);
    }

    @Override
    public Boolean isAvailable(String inviteCode) throws CustomerException {
        log.info("[RemoteInviteRuleService.isAvailable] inviteCode:{}", inviteCode);

        AssertUtils.isNotBlank(inviteCode, "inviteCode can not be empty");

        return inviteCodeRuleService.isAvailable(inviteCode);

    }

    @Override
    public Boolean batchCreate(BatchInviteCodeDTO batchInviteCodeDTO) throws CustomerException {
        log.info("[RemoteInviteRuleService.batchCreate] batchInviteCodeDTO:{}", batchInviteCodeDTO);

        AssertUtils.isNotBlank(batchInviteCodeDTO.getInviter(), "inviter can not be empty");
        AssertUtils.isNotNull(batchInviteCodeDTO.getMaxInviteNumber(), "maxInviteNumber can not be empty");
        AssertUtils.isNotNull(batchInviteCodeDTO.getBatchNumber(), "batchNumber can not be empty");
        AssertUtils.isNotNull(batchInviteCodeDTO.getType(), "inviteType can not be empty");

        return inviteCodeRuleService.batchCreate(batchInviteCodeDTO);
    }

    @Override
    public Boolean hasPermission(String inviter) throws CustomerException {
        log.info("[RemoteInviteRuleService.hasPermission] inviter:{}", inviter);

        AssertUtils.isNotBlank(inviter, "inviter can not be empty");

        return inviteCodeRuleService.hasPermission(inviter);
    }

    @Override
    public InviteCodeRuleDTO distributeCode(Date date, String scene) throws CustomerException {
        log.info("[RemoteInviteRuleService.distributeCode] date:{} scene:{}", date, scene);

        AssertUtils.isNotBlank(scene, "scene can not be empty");

        return inviteCodeRuleService.distributeCode(date, scene, 0, 1);
    }
}
