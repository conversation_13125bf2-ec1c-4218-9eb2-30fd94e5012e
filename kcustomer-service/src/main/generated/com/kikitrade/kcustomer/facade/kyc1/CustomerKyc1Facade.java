// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerKyc1Facade.proto

package com.kikitrade.kcustomer.facade.kyc1;

public final class CustomerKyc1Facade {
  private CustomerKyc1Facade() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_CustomerKyc_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_CustomerKyc_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030CustomerKyc1Facade.proto\022#com.kikitrad" +
      "e.kcustomer.facade.kyc1\032\037google/protobuf" +
      "/timestamp.proto\"\244\r\n\013CustomerKyc\022\020\n\010user" +
      "Name\030\001 \001(\t\022\n\n\002id\030\002 \001(\t\022E\n\tkycStatus\030\003 \001(" +
      "\01622.com.kikitrade.kcustomer.facade.kyc1." +
      "KycStatusEnum\022\r\n\005phone\030\004 \001(\t\022\r\n\005email\030\005 " +
      "\001(\t\022\016\n\006region\030\006 \001(\t\022?\n\006gender\030\007 \001(\0162/.co" +
      "m.kikitrade.kcustomer.facade.kyc1.Gender" +
      "Enum\022\031\n\021kyc1CertifiedType\030\t \001(\t\0220\n\014kyc1M" +
      "odified\030\n \001(\0132\032.google.protobuf.Timestam" +
      "p\022\033\n\023kyc1CertificateType\030\013 \001(\t\022\035\n\025kyc1Ce" +
      "rtificateNumber\030\014 \001(\t\022\025\n\rkyc1FirstName\030\r" +
      " \001(\t\022\026\n\016kyc1MiddleName\030\016 \001(\t\022\024\n\014kyc1Last" +
      "Name\030\017 \001(\t\022\023\n\013kyc1Country\030\020 \001(\t\022\024\n\014kyc1B" +
      "irthday\030\021 \001(\t\022\027\n\017kyc1OcrPicFront\030\022 \001(\t\022\026" +
      "\n\016kyc1OcrPicBack\030\023 \001(\t\022\026\n\016faceComparePic" +
      "\030\024 \001(\t\022\027\n\017idCardExtraInfo\030\025 \001(\t\022=\n\007kycTy" +
      "pe\030\026 \001(\0162,.com.kikitrade.kcustomer.facad" +
      "e.kyc1.KycType\022\024\n\014kyc1FullName\030\027 \001(\t\022\024\n\014" +
      "annualIncome\030\030 \001(\t\022\026\n\016sourceOfIncome\030\031 \001" +
      "(\t\022\035\n\025companyBusinessNature\030\032 \001(\t\022\027\n\017com" +
      "panyBrNumber\030\033 \001(\t\022\023\n\013companyType\030\034 \001(\t\022" +
      "!\n\031companyRepresentativeName\030\035 \001(\t\022\"\n\032co" +
      "mpanyRepresentativeTitle\030\036 \001(\t\022\023\n\013compan" +
      "yName\030\037 \001(\t\022\"\n\032companyRegistrationCountr" +
      "y\030  \001(\t\022\026\n\016contactAddress\030! \001(\t\022\024\n\014conta" +
      "ctPhone\030\" \001(\t\022\024\n\014contactEmail\030# \001(\t\022\024\n\014s" +
      "ourceOfFund\030$ \001(\t\022\024\n\014liquidAssets\030% \001(\t\022" +
      "\023\n\013totalAssets\030& \001(\t\022\024\n\014accountProof\030\' \003" +
      "(\t\022\021\n\tkycResult\030( \001(\t\022\026\n\016considerReason\030" +
      ") \003(\t\0222\n\016kyc1CreateTime\030* \001(\0132\032.google.p" +
      "rotobuf.Timestamp\022\026\n\016docHandheldPic\030+ \001(" +
      "\t\022\034\n\024personalAnnualIncome\030, \001(\t\022\026\n\016liqui" +
      "dNetWorth\030- \001(\t\022\030\n\020sourceOfNetWorth\030. \001(" +
      "\t\022&\n\036percentageOfNetWorthInvestment\030/ \001(" +
      "\t\022\030\n\020employmentStatus\0300 \001(\t\022\036\n\026expectedA" +
      "nnualTurnover\0301 \001(\t\022\025\n\rnetAssetValue\0302 \001" +
      "(\t\022\023\n\013sourceOfNav\0303 \001(\t\022!\n\031percentageOfN" +
      "avInvestment\0304 \001(\t\022\032\n\022mainBusinessNature" +
      "\0305 \001(\t\022\034\n\024investmentExperience\0306 \001(\t\022\035\n\025" +
      "expectedHoldingPeriod\0307 \001(\t\022\033\n\023investmen" +
      "tObjective\0308 \001(\t\022\025\n\rlossTolerance\0309 \001(\t\022" +
      "\035\n\025actionDownByPercent20\030: \001(\t\022D\n\triskLe" +
      "vel\030; \001(\01621.com.kikitrade.kcustomer.faca" +
      "de.kyc1.KycRiskLevel\022\021\n\triskScore\030< \001(\t\"" +
      "\373\001\n\027QueryCustomerKycRequest\022\n\n\002id\030\001 \001(\t\022" +
      "\016\n\006saasId\030\002 \001(\t\022\r\n\005email\030\003 \001(\t\022\r\n\005phone\030" +
      "\004 \001(\t\022E\n\tkycStatus\030\005 \001(\01622.com.kikitrade" +
      ".kcustomer.facade.kyc1.KycStatusEnum\022=\n\007" +
      "kycType\030\006 \001(\0162,.com.kikitrade.kcustomer." +
      "facade.kyc1.KycType\022\r\n\005limit\030\007 \001(\005\022\021\n\tne" +
      "xtToken\030\010 \001(\t\"\223\001\n\030QueryCustomerKycRespon" +
      "se\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022\021\n\t" +
      "nextToken\030\003 \001(\t\022B\n\010customer\030\004 \003(\01320.com." +
      "kikitrade.kcustomer.facade.kyc1.Customer" +
      "Kyc\";\n\035QueryCustomerKycDetailRequest\022\016\n\006" +
      "saasId\030\001 \001(\t\022\n\n\002id\030\002 \001(\t\"\206\001\n\036QueryCustom" +
      "erKycDetailResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007" +
      "message\030\002 \001(\t\022B\n\010customer\030\003 \001(\01320.com.ki" +
      "kitrade.kcustomer.facade.kyc1.CustomerKy" +
      "c\"\231\001\n\017AuditKycRequest\022\016\n\006saasId\030\001 \001(\t\022\n\n" +
      "\002id\030\002 \001(\t\022E\n\tkycStatus\030\003 \001(\01622.com.kikit" +
      "rade.kcustomer.facade.kyc1.KycStatusEnum" +
      "\022\021\n\tkycResult\030\004 \001(\t\022\020\n\010reviewer\030\005 \001(\t\"x\n" +
      "\020AuditKycResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007me" +
      "ssage\030\002 \001(\t\022B\n\010customer\030\003 \001(\01320.com.kiki" +
      "trade.kcustomer.facade.kyc1.CustomerKyc\"" +
      "\331\010\n\030UpdateCustomerKycRequest\022\016\n\006saasId\030\001" +
      " \001(\t\022\n\n\002id\030\002 \001(\t\022\016\n\006region\030\003 \001(\t\022\035\n\025kyc1" +
      "CertificateNumber\030\004 \001(\t\022\025\n\rkyc1FirstName" +
      "\030\005 \001(\t\022\026\n\016kyc1MiddleName\030\006 \001(\t\022\024\n\014kyc1La" +
      "stName\030\007 \001(\t\022\023\n\013kyc1Country\030\010 \001(\t\022?\n\006gen" +
      "der\030\t \001(\0162/.com.kikitrade.kcustomer.faca" +
      "de.kyc1.GenderEnum\022\024\n\014kyc1Birthday\030\n \001(\t" +
      "\022\024\n\014contactPhone\030\013 \001(\t\022\024\n\014contactEmail\030\014" +
      " \001(\t\022\026\n\016contactAddress\030\r \001(\t\022\024\n\014annualIn" +
      "come\030\016 \001(\t\022\026\n\016sourceOfIncome\030\017 \001(\t\022\035\n\025co" +
      "mpanyBusinessNature\030\020 \001(\t\022\027\n\017companyBrNu" +
      "mber\030\021 \001(\t\022\023\n\013companyType\030\022 \001(\t\022!\n\031compa" +
      "nyRepresentativeName\030\023 \001(\t\022\"\n\032companyRep" +
      "resentativeTitle\030\024 \001(\t\022\023\n\013companyName\030\025 " +
      "\001(\t\022\"\n\032companyRegistrationCountry\030\026 \001(\t\022" +
      "\024\n\014sourceOfFund\030\027 \001(\t\022\024\n\014liquidAssets\030\030 " +
      "\001(\t\022\023\n\013totalAssets\030\031 \001(\t\022\024\n\014accountProof" +
      "\030\032 \003(\t\022\034\n\024personalAnnualIncome\030\033 \001(\t\022\026\n\016" +
      "liquidNetWorth\030\034 \001(\t\022\030\n\020sourceOfNetWorth" +
      "\030\035 \001(\t\022&\n\036percentageOfNetWorthInvestment" +
      "\030\036 \001(\t\022\030\n\020employmentStatus\030\037 \001(\t\022\036\n\026expe" +
      "ctedAnnualTurnover\030  \001(\t\022\025\n\rnetAssetValu" +
      "e\030! \001(\t\022\023\n\013sourceOfNav\030\" \001(\t\022!\n\031percenta" +
      "geOfNavInvestment\030# \001(\t\022\032\n\022mainBusinessN" +
      "ature\030$ \001(\t\022\034\n\024investmentExperience\030% \001(" +
      "\t\022\035\n\025expectedHoldingPeriod\030& \001(\t\022\033\n\023inve" +
      "stmentObjective\030\' \001(\t\022\025\n\rlossTolerance\030(" +
      " \001(\t\022\035\n\025actionDownByPercent20\030) \001(\t\"=\n\031U" +
      "pdateCustomerKycResponse\022\017\n\007success\030\001 \001(" +
      "\010\022\017\n\007message\030\002 \001(\t*\223\001\n\rKycStatusEnum\022\016\n\n" +
      "UNVERIFIED\020\000\022\017\n\013L1_AUDITING\020\001\022\r\n\tL1_REJE" +
      "CT\020\002\022\016\n\nL1_SUCCESS\020\003\022\017\n\013L2_AUDITING\020\004\022\r\n" +
      "\tL2_REJECT\020\005\022\016\n\nL2_SUCCESS\020\006\022\022\n\016ALL_KYC_" +
      "STATUS\020c*3\n\nGenderEnum\022\017\n\013UNSPECIFIED\020\000\022" +
      "\010\n\004MALE\020\001\022\n\n\006FEMALE\020\002*<\n\007KycType\022\016\n\nINDI" +
      "VIDUAL\020\000\022\017\n\013INSTITUTION\020\001\022\020\n\014ALL_KYC_TYP" +
      "E\020c*<\n\014KycRiskLevel\022\014\n\010LOW_RISK\020\000\022\017\n\013MED" +
      "IUM_RISK\020\001\022\r\n\tHIGH_RISK\020\0022\332\004\n\021CustomerKy" +
      "cFacade\022\217\001\n\020queryCustomerKyc\022<.com.kikit" +
      "rade.kcustomer.facade.kyc1.QueryCustomer" +
      "KycRequest\032=.com.kikitrade.kcustomer.fac" +
      "ade.kyc1.QueryCustomerKycResponse\022\234\001\n\021cu" +
      "stomerKycDetail\022B.com.kikitrade.kcustome" +
      "r.facade.kyc1.QueryCustomerKycDetailRequ" +
      "est\032C.com.kikitrade.kcustomer.facade.kyc" +
      "1.QueryCustomerKycDetailResponse\022\177\n\020audi" +
      "tCustomerKyc\0224.com.kikitrade.kcustomer.f" +
      "acade.kyc1.AuditKycRequest\0325.com.kikitra" +
      "de.kcustomer.facade.kyc1.AuditKycRespons" +
      "e\022\222\001\n\021updateCustomerKyc\022=.com.kikitrade." +
      "kcustomer.facade.kyc1.UpdateCustomerKycR" +
      "equest\032>.com.kikitrade.kcustomer.facade." +
      "kyc1.UpdateCustomerKycResponseB\'\n#com.ki" +
      "kitrade.kcustomer.facade.kyc1P\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_CustomerKyc_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_CustomerKyc_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_CustomerKyc_descriptor,
        new java.lang.String[] { "UserName", "Id", "KycStatus", "Phone", "Email", "Region", "Gender", "Kyc1CertifiedType", "Kyc1Modified", "Kyc1CertificateType", "Kyc1CertificateNumber", "Kyc1FirstName", "Kyc1MiddleName", "Kyc1LastName", "Kyc1Country", "Kyc1Birthday", "Kyc1OcrPicFront", "Kyc1OcrPicBack", "FaceComparePic", "IdCardExtraInfo", "KycType", "Kyc1FullName", "AnnualIncome", "SourceOfIncome", "CompanyBusinessNature", "CompanyBrNumber", "CompanyType", "CompanyRepresentativeName", "CompanyRepresentativeTitle", "CompanyName", "CompanyRegistrationCountry", "ContactAddress", "ContactPhone", "ContactEmail", "SourceOfFund", "LiquidAssets", "TotalAssets", "AccountProof", "KycResult", "ConsiderReason", "Kyc1CreateTime", "DocHandheldPic", "PersonalAnnualIncome", "LiquidNetWorth", "SourceOfNetWorth", "PercentageOfNetWorthInvestment", "EmploymentStatus", "ExpectedAnnualTurnover", "NetAssetValue", "SourceOfNav", "PercentageOfNavInvestment", "MainBusinessNature", "InvestmentExperience", "ExpectedHoldingPeriod", "InvestmentObjective", "LossTolerance", "ActionDownByPercent20", "RiskLevel", "RiskScore", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycRequest_descriptor,
        new java.lang.String[] { "Id", "SaasId", "Email", "Phone", "KycStatus", "KycType", "Limit", "NextToken", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "NextToken", "Customer", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailRequest_descriptor,
        new java.lang.String[] { "SaasId", "Id", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_QueryCustomerKycDetailResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Customer", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycRequest_descriptor,
        new java.lang.String[] { "SaasId", "Id", "KycStatus", "KycResult", "Reviewer", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycResponse_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_AuditKycResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "Customer", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycRequest_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycRequest_descriptor,
        new java.lang.String[] { "SaasId", "Id", "Region", "Kyc1CertificateNumber", "Kyc1FirstName", "Kyc1MiddleName", "Kyc1LastName", "Kyc1Country", "Gender", "Kyc1Birthday", "ContactPhone", "ContactEmail", "ContactAddress", "AnnualIncome", "SourceOfIncome", "CompanyBusinessNature", "CompanyBrNumber", "CompanyType", "CompanyRepresentativeName", "CompanyRepresentativeTitle", "CompanyName", "CompanyRegistrationCountry", "SourceOfFund", "LiquidAssets", "TotalAssets", "AccountProof", "PersonalAnnualIncome", "LiquidNetWorth", "SourceOfNetWorth", "PercentageOfNetWorthInvestment", "EmploymentStatus", "ExpectedAnnualTurnover", "NetAssetValue", "SourceOfNav", "PercentageOfNavInvestment", "MainBusinessNature", "InvestmentExperience", "ExpectedHoldingPeriod", "InvestmentObjective", "LossTolerance", "ActionDownByPercent20", });
    internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycResponse_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_kyc1_UpdateCustomerKycResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
