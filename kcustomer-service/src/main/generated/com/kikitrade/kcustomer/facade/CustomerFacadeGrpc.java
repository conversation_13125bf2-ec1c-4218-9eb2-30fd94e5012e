package com.kikitrade.kcustomer.facade;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.52.1)",
    comments = "Source: CustomerFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class CustomerFacadeGrpc {

  private CustomerFacadeGrpc() {}

  public static final String SERVICE_NAME = "com.kikitrade.kcustomer.facade.CustomerFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UnbindGoogleRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getUnbindGoogleMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "unbindGoogle",
      requestType = com.kikitrade.kcustomer.facade.UnbindGoogleRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryCustomerResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UnbindGoogleRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getUnbindGoogleMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UnbindGoogleRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse> getUnbindGoogleMethod;
    if ((getUnbindGoogleMethod = CustomerFacadeGrpc.getUnbindGoogleMethod) == null) {
      synchronized (CustomerFacadeGrpc.class) {
        if ((getUnbindGoogleMethod = CustomerFacadeGrpc.getUnbindGoogleMethod) == null) {
          CustomerFacadeGrpc.getUnbindGoogleMethod = getUnbindGoogleMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.UnbindGoogleRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "unbindGoogle"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.UnbindGoogleRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCustomerResponse.getDefaultInstance()))
              .setSchemaDescriptor(new CustomerFacadeMethodDescriptorSupplier("unbindGoogle"))
              .build();
        }
      }
    }
    return getUnbindGoogleMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCustomerRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getQueryCustomerMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryCustomer",
      requestType = com.kikitrade.kcustomer.facade.QueryCustomerRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryCustomerResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCustomerRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getQueryCustomerMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCustomerRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse> getQueryCustomerMethod;
    if ((getQueryCustomerMethod = CustomerFacadeGrpc.getQueryCustomerMethod) == null) {
      synchronized (CustomerFacadeGrpc.class) {
        if ((getQueryCustomerMethod = CustomerFacadeGrpc.getQueryCustomerMethod) == null) {
          CustomerFacadeGrpc.getQueryCustomerMethod = getQueryCustomerMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.QueryCustomerRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryCustomer"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCustomerRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCustomerResponse.getDefaultInstance()))
              .setSchemaDescriptor(new CustomerFacadeMethodDescriptorSupplier("queryCustomer"))
              .build();
        }
      }
    }
    return getQueryCustomerMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.BlockCustomerRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getBlockCustomerMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "blockCustomer",
      requestType = com.kikitrade.kcustomer.facade.BlockCustomerRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryCustomerResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.BlockCustomerRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getBlockCustomerMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.BlockCustomerRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse> getBlockCustomerMethod;
    if ((getBlockCustomerMethod = CustomerFacadeGrpc.getBlockCustomerMethod) == null) {
      synchronized (CustomerFacadeGrpc.class) {
        if ((getBlockCustomerMethod = CustomerFacadeGrpc.getBlockCustomerMethod) == null) {
          CustomerFacadeGrpc.getBlockCustomerMethod = getBlockCustomerMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.BlockCustomerRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "blockCustomer"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.BlockCustomerRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCustomerResponse.getDefaultInstance()))
              .setSchemaDescriptor(new CustomerFacadeMethodDescriptorSupplier("blockCustomer"))
              .build();
        }
      }
    }
    return getBlockCustomerMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest,
      com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse> getBatchValidateCustomerMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "batchValidateCustomer",
      requestType = com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest.class,
      responseType = com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest,
      com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse> getBatchValidateCustomerMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest, com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse> getBatchValidateCustomerMethod;
    if ((getBatchValidateCustomerMethod = CustomerFacadeGrpc.getBatchValidateCustomerMethod) == null) {
      synchronized (CustomerFacadeGrpc.class) {
        if ((getBatchValidateCustomerMethod = CustomerFacadeGrpc.getBatchValidateCustomerMethod) == null) {
          CustomerFacadeGrpc.getBatchValidateCustomerMethod = getBatchValidateCustomerMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest, com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "batchValidateCustomer"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse.getDefaultInstance()))
              .setSchemaDescriptor(new CustomerFacadeMethodDescriptorSupplier("batchValidateCustomer"))
              .build();
        }
      }
    }
    return getBatchValidateCustomerMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getUpdateCustomerTypeMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "updateCustomerType",
      requestType = com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryCustomerResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest,
      com.kikitrade.kcustomer.facade.QueryCustomerResponse> getUpdateCustomerTypeMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse> getUpdateCustomerTypeMethod;
    if ((getUpdateCustomerTypeMethod = CustomerFacadeGrpc.getUpdateCustomerTypeMethod) == null) {
      synchronized (CustomerFacadeGrpc.class) {
        if ((getUpdateCustomerTypeMethod = CustomerFacadeGrpc.getUpdateCustomerTypeMethod) == null) {
          CustomerFacadeGrpc.getUpdateCustomerTypeMethod = getUpdateCustomerTypeMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest, com.kikitrade.kcustomer.facade.QueryCustomerResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "updateCustomerType"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCustomerResponse.getDefaultInstance()))
              .setSchemaDescriptor(new CustomerFacadeMethodDescriptorSupplier("updateCustomerType"))
              .build();
        }
      }
    }
    return getUpdateCustomerTypeMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCustomerRequest,
      com.kikitrade.kcustomer.facade.QueryInviterResponse> getQueryCustomerInviterMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryCustomerInviter",
      requestType = com.kikitrade.kcustomer.facade.QueryCustomerRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryInviterResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCustomerRequest,
      com.kikitrade.kcustomer.facade.QueryInviterResponse> getQueryCustomerInviterMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCustomerRequest, com.kikitrade.kcustomer.facade.QueryInviterResponse> getQueryCustomerInviterMethod;
    if ((getQueryCustomerInviterMethod = CustomerFacadeGrpc.getQueryCustomerInviterMethod) == null) {
      synchronized (CustomerFacadeGrpc.class) {
        if ((getQueryCustomerInviterMethod = CustomerFacadeGrpc.getQueryCustomerInviterMethod) == null) {
          CustomerFacadeGrpc.getQueryCustomerInviterMethod = getQueryCustomerInviterMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.QueryCustomerRequest, com.kikitrade.kcustomer.facade.QueryInviterResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryCustomerInviter"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCustomerRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryInviterResponse.getDefaultInstance()))
              .setSchemaDescriptor(new CustomerFacadeMethodDescriptorSupplier("queryCustomerInviter"))
              .build();
        }
      }
    }
    return getQueryCustomerInviterMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static CustomerFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<CustomerFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<CustomerFacadeStub>() {
        @java.lang.Override
        public CustomerFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new CustomerFacadeStub(channel, callOptions);
        }
      };
    return CustomerFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static CustomerFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<CustomerFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<CustomerFacadeBlockingStub>() {
        @java.lang.Override
        public CustomerFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new CustomerFacadeBlockingStub(channel, callOptions);
        }
      };
    return CustomerFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static CustomerFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<CustomerFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<CustomerFacadeFutureStub>() {
        @java.lang.Override
        public CustomerFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new CustomerFacadeFutureStub(channel, callOptions);
        }
      };
    return CustomerFacadeFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class CustomerFacadeImplBase implements io.grpc.BindableService {

    /**
     */
    public void unbindGoogle(com.kikitrade.kcustomer.facade.UnbindGoogleRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUnbindGoogleMethod(), responseObserver);
    }

    /**
     */
    public void queryCustomer(com.kikitrade.kcustomer.facade.QueryCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCustomerMethod(), responseObserver);
    }

    /**
     */
    public void blockCustomer(com.kikitrade.kcustomer.facade.BlockCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getBlockCustomerMethod(), responseObserver);
    }

    /**
     */
    public void batchValidateCustomer(com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getBatchValidateCustomerMethod(), responseObserver);
    }

    /**
     */
    public void updateCustomerType(com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateCustomerTypeMethod(), responseObserver);
    }

    /**
     */
    public void queryCustomerInviter(com.kikitrade.kcustomer.facade.QueryCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryInviterResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCustomerInviterMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getUnbindGoogleMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.UnbindGoogleRequest,
                com.kikitrade.kcustomer.facade.QueryCustomerResponse>(
                  this, METHODID_UNBIND_GOOGLE)))
          .addMethod(
            getQueryCustomerMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.QueryCustomerRequest,
                com.kikitrade.kcustomer.facade.QueryCustomerResponse>(
                  this, METHODID_QUERY_CUSTOMER)))
          .addMethod(
            getBlockCustomerMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.BlockCustomerRequest,
                com.kikitrade.kcustomer.facade.QueryCustomerResponse>(
                  this, METHODID_BLOCK_CUSTOMER)))
          .addMethod(
            getBatchValidateCustomerMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest,
                com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse>(
                  this, METHODID_BATCH_VALIDATE_CUSTOMER)))
          .addMethod(
            getUpdateCustomerTypeMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest,
                com.kikitrade.kcustomer.facade.QueryCustomerResponse>(
                  this, METHODID_UPDATE_CUSTOMER_TYPE)))
          .addMethod(
            getQueryCustomerInviterMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.QueryCustomerRequest,
                com.kikitrade.kcustomer.facade.QueryInviterResponse>(
                  this, METHODID_QUERY_CUSTOMER_INVITER)))
          .build();
    }
  }

  /**
   */
  public static final class CustomerFacadeStub extends io.grpc.stub.AbstractAsyncStub<CustomerFacadeStub> {
    private CustomerFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected CustomerFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new CustomerFacadeStub(channel, callOptions);
    }

    /**
     */
    public void unbindGoogle(com.kikitrade.kcustomer.facade.UnbindGoogleRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUnbindGoogleMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryCustomer(com.kikitrade.kcustomer.facade.QueryCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCustomerMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void blockCustomer(com.kikitrade.kcustomer.facade.BlockCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getBlockCustomerMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void batchValidateCustomer(com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getBatchValidateCustomerMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void updateCustomerType(com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateCustomerTypeMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void queryCustomerInviter(com.kikitrade.kcustomer.facade.QueryCustomerRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryInviterResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCustomerInviterMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class CustomerFacadeBlockingStub extends io.grpc.stub.AbstractBlockingStub<CustomerFacadeBlockingStub> {
    private CustomerFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected CustomerFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new CustomerFacadeBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.kikitrade.kcustomer.facade.QueryCustomerResponse unbindGoogle(com.kikitrade.kcustomer.facade.UnbindGoogleRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUnbindGoogleMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.kikitrade.kcustomer.facade.QueryCustomerResponse queryCustomer(com.kikitrade.kcustomer.facade.QueryCustomerRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCustomerMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.kikitrade.kcustomer.facade.QueryCustomerResponse blockCustomer(com.kikitrade.kcustomer.facade.BlockCustomerRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getBlockCustomerMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse batchValidateCustomer(com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getBatchValidateCustomerMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.kikitrade.kcustomer.facade.QueryCustomerResponse updateCustomerType(com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateCustomerTypeMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.kikitrade.kcustomer.facade.QueryInviterResponse queryCustomerInviter(com.kikitrade.kcustomer.facade.QueryCustomerRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCustomerInviterMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class CustomerFacadeFutureStub extends io.grpc.stub.AbstractFutureStub<CustomerFacadeFutureStub> {
    private CustomerFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected CustomerFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new CustomerFacadeFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryCustomerResponse> unbindGoogle(
        com.kikitrade.kcustomer.facade.UnbindGoogleRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUnbindGoogleMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryCustomerResponse> queryCustomer(
        com.kikitrade.kcustomer.facade.QueryCustomerRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCustomerMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryCustomerResponse> blockCustomer(
        com.kikitrade.kcustomer.facade.BlockCustomerRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getBlockCustomerMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse> batchValidateCustomer(
        com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getBatchValidateCustomerMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryCustomerResponse> updateCustomerType(
        com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateCustomerTypeMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryInviterResponse> queryCustomerInviter(
        com.kikitrade.kcustomer.facade.QueryCustomerRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCustomerInviterMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_UNBIND_GOOGLE = 0;
  private static final int METHODID_QUERY_CUSTOMER = 1;
  private static final int METHODID_BLOCK_CUSTOMER = 2;
  private static final int METHODID_BATCH_VALIDATE_CUSTOMER = 3;
  private static final int METHODID_UPDATE_CUSTOMER_TYPE = 4;
  private static final int METHODID_QUERY_CUSTOMER_INVITER = 5;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final CustomerFacadeImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(CustomerFacadeImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_UNBIND_GOOGLE:
          serviceImpl.unbindGoogle((com.kikitrade.kcustomer.facade.UnbindGoogleRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse>) responseObserver);
          break;
        case METHODID_QUERY_CUSTOMER:
          serviceImpl.queryCustomer((com.kikitrade.kcustomer.facade.QueryCustomerRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse>) responseObserver);
          break;
        case METHODID_BLOCK_CUSTOMER:
          serviceImpl.blockCustomer((com.kikitrade.kcustomer.facade.BlockCustomerRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse>) responseObserver);
          break;
        case METHODID_BATCH_VALIDATE_CUSTOMER:
          serviceImpl.batchValidateCustomer((com.kikitrade.kcustomer.facade.BatchValidateCustomerRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.BatchValidateCustomerResponse>) responseObserver);
          break;
        case METHODID_UPDATE_CUSTOMER_TYPE:
          serviceImpl.updateCustomerType((com.kikitrade.kcustomer.facade.UpdateCustomerTypeRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCustomerResponse>) responseObserver);
          break;
        case METHODID_QUERY_CUSTOMER_INVITER:
          serviceImpl.queryCustomerInviter((com.kikitrade.kcustomer.facade.QueryCustomerRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryInviterResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class CustomerFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    CustomerFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.kcustomer.facade.CustomerFacadeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("CustomerFacade");
    }
  }

  private static final class CustomerFacadeFileDescriptorSupplier
      extends CustomerFacadeBaseDescriptorSupplier {
    CustomerFacadeFileDescriptorSupplier() {}
  }

  private static final class CustomerFacadeMethodDescriptorSupplier
      extends CustomerFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    CustomerFacadeMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (CustomerFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new CustomerFacadeFileDescriptorSupplier())
              .addMethod(getUnbindGoogleMethod())
              .addMethod(getQueryCustomerMethod())
              .addMethod(getBlockCustomerMethod())
              .addMethod(getBatchValidateCustomerMethod())
              .addMethod(getUpdateCustomerTypeMethod())
              .addMethod(getQueryCustomerInviterMethod())
              .build();
        }
      }
    }
    return result;
  }
}
