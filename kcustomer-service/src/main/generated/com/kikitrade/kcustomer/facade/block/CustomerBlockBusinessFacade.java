// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerBlockBusinessFacade.proto

package com.kikitrade.kcustomer.facade.block;

public final class CustomerBlockBusinessFacade {
  private CustomerBlockBusinessFacade() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_block_CustomerBlockBusiness_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_block_CustomerBlockBusiness_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_block_UpdateCustomerBlockBusinessRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_block_UpdateCustomerBlockBusinessRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_block_ModifyCustomerBlockBusinessResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_block_ModifyCustomerBlockBusinessResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_block_RemoveCustomerBlockBusinessRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_block_RemoveCustomerBlockBusinessRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n!CustomerBlockBusinessFacade.proto\022$com" +
      ".kikitrade.kcustomer.facade.block\032\037googl" +
      "e/protobuf/timestamp.proto\"\353\002\n\025CustomerB" +
      "lockBusiness\022\022\n\ncustomerId\030\001 \001(\t\022\021\n\tbloc" +
      "kType\030\002 \001(\t\022\023\n\013blockReason\030\003 \001(\t\0222\n\016bloc" +
      "kStartTime\030\004 \001(\0132\032.google.protobuf.Times" +
      "tamp\0220\n\014blockEndTime\030\005 \001(\0132\032.google.prot" +
      "obuf.Timestamp\022\020\n\010operator\030\006 \001(\t\022D\n\ntime" +
      "Period\030\007 \001(\01620.com.kikitrade.kcustomer.f" +
      "acade.block.TimePeriod\022\021\n\tmaxAccess\030\010 \001(" +
      "\005\022E\n\010decision\030\t \001(\01623.com.kikitrade.kcus" +
      "tomer.facade.block.FinalDecision\"j\n!Quer" +
      "yCustomerBlockBusinessRequest\022\022\n\ncustome" +
      "rId\030\001 \001(\t\022\017\n\007account\030\002 \001(\t\022\021\n\tblockType\030" +
      "\003 \001(\t\022\r\n\005token\030\004 \001(\t\"\265\001\n\"QueryCustomerBl" +
      "ockBusinessResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007" +
      "message\030\002 \001(\t\022\021\n\tnextToken\030\003 \001(\t\022Z\n\025cust" +
      "omerBlockBusiness\030\004 \003(\0132;.com.kikitrade." +
      "kcustomer.facade.block.CustomerBlockBusi" +
      "ness\"x\n\"UpdateCustomerBlockBusinessReque" +
      "st\022R\n\rcustomerBlock\030\003 \001(\0132;.com.kikitrad" +
      "e.kcustomer.facade.block.CustomerBlockBu" +
      "siness\"G\n#ModifyCustomerBlockBusinessRes" +
      "ponse\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\"" +
      "]\n\"RemoveCustomerBlockBusinessRequest\022\022\n" +
      "\ncustomerId\030\001 \001(\t\022\021\n\tblockType\030\002 \001(\t\022\020\n\010" +
      "operator\030\003 \001(\t*J\n\nTimePeriod\022\n\n\006MINUTE\020\000" +
      "\022\010\n\004HOUR\020\001\022\007\n\003DAY\020\002\022\010\n\004WEEK\020\003\022\t\n\005MONTH\020\004" +
      "\022\010\n\004YEAR\020\005*\'\n\rFinalDecision\022\013\n\007PENDING\020\000" +
      "\022\t\n\005BLOCK\020\0012\304\005\n\023CustomerBlockFacade\022\247\001\n\022" +
      "queryCustomerBlock\022G.com.kikitrade.kcust" +
      "omer.facade.block.QueryCustomerBlockBusi" +
      "nessRequest\032H.com.kikitrade.kcustomer.fa" +
      "cade.block.QueryCustomerBlockBusinessRes" +
      "ponse\022\250\001\n\023customerBlockDetail\022G.com.kiki" +
      "trade.kcustomer.facade.block.QueryCustom" +
      "erBlockBusinessRequest\032H.com.kikitrade.k" +
      "customer.facade.block.QueryCustomerBlock" +
      "BusinessResponse\022\252\001\n\023updateCustomerBlock" +
      "\022H.com.kikitrade.kcustomer.facade.block." +
      "UpdateCustomerBlockBusinessRequest\032I.com" +
      ".kikitrade.kcustomer.facade.block.Modify" +
      "CustomerBlockBusinessResponse\022\252\001\n\023remove" +
      "CustomerBlock\022H.com.kikitrade.kcustomer." +
      "facade.block.RemoveCustomerBlockBusiness" +
      "Request\032I.com.kikitrade.kcustomer.facade" +
      ".block.ModifyCustomerBlockBusinessRespon" +
      "seB(\n$com.kikitrade.kcustomer.facade.blo" +
      "ckP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_kcustomer_facade_block_CustomerBlockBusiness_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_kcustomer_facade_block_CustomerBlockBusiness_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_block_CustomerBlockBusiness_descriptor,
        new java.lang.String[] { "CustomerId", "BlockType", "BlockReason", "BlockStartTime", "BlockEndTime", "Operator", "TimePeriod", "MaxAccess", "Decision", });
    internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessRequest_descriptor,
        new java.lang.String[] { "CustomerId", "Account", "BlockType", "Token", });
    internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_block_QueryCustomerBlockBusinessResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "NextToken", "CustomerBlockBusiness", });
    internal_static_com_kikitrade_kcustomer_facade_block_UpdateCustomerBlockBusinessRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_kikitrade_kcustomer_facade_block_UpdateCustomerBlockBusinessRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_block_UpdateCustomerBlockBusinessRequest_descriptor,
        new java.lang.String[] { "CustomerBlock", });
    internal_static_com_kikitrade_kcustomer_facade_block_ModifyCustomerBlockBusinessResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_kikitrade_kcustomer_facade_block_ModifyCustomerBlockBusinessResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_block_ModifyCustomerBlockBusinessResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_kikitrade_kcustomer_facade_block_RemoveCustomerBlockBusinessRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_kikitrade_kcustomer_facade_block_RemoveCustomerBlockBusinessRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_block_RemoveCustomerBlockBusinessRequest_descriptor,
        new java.lang.String[] { "CustomerId", "BlockType", "Operator", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
