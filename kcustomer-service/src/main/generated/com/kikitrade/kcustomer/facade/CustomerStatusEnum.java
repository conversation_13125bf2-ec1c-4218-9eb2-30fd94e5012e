// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerFacade.proto

package com.kikitrade.kcustomer.facade;

/**
 * Protobuf enum {@code com.kikitrade.kcustomer.facade.CustomerStatusEnum}
 */
public enum CustomerStatusEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * (0, "预注册"),
   * </pre>
   *
   * <code>Applying = 0;</code>
   */
  Applying(0),
  /**
   * <pre>
   *(1, "已激活"),
   * </pre>
   *
   * <code>Active = 1;</code>
   */
  Active(1),
  /**
   * <pre>
   *(2, "禁止"),
   * </pre>
   *
   * <code>Forbidden = 2;</code>
   */
  Forbidden(2),
  /**
   * <pre>
   *(3, "注销");
   * </pre>
   *
   * <code>Cancelled = 3;</code>
   */
  Cancelled(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * (0, "预注册"),
   * </pre>
   *
   * <code>Applying = 0;</code>
   */
  public static final int Applying_VALUE = 0;
  /**
   * <pre>
   *(1, "已激活"),
   * </pre>
   *
   * <code>Active = 1;</code>
   */
  public static final int Active_VALUE = 1;
  /**
   * <pre>
   *(2, "禁止"),
   * </pre>
   *
   * <code>Forbidden = 2;</code>
   */
  public static final int Forbidden_VALUE = 2;
  /**
   * <pre>
   *(3, "注销");
   * </pre>
   *
   * <code>Cancelled = 3;</code>
   */
  public static final int Cancelled_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static CustomerStatusEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static CustomerStatusEnum forNumber(int value) {
    switch (value) {
      case 0: return Applying;
      case 1: return Active;
      case 2: return Forbidden;
      case 3: return Cancelled;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<CustomerStatusEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      CustomerStatusEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<CustomerStatusEnum>() {
          public CustomerStatusEnum findValueByNumber(int number) {
            return CustomerStatusEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.kcustomer.facade.CustomerFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final CustomerStatusEnum[] VALUES = values();

  public static CustomerStatusEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private CustomerStatusEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.kcustomer.facade.CustomerStatusEnum)
}

