// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerKycFacade.proto

package com.kikitrade.kcustomer.facade.kyc;

public interface CustomerKycDetailRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.kcustomer.facade.kyc.CustomerKycDetailRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string saasId = 1;</code>
   * @return The saasId.
   */
  java.lang.String getSaasId();
  /**
   * <code>string saasId = 1;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>string id = 2;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 2;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();
}
