// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerInfoFacade.proto

package com.kikitrade.kcustomer.facade.customer;

/**
 * Protobuf enum {@code com.kikitrade.kcustomer.facade.Gender}
 */
public enum Gender
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *未知,
   * </pre>
   *
   * <code>unknown = 0;</code>
   */
  unknown(0),
  /**
   * <pre>
   * 1=male,
   * </pre>
   *
   * <code>male = 1;</code>
   */
  male(1),
  /**
   * <pre>
   * 2=female
   * </pre>
   *
   * <code>female = 2;</code>
   */
  female(2),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *未知,
   * </pre>
   *
   * <code>unknown = 0;</code>
   */
  public static final int unknown_VALUE = 0;
  /**
   * <pre>
   * 1=male,
   * </pre>
   *
   * <code>male = 1;</code>
   */
  public static final int male_VALUE = 1;
  /**
   * <pre>
   * 2=female
   * </pre>
   *
   * <code>female = 2;</code>
   */
  public static final int female_VALUE = 2;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @Deprecated
  public static Gender valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static Gender forNumber(int value) {
    switch (value) {
      case 0: return unknown;
      case 1: return male;
      case 2: return female;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<Gender>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      Gender> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<Gender>() {
          public Gender findValueByNumber(int number) {
            return Gender.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return CustomerInfoFacadeOuterClass.getDescriptor().getEnumTypes().get(2);
  }

  private static final Gender[] VALUES = values();

  public static Gender valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private Gender(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.kcustomer.facade.Gender)
}

