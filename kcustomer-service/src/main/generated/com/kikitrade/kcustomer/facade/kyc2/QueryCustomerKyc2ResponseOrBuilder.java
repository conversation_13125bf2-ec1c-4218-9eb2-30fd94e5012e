// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerKyc2Facade.proto

package com.kikitrade.kcustomer.facade.kyc2;

public interface QueryCustomerKyc2ResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.kcustomer.facade.kyc2.QueryCustomerKyc2Response)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2 customer = 3;</code>
   */
  java.util.List<com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2> 
      getCustomerList();
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2 customer = 3;</code>
   */
  com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2 getCustomer(int index);
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2 customer = 3;</code>
   */
  int getCustomerCount();
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2 customer = 3;</code>
   */
  java.util.List<? extends com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2OrBuilder> 
      getCustomerOrBuilderList();
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2 customer = 3;</code>
   */
  com.kikitrade.kcustomer.facade.kyc2.CustomerKyc2OrBuilder getCustomerOrBuilder(
      int index);
}
