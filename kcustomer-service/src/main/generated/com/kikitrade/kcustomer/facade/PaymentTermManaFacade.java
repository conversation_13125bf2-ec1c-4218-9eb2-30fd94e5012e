// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentTermManaFacade.proto

package com.kikitrade.kcustomer.facade;

public final class PaymentTermManaFacade {
  private PaymentTermManaFacade() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_PaymentTermInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_PaymentTermInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_CustomerInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_CustomerInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_CircleSupportBank_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_CircleSupportBank_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033PaymentTermManaFacade.proto\022\036com.kikit" +
      "rade.kcustomer.facade\032\037google/protobuf/t" +
      "imestamp.proto\"\231\004\n\017PaymentTermInfo\022\022\n\ncu" +
      "stomerId\030\001 \001(\t\022\n\n\002id\030\002 \001(\t\022\021\n\taccountNo\030" +
      "\003 \001(\t\022\020\n\010bankName\030\004 \001(\t\022\022\n\nnameAtBank\030\005 " +
      "\001(\t\022A\n\006status\030\006 \001(\01621.com.kikitrade.kcus" +
      "tomer.facade.PaymentTermStatus\022+\n\007create" +
      "d\030\007 \001(\0132\032.google.protobuf.Timestamp\022,\n\010m" +
      "odified\030\010 \001(\0132\032.google.protobuf.Timestam" +
      "p\022\016\n\006saasId\030\t \001(\t\022\016\n\006remark\030\n \001(\t\022\022\n\nsta" +
      "tusDesc\030\013 \003(\t\022\014\n\004type\030\014 \001(\005\0224\n\005usage\030\r \001" +
      "(\0162%.com.kikitrade.kcustomer.facade.Usag" +
      "e\022\017\n\007outerId\030\016 \001(\t\022\021\n\tswiftCode\030\017 \001(\t\022\025\n" +
      "\rroutingNumber\030\020 \001(\t\022\014\n\004iban\030\021 \001(\t\022\014\n\004ci" +
      "ty\030\022 \001(\t\022\017\n\007country\030\023 \001(\t\022\016\n\006street\030\024 \001(" +
      "\t\022\020\n\010district\030\025 \001(\t\022\r\n\005proof\030\026 \001(\t\"\361\001\n\014C" +
      "ustomerInfo\022\022\n\ncustomerId\030\001 \001(\t\022\021\n\tfirst" +
      "Name\030\002 \001(\t\022\020\n\010lastName\030\003 \001(\t\022\022\n\nkycCount" +
      "ry\030\004 \001(\t\022\020\n\010address1\030\005 \001(\t\022\020\n\010address2\030\006" +
      " \001(\t\022\022\n\npostalCode\030\007 \001(\t\022\017\n\007kycCity\030\010 \001(" +
      "\t\022\031\n\021passportFirstName\030\t \001(\t\022\030\n\020passport" +
      "LastName\030\n \001(\t\022\026\n\016passportOcrPic\030\013 \001(\t\"\354" +
      "\001\n\033QueryPaymentTermListRequest\022\016\n\006saasId" +
      "\030\001 \001(\t\022\022\n\ncustomerId\030\002 \001(\t\022F\n\013auditStatu" +
      "s\030\003 \001(\01621.com.kikitrade.kcustomer.facade" +
      ".PaymentTermStatus\022\014\n\004type\030\004 \001(\005\0224\n\005usag" +
      "e\030\005 \001(\0162%.com.kikitrade.kcustomer.facade" +
      ".Usage\022\016\n\006offset\030\006 \001(\005\022\r\n\005limit\030\007 \001(\005\"\211\001" +
      "\n\034QueryPaymentTermListResponse\022\017\n\007succes" +
      "s\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022G\n\016paymentWayIn" +
      "fo\030\003 \003(\0132/.com.kikitrade.kcustomer.facad" +
      "e.PaymentTermInfo\"O\n\035QueryPaymentTermDet" +
      "ailRequest\022\016\n\006saasId\030\001 \001(\t\022\n\n\002id\030\002 \001(\t\022\022" +
      "\n\ncustomerId\030\003 \001(\t\"\320\001\n\036QueryPaymentTermD" +
      "etailResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007messag" +
      "e\030\002 \001(\t\022H\n\017paymentTermInfo\030\003 \001(\0132/.com.k" +
      "ikitrade.kcustomer.facade.PaymentTermInf" +
      "o\022B\n\014customerInfo\030\004 \001(\0132,.com.kikitrade." +
      "kcustomer.facade.CustomerInfo\"\215\002\n\030Update" +
      "PaymentTermRequest\022\016\n\006saasId\030\001 \001(\t\022\022\n\ncu" +
      "stomerId\030\002 \001(\t\022\n\n\002id\030\003 \001(\t\022\020\n\010bankName\030\004" +
      " \001(\t\022\014\n\004city\030\005 \001(\t\022\021\n\tswiftCode\030\006 \001(\t\022\021\n" +
      "\taccountNo\030\007 \001(\t\022\017\n\007country\030\010 \001(\t\022\022\n\nkyc" +
      "Country\030\t \001(\t\022\017\n\007kycCity\030\n \001(\t\022\020\n\010addres" +
      "s1\030\013 \001(\t\022\020\n\010address2\030\014 \001(\t\022\022\n\npostalCode" +
      "\030\r \001(\t\022\r\n\005proof\030\016 \001(\t\"=\n\031UpdatePaymentTe" +
      "rmResponse\022\017\n\007success\030\001 \001(\010\022\017\n\007message\030\002" +
      " \001(\t\"\212\001\n\021CircleSupportBank\022\014\n\004name\030\001 \001(\t" +
      "\022\021\n\tswiftCode\030\002 \001(\t\022\020\n\010bankCode\030\003 \001(\t\022\017\n" +
      "\007country\030\004 \001(\t\022\014\n\004city\030\005 \001(\t\022\017\n\007address\030" +
      "\006 \001(\t\022\022\n\nnetworkFee\030\007 \001(\001\"3\n!QueryCircle" +
      "SupportBankListRequest\022\016\n\006saasId\030\001 \001(\t\"\224" +
      "\001\n\"QueryCircleSupportBankListResponse\022\017\n" +
      "\007success\030\001 \001(\010\022\017\n\007message\030\002 \001(\t\022L\n\021circl" +
      "eSupportBank\030\003 \003(\01321.com.kikitrade.kcust" +
      "omer.facade.CircleSupportBank*\036\n\005Usage\022\t" +
      "\n\005audit\020\000\022\n\n\006circle\020\001*f\n\021PaymentTermStat" +
      "us\022\007\n\003all\020\000\022\r\n\tunaudited\020\001\022\014\n\010approved\020\002" +
      "\022\014\n\010rejected\020\003\022\016\n\naddSuccess\020\004\022\r\n\taddFai" +
      "led\020\0052\370\004\n\027PaymentTermManageFacade\022\221\001\n\024qu" +
      "eryPaymentTermList\022;.com.kikitrade.kcust" +
      "omer.facade.QueryPaymentTermListRequest\032" +
      "<.com.kikitrade.kcustomer.facade.QueryPa" +
      "ymentTermListResponse\022\227\001\n\026queryPaymentTe" +
      "rmDetail\022=.com.kikitrade.kcustomer.facad" +
      "e.QueryPaymentTermDetailRequest\032>.com.ki" +
      "kitrade.kcustomer.facade.QueryPaymentTer" +
      "mDetailResponse\022\210\001\n\021updatePaymentTerm\0228." +
      "com.kikitrade.kcustomer.facade.UpdatePay" +
      "mentTermRequest\0329.com.kikitrade.kcustome" +
      "r.facade.UpdatePaymentTermResponse\022\243\001\n\032q" +
      "ueryCircleSupportBankList\022A.com.kikitrad" +
      "e.kcustomer.facade.QueryCircleSupportBan" +
      "kListRequest\032B.com.kikitrade.kcustomer.f" +
      "acade.QueryCircleSupportBankListResponse" +
      "B\"\n\036com.kikitrade.kcustomer.facadeP\001b\006pr" +
      "oto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.TimestampProto.getDescriptor(),
        });
    internal_static_com_kikitrade_kcustomer_facade_PaymentTermInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_kikitrade_kcustomer_facade_PaymentTermInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_PaymentTermInfo_descriptor,
        new java.lang.String[] { "CustomerId", "Id", "AccountNo", "BankName", "NameAtBank", "Status", "Created", "Modified", "SaasId", "Remark", "StatusDesc", "Type", "Usage", "OuterId", "SwiftCode", "RoutingNumber", "Iban", "City", "Country", "Street", "District", "Proof", });
    internal_static_com_kikitrade_kcustomer_facade_CustomerInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_kikitrade_kcustomer_facade_CustomerInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_CustomerInfo_descriptor,
        new java.lang.String[] { "CustomerId", "FirstName", "LastName", "KycCountry", "Address1", "Address2", "PostalCode", "KycCity", "PassportFirstName", "PassportLastName", "PassportOcrPic", });
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListRequest_descriptor,
        new java.lang.String[] { "SaasId", "CustomerId", "AuditStatus", "Type", "Usage", "Offset", "Limit", });
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "PaymentWayInfo", });
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailRequest_descriptor,
        new java.lang.String[] { "SaasId", "Id", "CustomerId", });
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermDetailResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "PaymentTermInfo", "CustomerInfo", });
    internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermRequest_descriptor,
        new java.lang.String[] { "SaasId", "CustomerId", "Id", "BankName", "City", "SwiftCode", "AccountNo", "Country", "KycCountry", "KycCity", "Address1", "Address2", "PostalCode", "Proof", });
    internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_UpdatePaymentTermResponse_descriptor,
        new java.lang.String[] { "Success", "Message", });
    internal_static_com_kikitrade_kcustomer_facade_CircleSupportBank_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_kikitrade_kcustomer_facade_CircleSupportBank_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_CircleSupportBank_descriptor,
        new java.lang.String[] { "Name", "SwiftCode", "BankCode", "Country", "City", "Address", "NetworkFee", });
    internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListRequest_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListRequest_descriptor,
        new java.lang.String[] { "SaasId", });
    internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListResponse_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_kikitrade_kcustomer_facade_QueryCircleSupportBankListResponse_descriptor,
        new java.lang.String[] { "Success", "Message", "CircleSupportBank", });
    com.google.protobuf.TimestampProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
