// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerInfoFacade.proto

package com.kikitrade.kcustomer.facade.customer;

public interface CustomerInfoDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.kcustomer.facade.CustomerInfoDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>.com.kikitrade.kcustomer.facade.StatusEnum status = 2;</code>
   * @return The enum numeric value on the wire for status.
   */
  int getStatusValue();
  /**
   * <code>.com.kikitrade.kcustomer.facade.StatusEnum status = 2;</code>
   * @return The status.
   */
  StatusEnum getStatus();

  /**
   * <code>string email = 3;</code>
   * @return The email.
   */
  String getEmail();
  /**
   * <code>string email = 3;</code>
   * @return The bytes for email.
   */
  com.google.protobuf.ByteString
      getEmailBytes();

  /**
   * <code>string phone = 4;</code>
   * @return The phone.
   */
  String getPhone();
  /**
   * <code>string phone = 4;</code>
   * @return The bytes for phone.
   */
  com.google.protobuf.ByteString
      getPhoneBytes();

  /**
   * <code>.google.protobuf.Timestamp registerTime = 5;</code>
   * @return Whether the registerTime field is set.
   */
  boolean hasRegisterTime();
  /**
   * <code>.google.protobuf.Timestamp registerTime = 5;</code>
   * @return The registerTime.
   */
  com.google.protobuf.Timestamp getRegisterTime();
  /**
   * <code>.google.protobuf.Timestamp registerTime = 5;</code>
   */
  com.google.protobuf.TimestampOrBuilder getRegisterTimeOrBuilder();

  /**
   * <code>string inviter = 6;</code>
   * @return The inviter.
   */
  String getInviter();
  /**
   * <code>string inviter = 6;</code>
   * @return The bytes for inviter.
   */
  com.google.protobuf.ByteString
      getInviterBytes();

  /**
   * <code>.com.kikitrade.kcustomer.facade.Gender gender = 7;</code>
   * @return The enum numeric value on the wire for gender.
   */
  int getGenderValue();
  /**
   * <code>.com.kikitrade.kcustomer.facade.Gender gender = 7;</code>
   * @return The gender.
   */
  Gender getGender();

  /**
   * <code>string region = 8;</code>
   * @return The region.
   */
  String getRegion();
  /**
   * <code>string region = 8;</code>
   * @return The bytes for region.
   */
  com.google.protobuf.ByteString
      getRegionBytes();

  /**
   * <code>string operator = 9;</code>
   * @return The operator.
   */
  String getOperator();
  /**
   * <code>string operator = 9;</code>
   * @return The bytes for operator.
   */
  com.google.protobuf.ByteString
      getOperatorBytes();

  /**
   * <pre>
   * = false;//是否开启谷歌认证
   * </pre>
   *
   * <code>bool isGoogleCertified = 10;</code>
   * @return The isGoogleCertified.
   */
  boolean getIsGoogleCertified();

  /**
   * <code>string vipLevel = 11;</code>
   * @return The vipLevel.
   */
  String getVipLevel();
  /**
   * <code>string vipLevel = 11;</code>
   * @return The bytes for vipLevel.
   */
  com.google.protobuf.ByteString
      getVipLevelBytes();

  /**
   * <code>string saasId = 12;</code>
   * @return The saasId.
   */
  String getSaasId();
  /**
   * <code>string saasId = 12;</code>
   * @return The bytes for saasId.
   */
  com.google.protobuf.ByteString
      getSaasIdBytes();

  /**
   * <code>.google.protobuf.Timestamp vipEndTime = 13;</code>
   * @return Whether the vipEndTime field is set.
   */
  boolean hasVipEndTime();
  /**
   * <code>.google.protobuf.Timestamp vipEndTime = 13;</code>
   * @return The vipEndTime.
   */
  com.google.protobuf.Timestamp getVipEndTime();
  /**
   * <code>.google.protobuf.Timestamp vipEndTime = 13;</code>
   */
  com.google.protobuf.TimestampOrBuilder getVipEndTimeOrBuilder();

  /**
   * <pre>
   *注册类型
   * </pre>
   *
   * <code>string registerType = 14;</code>
   * @return The registerType.
   */
  String getRegisterType();
  /**
   * <pre>
   *注册类型
   * </pre>
   *
   * <code>string registerType = 14;</code>
   * @return The bytes for registerType.
   */
  com.google.protobuf.ByteString
      getRegisterTypeBytes();

  /**
   * <pre>
   *锁定状态
   * </pre>
   *
   * <code>bool isLock = 15;</code>
   * @return The isLock.
   */
  boolean getIsLock();

  /**
   * <pre>
   *锁定时间
   * </pre>
   *
   * <code>.google.protobuf.Timestamp lockTime = 16;</code>
   * @return Whether the lockTime field is set.
   */
  boolean hasLockTime();
  /**
   * <pre>
   *锁定时间
   * </pre>
   *
   * <code>.google.protobuf.Timestamp lockTime = 16;</code>
   * @return The lockTime.
   */
  com.google.protobuf.Timestamp getLockTime();
  /**
   * <pre>
   *锁定时间
   * </pre>
   *
   * <code>.google.protobuf.Timestamp lockTime = 16;</code>
   */
  com.google.protobuf.TimestampOrBuilder getLockTimeOrBuilder();

  /**
   * <pre>
   *kyc级别
   * </pre>
   *
   * <code>string kycLevel = 18;</code>
   * @return The kycLevel.
   */
  String getKycLevel();
  /**
   * <pre>
   *kyc级别
   * </pre>
   *
   * <code>string kycLevel = 18;</code>
   * @return The bytes for kycLevel.
   */
  com.google.protobuf.ByteString
      getKycLevelBytes();

  /**
   * <pre>
   *语言区
   * </pre>
   *
   * <code>string locale = 19;</code>
   * @return The locale.
   */
  String getLocale();
  /**
   * <pre>
   *语言区
   * </pre>
   *
   * <code>string locale = 19;</code>
   * @return The bytes for locale.
   */
  com.google.protobuf.ByteString
      getLocaleBytes();

  /**
   * <pre>
   *社区昵称
   * </pre>
   *
   * <code>string nickName = 20;</code>
   * @return The nickName.
   */
  String getNickName();
  /**
   * <pre>
   *社区昵称
   * </pre>
   *
   * <code>string nickName = 20;</code>
   * @return The bytes for nickName.
   */
  com.google.protobuf.ByteString
      getNickNameBytes();

  /**
   * <pre>
   *注册设备类型
   * </pre>
   *
   * <code>string registerDeviceType = 21;</code>
   * @return The registerDeviceType.
   */
  String getRegisterDeviceType();
  /**
   * <pre>
   *注册设备类型
   * </pre>
   *
   * <code>string registerDeviceType = 21;</code>
   * @return The bytes for registerDeviceType.
   */
  com.google.protobuf.ByteString
      getRegisterDeviceTypeBytes();

  /**
   * <pre>
   *头像
   * </pre>
   *
   * <code>string avatar = 22;</code>
   * @return The avatar.
   */
  String getAvatar();
  /**
   * <pre>
   *头像
   * </pre>
   *
   * <code>string avatar = 22;</code>
   * @return The bytes for avatar.
   */
  com.google.protobuf.ByteString
      getAvatarBytes();
}
