// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerKycFacade.proto

package com.kikitrade.kcustomer.facade.kyc;

/**
 * Protobuf type {@code com.kikitrade.kcustomer.facade.kyc.AuditKycResponse}
 */
public final class AuditKycResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.kcustomer.facade.kyc.AuditKycResponse)
    AuditKycResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AuditKycResponse.newBuilder() to construct.
  private AuditKycResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AuditKycResponse() {
    message_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AuditKycResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AuditKycResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            success_ = input.readBool();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            message_ = s;
            break;
          }
          case 26: {
            com.kikitrade.kcustomer.facade.kyc.CustomerKyc.Builder subBuilder = null;
            if (customer_ != null) {
              subBuilder = customer_.toBuilder();
            }
            customer_ = input.readMessage(com.kikitrade.kcustomer.facade.kyc.CustomerKyc.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(customer_);
              customer_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (com.google.protobuf.UninitializedMessageException e) {
      throw e.asInvalidProtocolBufferException().setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.kcustomer.facade.kyc.CustomerKycFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_kyc_AuditKycResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.kcustomer.facade.kyc.CustomerKycFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_kyc_AuditKycResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.kcustomer.facade.kyc.AuditKycResponse.class, com.kikitrade.kcustomer.facade.kyc.AuditKycResponse.Builder.class);
  }

  public static final int SUCCESS_FIELD_NUMBER = 1;
  private boolean success_;
  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object message_;
  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  @java.lang.Override
  public java.lang.String getMessage() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CUSTOMER_FIELD_NUMBER = 3;
  private com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer_;
  /**
   * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
   * @return Whether the customer field is set.
   */
  @java.lang.Override
  public boolean hasCustomer() {
    return customer_ != null;
  }
  /**
   * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
   * @return The customer.
   */
  @java.lang.Override
  public com.kikitrade.kcustomer.facade.kyc.CustomerKyc getCustomer() {
    return customer_ == null ? com.kikitrade.kcustomer.facade.kyc.CustomerKyc.getDefaultInstance() : customer_;
  }
  /**
   * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.kcustomer.facade.kyc.CustomerKycOrBuilder getCustomerOrBuilder() {
    return getCustomer();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (success_ != false) {
      output.writeBool(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
    }
    if (customer_ != null) {
      output.writeMessage(3, getCustomer());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
    }
    if (customer_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getCustomer());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.kcustomer.facade.kyc.AuditKycResponse)) {
      return super.equals(obj);
    }
    com.kikitrade.kcustomer.facade.kyc.AuditKycResponse other = (com.kikitrade.kcustomer.facade.kyc.AuditKycResponse) obj;

    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (hasCustomer() != other.hasCustomer()) return false;
    if (hasCustomer()) {
      if (!getCustomer()
          .equals(other.getCustomer())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    if (hasCustomer()) {
      hash = (37 * hash) + CUSTOMER_FIELD_NUMBER;
      hash = (53 * hash) + getCustomer().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.kcustomer.facade.kyc.AuditKycResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.kcustomer.facade.kyc.AuditKycResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.kcustomer.facade.kyc.AuditKycResponse)
      com.kikitrade.kcustomer.facade.kyc.AuditKycResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.kcustomer.facade.kyc.CustomerKycFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_kyc_AuditKycResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.kcustomer.facade.kyc.CustomerKycFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_kyc_AuditKycResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.kcustomer.facade.kyc.AuditKycResponse.class, com.kikitrade.kcustomer.facade.kyc.AuditKycResponse.Builder.class);
    }

    // Construct using com.kikitrade.kcustomer.facade.kyc.AuditKycResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      success_ = false;

      message_ = "";

      if (customerBuilder_ == null) {
        customer_ = null;
      } else {
        customer_ = null;
        customerBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.kcustomer.facade.kyc.CustomerKycFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_kyc_AuditKycResponse_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.kcustomer.facade.kyc.AuditKycResponse getDefaultInstanceForType() {
      return com.kikitrade.kcustomer.facade.kyc.AuditKycResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.kcustomer.facade.kyc.AuditKycResponse build() {
      com.kikitrade.kcustomer.facade.kyc.AuditKycResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.kcustomer.facade.kyc.AuditKycResponse buildPartial() {
      com.kikitrade.kcustomer.facade.kyc.AuditKycResponse result = new com.kikitrade.kcustomer.facade.kyc.AuditKycResponse(this);
      result.success_ = success_;
      result.message_ = message_;
      if (customerBuilder_ == null) {
        result.customer_ = customer_;
      } else {
        result.customer_ = customerBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.kcustomer.facade.kyc.AuditKycResponse) {
        return mergeFrom((com.kikitrade.kcustomer.facade.kyc.AuditKycResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.kcustomer.facade.kyc.AuditKycResponse other) {
      if (other == com.kikitrade.kcustomer.facade.kyc.AuditKycResponse.getDefaultInstance()) return this;
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        onChanged();
      }
      if (other.hasCustomer()) {
        mergeCustomer(other.getCustomer());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.kikitrade.kcustomer.facade.kyc.AuditKycResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.kikitrade.kcustomer.facade.kyc.AuditKycResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private boolean success_ ;
    /**
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <code>bool success = 1;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {
      
      success_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool success = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object message_ = "";
    /**
     * <code>string message = 2;</code>
     * @return The message.
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      message_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      
      message_ = getDefaultInstance().getMessage();
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      message_ = value;
      onChanged();
      return this;
    }

    private com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kcustomer.facade.kyc.CustomerKyc, com.kikitrade.kcustomer.facade.kyc.CustomerKyc.Builder, com.kikitrade.kcustomer.facade.kyc.CustomerKycOrBuilder> customerBuilder_;
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     * @return Whether the customer field is set.
     */
    public boolean hasCustomer() {
      return customerBuilder_ != null || customer_ != null;
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     * @return The customer.
     */
    public com.kikitrade.kcustomer.facade.kyc.CustomerKyc getCustomer() {
      if (customerBuilder_ == null) {
        return customer_ == null ? com.kikitrade.kcustomer.facade.kyc.CustomerKyc.getDefaultInstance() : customer_;
      } else {
        return customerBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     */
    public Builder setCustomer(com.kikitrade.kcustomer.facade.kyc.CustomerKyc value) {
      if (customerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        customer_ = value;
        onChanged();
      } else {
        customerBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     */
    public Builder setCustomer(
        com.kikitrade.kcustomer.facade.kyc.CustomerKyc.Builder builderForValue) {
      if (customerBuilder_ == null) {
        customer_ = builderForValue.build();
        onChanged();
      } else {
        customerBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     */
    public Builder mergeCustomer(com.kikitrade.kcustomer.facade.kyc.CustomerKyc value) {
      if (customerBuilder_ == null) {
        if (customer_ != null) {
          customer_ =
            com.kikitrade.kcustomer.facade.kyc.CustomerKyc.newBuilder(customer_).mergeFrom(value).buildPartial();
        } else {
          customer_ = value;
        }
        onChanged();
      } else {
        customerBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     */
    public Builder clearCustomer() {
      if (customerBuilder_ == null) {
        customer_ = null;
        onChanged();
      } else {
        customer_ = null;
        customerBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     */
    public com.kikitrade.kcustomer.facade.kyc.CustomerKyc.Builder getCustomerBuilder() {
      
      onChanged();
      return getCustomerFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     */
    public com.kikitrade.kcustomer.facade.kyc.CustomerKycOrBuilder getCustomerOrBuilder() {
      if (customerBuilder_ != null) {
        return customerBuilder_.getMessageOrBuilder();
      } else {
        return customer_ == null ?
            com.kikitrade.kcustomer.facade.kyc.CustomerKyc.getDefaultInstance() : customer_;
      }
    }
    /**
     * <code>.com.kikitrade.kcustomer.facade.kyc.CustomerKyc customer = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kcustomer.facade.kyc.CustomerKyc, com.kikitrade.kcustomer.facade.kyc.CustomerKyc.Builder, com.kikitrade.kcustomer.facade.kyc.CustomerKycOrBuilder> 
        getCustomerFieldBuilder() {
      if (customerBuilder_ == null) {
        customerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.kikitrade.kcustomer.facade.kyc.CustomerKyc, com.kikitrade.kcustomer.facade.kyc.CustomerKyc.Builder, com.kikitrade.kcustomer.facade.kyc.CustomerKycOrBuilder>(
                getCustomer(),
                getParentForChildren(),
                isClean());
        customer_ = null;
      }
      return customerBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.kcustomer.facade.kyc.AuditKycResponse)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.kcustomer.facade.kyc.AuditKycResponse)
  private static final com.kikitrade.kcustomer.facade.kyc.AuditKycResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.kcustomer.facade.kyc.AuditKycResponse();
  }

  public static com.kikitrade.kcustomer.facade.kyc.AuditKycResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AuditKycResponse>
      PARSER = new com.google.protobuf.AbstractParser<AuditKycResponse>() {
    @java.lang.Override
    public AuditKycResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AuditKycResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AuditKycResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AuditKycResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.kcustomer.facade.kyc.AuditKycResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

