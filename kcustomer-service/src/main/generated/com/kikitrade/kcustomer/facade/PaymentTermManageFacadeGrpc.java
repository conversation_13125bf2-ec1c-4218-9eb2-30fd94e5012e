package com.kikitrade.kcustomer.facade;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * circle入金银行卡管理
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.52.1)",
    comments = "Source: PaymentTermManaFacade.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class PaymentTermManageFacadeGrpc {

  private PaymentTermManageFacadeGrpc() {}

  public static final String SERVICE_NAME = "com.kikitrade.kcustomer.facade.PaymentTermManageFacade";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest,
      com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse> getQueryPaymentTermListMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryPaymentTermList",
      requestType = com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest,
      com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse> getQueryPaymentTermListMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest, com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse> getQueryPaymentTermListMethod;
    if ((getQueryPaymentTermListMethod = PaymentTermManageFacadeGrpc.getQueryPaymentTermListMethod) == null) {
      synchronized (PaymentTermManageFacadeGrpc.class) {
        if ((getQueryPaymentTermListMethod = PaymentTermManageFacadeGrpc.getQueryPaymentTermListMethod) == null) {
          PaymentTermManageFacadeGrpc.getQueryPaymentTermListMethod = getQueryPaymentTermListMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest, com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryPaymentTermList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentTermManageFacadeMethodDescriptorSupplier("queryPaymentTermList"))
              .build();
        }
      }
    }
    return getQueryPaymentTermListMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest,
      com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse> getQueryPaymentTermDetailMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryPaymentTermDetail",
      requestType = com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest,
      com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse> getQueryPaymentTermDetailMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest, com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse> getQueryPaymentTermDetailMethod;
    if ((getQueryPaymentTermDetailMethod = PaymentTermManageFacadeGrpc.getQueryPaymentTermDetailMethod) == null) {
      synchronized (PaymentTermManageFacadeGrpc.class) {
        if ((getQueryPaymentTermDetailMethod = PaymentTermManageFacadeGrpc.getQueryPaymentTermDetailMethod) == null) {
          PaymentTermManageFacadeGrpc.getQueryPaymentTermDetailMethod = getQueryPaymentTermDetailMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest, com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryPaymentTermDetail"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentTermManageFacadeMethodDescriptorSupplier("queryPaymentTermDetail"))
              .build();
        }
      }
    }
    return getQueryPaymentTermDetailMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest,
      com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse> getUpdatePaymentTermMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "updatePaymentTerm",
      requestType = com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest.class,
      responseType = com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest,
      com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse> getUpdatePaymentTermMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest, com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse> getUpdatePaymentTermMethod;
    if ((getUpdatePaymentTermMethod = PaymentTermManageFacadeGrpc.getUpdatePaymentTermMethod) == null) {
      synchronized (PaymentTermManageFacadeGrpc.class) {
        if ((getUpdatePaymentTermMethod = PaymentTermManageFacadeGrpc.getUpdatePaymentTermMethod) == null) {
          PaymentTermManageFacadeGrpc.getUpdatePaymentTermMethod = getUpdatePaymentTermMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest, com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "updatePaymentTerm"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentTermManageFacadeMethodDescriptorSupplier("updatePaymentTerm"))
              .build();
        }
      }
    }
    return getUpdatePaymentTermMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest,
      com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse> getQueryCircleSupportBankListMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "queryCircleSupportBankList",
      requestType = com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest.class,
      responseType = com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest,
      com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse> getQueryCircleSupportBankListMethod() {
    io.grpc.MethodDescriptor<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest, com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse> getQueryCircleSupportBankListMethod;
    if ((getQueryCircleSupportBankListMethod = PaymentTermManageFacadeGrpc.getQueryCircleSupportBankListMethod) == null) {
      synchronized (PaymentTermManageFacadeGrpc.class) {
        if ((getQueryCircleSupportBankListMethod = PaymentTermManageFacadeGrpc.getQueryCircleSupportBankListMethod) == null) {
          PaymentTermManageFacadeGrpc.getQueryCircleSupportBankListMethod = getQueryCircleSupportBankListMethod =
              io.grpc.MethodDescriptor.<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest, com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "queryCircleSupportBankList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentTermManageFacadeMethodDescriptorSupplier("queryCircleSupportBankList"))
              .build();
        }
      }
    }
    return getQueryCircleSupportBankListMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static PaymentTermManageFacadeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PaymentTermManageFacadeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PaymentTermManageFacadeStub>() {
        @java.lang.Override
        public PaymentTermManageFacadeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PaymentTermManageFacadeStub(channel, callOptions);
        }
      };
    return PaymentTermManageFacadeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static PaymentTermManageFacadeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PaymentTermManageFacadeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PaymentTermManageFacadeBlockingStub>() {
        @java.lang.Override
        public PaymentTermManageFacadeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PaymentTermManageFacadeBlockingStub(channel, callOptions);
        }
      };
    return PaymentTermManageFacadeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static PaymentTermManageFacadeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PaymentTermManageFacadeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PaymentTermManageFacadeFutureStub>() {
        @java.lang.Override
        public PaymentTermManageFacadeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PaymentTermManageFacadeFutureStub(channel, callOptions);
        }
      };
    return PaymentTermManageFacadeFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * circle入金银行卡管理
   * </pre>
   */
  public static abstract class PaymentTermManageFacadeImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     **
     *查询卡列表
     * </pre>
     */
    public void queryPaymentTermList(com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryPaymentTermListMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *查询卡详情
     * </pre>
     */
    public void queryPaymentTermDetail(com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryPaymentTermDetailMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *修改circle相关信息
     * </pre>
     */
    public void updatePaymentTerm(com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdatePaymentTermMethod(), responseObserver);
    }

    /**
     * <pre>
     **
     *查询circle支持的银行卡列表
     * </pre>
     */
    public void queryCircleSupportBankList(com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryCircleSupportBankListMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getQueryPaymentTermListMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest,
                com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse>(
                  this, METHODID_QUERY_PAYMENT_TERM_LIST)))
          .addMethod(
            getQueryPaymentTermDetailMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest,
                com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse>(
                  this, METHODID_QUERY_PAYMENT_TERM_DETAIL)))
          .addMethod(
            getUpdatePaymentTermMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest,
                com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse>(
                  this, METHODID_UPDATE_PAYMENT_TERM)))
          .addMethod(
            getQueryCircleSupportBankListMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest,
                com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse>(
                  this, METHODID_QUERY_CIRCLE_SUPPORT_BANK_LIST)))
          .build();
    }
  }

  /**
   * <pre>
   * circle入金银行卡管理
   * </pre>
   */
  public static final class PaymentTermManageFacadeStub extends io.grpc.stub.AbstractAsyncStub<PaymentTermManageFacadeStub> {
    private PaymentTermManageFacadeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PaymentTermManageFacadeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PaymentTermManageFacadeStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *查询卡列表
     * </pre>
     */
    public void queryPaymentTermList(com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryPaymentTermListMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *查询卡详情
     * </pre>
     */
    public void queryPaymentTermDetail(com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryPaymentTermDetailMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *修改circle相关信息
     * </pre>
     */
    public void updatePaymentTerm(com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdatePaymentTermMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     **
     *查询circle支持的银行卡列表
     * </pre>
     */
    public void queryCircleSupportBankList(com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest request,
        io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryCircleSupportBankListMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * <pre>
   * circle入金银行卡管理
   * </pre>
   */
  public static final class PaymentTermManageFacadeBlockingStub extends io.grpc.stub.AbstractBlockingStub<PaymentTermManageFacadeBlockingStub> {
    private PaymentTermManageFacadeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PaymentTermManageFacadeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PaymentTermManageFacadeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *查询卡列表
     * </pre>
     */
    public com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse queryPaymentTermList(com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryPaymentTermListMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *查询卡详情
     * </pre>
     */
    public com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse queryPaymentTermDetail(com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryPaymentTermDetailMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *修改circle相关信息
     * </pre>
     */
    public com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse updatePaymentTerm(com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdatePaymentTermMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     **
     *查询circle支持的银行卡列表
     * </pre>
     */
    public com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse queryCircleSupportBankList(com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryCircleSupportBankListMethod(), getCallOptions(), request);
    }
  }

  /**
   * <pre>
   * circle入金银行卡管理
   * </pre>
   */
  public static final class PaymentTermManageFacadeFutureStub extends io.grpc.stub.AbstractFutureStub<PaymentTermManageFacadeFutureStub> {
    private PaymentTermManageFacadeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PaymentTermManageFacadeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PaymentTermManageFacadeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     **
     *查询卡列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse> queryPaymentTermList(
        com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryPaymentTermListMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *查询卡详情
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse> queryPaymentTermDetail(
        com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryPaymentTermDetailMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *修改circle相关信息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse> updatePaymentTerm(
        com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdatePaymentTermMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     **
     *查询circle支持的银行卡列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse> queryCircleSupportBankList(
        com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryCircleSupportBankListMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_QUERY_PAYMENT_TERM_LIST = 0;
  private static final int METHODID_QUERY_PAYMENT_TERM_DETAIL = 1;
  private static final int METHODID_UPDATE_PAYMENT_TERM = 2;
  private static final int METHODID_QUERY_CIRCLE_SUPPORT_BANK_LIST = 3;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final PaymentTermManageFacadeImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(PaymentTermManageFacadeImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_QUERY_PAYMENT_TERM_LIST:
          serviceImpl.queryPaymentTermList((com.kikitrade.kcustomer.facade.QueryPaymentTermListRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse>) responseObserver);
          break;
        case METHODID_QUERY_PAYMENT_TERM_DETAIL:
          serviceImpl.queryPaymentTermDetail((com.kikitrade.kcustomer.facade.QueryPaymentTermDetailRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryPaymentTermDetailResponse>) responseObserver);
          break;
        case METHODID_UPDATE_PAYMENT_TERM:
          serviceImpl.updatePaymentTerm((com.kikitrade.kcustomer.facade.UpdatePaymentTermRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.UpdatePaymentTermResponse>) responseObserver);
          break;
        case METHODID_QUERY_CIRCLE_SUPPORT_BANK_LIST:
          serviceImpl.queryCircleSupportBankList((com.kikitrade.kcustomer.facade.QueryCircleSupportBankListRequest) request,
              (io.grpc.stub.StreamObserver<com.kikitrade.kcustomer.facade.QueryCircleSupportBankListResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class PaymentTermManageFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    PaymentTermManageFacadeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.kikitrade.kcustomer.facade.PaymentTermManaFacade.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("PaymentTermManageFacade");
    }
  }

  private static final class PaymentTermManageFacadeFileDescriptorSupplier
      extends PaymentTermManageFacadeBaseDescriptorSupplier {
    PaymentTermManageFacadeFileDescriptorSupplier() {}
  }

  private static final class PaymentTermManageFacadeMethodDescriptorSupplier
      extends PaymentTermManageFacadeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    PaymentTermManageFacadeMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (PaymentTermManageFacadeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new PaymentTermManageFacadeFileDescriptorSupplier())
              .addMethod(getQueryPaymentTermListMethod())
              .addMethod(getQueryPaymentTermDetailMethod())
              .addMethod(getUpdatePaymentTermMethod())
              .addMethod(getQueryCircleSupportBankListMethod())
              .build();
        }
      }
    }
    return result;
  }
}
