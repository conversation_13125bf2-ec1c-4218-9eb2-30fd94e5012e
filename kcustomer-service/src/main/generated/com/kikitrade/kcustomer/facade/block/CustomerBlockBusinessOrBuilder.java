// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerBlockBusinessFacade.proto

package com.kikitrade.kcustomer.facade.block;

public interface CustomerBlockBusinessOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.kcustomer.facade.block.CustomerBlockBusiness)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *用户id
   * </pre>
   *
   * <code>string customerId = 1;</code>
   * @return The customerId.
   */
  java.lang.String getCustomerId();
  /**
   * <pre>
   *用户id
   * </pre>
   *
   * <code>string customerId = 1;</code>
   * @return The bytes for customerId.
   */
  com.google.protobuf.ByteString
      getCustomerIdBytes();

  /**
   * <pre>
   *拦截业务
   * </pre>
   *
   * <code>string blockType = 2;</code>
   * @return The blockType.
   */
  java.lang.String getBlockType();
  /**
   * <pre>
   *拦截业务
   * </pre>
   *
   * <code>string blockType = 2;</code>
   * @return The bytes for blockType.
   */
  com.google.protobuf.ByteString
      getBlockTypeBytes();

  /**
   * <pre>
   *拦截原因
   * </pre>
   *
   * <code>string blockReason = 3;</code>
   * @return The blockReason.
   */
  java.lang.String getBlockReason();
  /**
   * <pre>
   *拦截原因
   * </pre>
   *
   * <code>string blockReason = 3;</code>
   * @return The bytes for blockReason.
   */
  com.google.protobuf.ByteString
      getBlockReasonBytes();

  /**
   * <pre>
   *拦截开始时间（系统当前时间）
   * </pre>
   *
   * <code>.google.protobuf.Timestamp blockStartTime = 4;</code>
   * @return Whether the blockStartTime field is set.
   */
  boolean hasBlockStartTime();
  /**
   * <pre>
   *拦截开始时间（系统当前时间）
   * </pre>
   *
   * <code>.google.protobuf.Timestamp blockStartTime = 4;</code>
   * @return The blockStartTime.
   */
  com.google.protobuf.Timestamp getBlockStartTime();
  /**
   * <pre>
   *拦截开始时间（系统当前时间）
   * </pre>
   *
   * <code>.google.protobuf.Timestamp blockStartTime = 4;</code>
   */
  com.google.protobuf.TimestampOrBuilder getBlockStartTimeOrBuilder();

  /**
   * <pre>
   *拦截截止时间
   * </pre>
   *
   * <code>.google.protobuf.Timestamp blockEndTime = 5;</code>
   * @return Whether the blockEndTime field is set.
   */
  boolean hasBlockEndTime();
  /**
   * <pre>
   *拦截截止时间
   * </pre>
   *
   * <code>.google.protobuf.Timestamp blockEndTime = 5;</code>
   * @return The blockEndTime.
   */
  com.google.protobuf.Timestamp getBlockEndTime();
  /**
   * <pre>
   *拦截截止时间
   * </pre>
   *
   * <code>.google.protobuf.Timestamp blockEndTime = 5;</code>
   */
  com.google.protobuf.TimestampOrBuilder getBlockEndTimeOrBuilder();

  /**
   * <pre>
   *操作人
   * </pre>
   *
   * <code>string operator = 6;</code>
   * @return The operator.
   */
  java.lang.String getOperator();
  /**
   * <pre>
   *操作人
   * </pre>
   *
   * <code>string operator = 6;</code>
   * @return The bytes for operator.
   */
  com.google.protobuf.ByteString
      getOperatorBytes();

  /**
   * <pre>
   *限频周期
   * </pre>
   *
   * <code>.com.kikitrade.kcustomer.facade.block.TimePeriod timePeriod = 7;</code>
   * @return The enum numeric value on the wire for timePeriod.
   */
  int getTimePeriodValue();
  /**
   * <pre>
   *限频周期
   * </pre>
   *
   * <code>.com.kikitrade.kcustomer.facade.block.TimePeriod timePeriod = 7;</code>
   * @return The timePeriod.
   */
  com.kikitrade.kcustomer.facade.block.TimePeriod getTimePeriod();

  /**
   * <pre>
   *限频周期最大访问次数
   * </pre>
   *
   * <code>int32 maxAccess = 8;</code>
   * @return The maxAccess.
   */
  int getMaxAccess();

  /**
   * <pre>
   * 决策结果
   * </pre>
   *
   * <code>.com.kikitrade.kcustomer.facade.block.FinalDecision decision = 9;</code>
   * @return The enum numeric value on the wire for decision.
   */
  int getDecisionValue();
  /**
   * <pre>
   * 决策结果
   * </pre>
   *
   * <code>.com.kikitrade.kcustomer.facade.block.FinalDecision decision = 9;</code>
   * @return The decision.
   */
  com.kikitrade.kcustomer.facade.block.FinalDecision getDecision();
}
