// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerBlockBusinessFacade.proto

package com.kikitrade.kcustomer.facade.block;

public interface UpdateCustomerBlockBusinessRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.kcustomer.facade.block.UpdateCustomerBlockBusinessRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *拦截业务
   * </pre>
   *
   * <code>.com.kikitrade.kcustomer.facade.block.CustomerBlockBusiness customerBlock = 3;</code>
   * @return Whether the customerBlock field is set.
   */
  boolean hasCustomerBlock();
  /**
   * <pre>
   *拦截业务
   * </pre>
   *
   * <code>.com.kikitrade.kcustomer.facade.block.CustomerBlockBusiness customerBlock = 3;</code>
   * @return The customerBlock.
   */
  com.kikitrade.kcustomer.facade.block.CustomerBlockBusiness getCustomerBlock();
  /**
   * <pre>
   *拦截业务
   * </pre>
   *
   * <code>.com.kikitrade.kcustomer.facade.block.CustomerBlockBusiness customerBlock = 3;</code>
   */
  com.kikitrade.kcustomer.facade.block.CustomerBlockBusinessOrBuilder getCustomerBlockOrBuilder();
}
