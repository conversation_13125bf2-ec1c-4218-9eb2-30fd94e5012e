// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerInfoFacade.proto

package com.kikitrade.kcustomer.facade.customer;

/**
 * Protobuf type {@code com.kikitrade.kcustomer.facade.CustomerInfoResponse}
 */
public final class CustomerInfoResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.kcustomer.facade.CustomerInfoResponse)
    CustomerInfoResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CustomerInfoResponse.newBuilder() to construct.
  private CustomerInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CustomerInfoResponse() {
    message_ = "";
    customer_ = java.util.Collections.emptyList();
  }

  @Override
  @SuppressWarnings({"unused"})
  protected Object newInstance(
      UnusedPrivateParameter unused) {
    return new CustomerInfoResponse();
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CustomerInfoResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            success_ = input.readBool();
            break;
          }
          case 18: {
            String s = input.readStringRequireUtf8();

            message_ = s;
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              customer_ = new java.util.ArrayList<CustomerInfoDTO>();
              mutable_bitField0_ |= 0x00000001;
            }
            customer_.add(
                input.readMessage(CustomerInfoDTO.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (com.google.protobuf.UninitializedMessageException e) {
      throw e.asInvalidProtocolBufferException().setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        customer_ = java.util.Collections.unmodifiableList(customer_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return CustomerInfoFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_CustomerInfoResponse_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return CustomerInfoFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_CustomerInfoResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            CustomerInfoResponse.class, Builder.class);
  }

  public static final int SUCCESS_FIELD_NUMBER = 1;
  private boolean success_;
  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  @Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 2;
  private volatile Object message_;
  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  @Override
  public String getMessage() {
    Object ref = message_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  @Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    Object ref = message_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CUSTOMER_FIELD_NUMBER = 3;
  private java.util.List<CustomerInfoDTO> customer_;
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
   */
  @Override
  public java.util.List<CustomerInfoDTO> getCustomerList() {
    return customer_;
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
   */
  @Override
  public java.util.List<? extends CustomerInfoDTOOrBuilder>
      getCustomerOrBuilderList() {
    return customer_;
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
   */
  @Override
  public int getCustomerCount() {
    return customer_.size();
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
   */
  @Override
  public CustomerInfoDTO getCustomer(int index) {
    return customer_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
   */
  @Override
  public CustomerInfoDTOOrBuilder getCustomerOrBuilder(
      int index) {
    return customer_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (success_ != false) {
      output.writeBool(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
    }
    for (int i = 0; i < customer_.size(); i++) {
      output.writeMessage(3, customer_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
    }
    for (int i = 0; i < customer_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, customer_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof CustomerInfoResponse)) {
      return super.equals(obj);
    }
    CustomerInfoResponse other = (CustomerInfoResponse) obj;

    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (!getCustomerList()
        .equals(other.getCustomerList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    if (getCustomerCount() > 0) {
      hash = (37 * hash) + CUSTOMER_FIELD_NUMBER;
      hash = (53 * hash) + getCustomerList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static CustomerInfoResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static CustomerInfoResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static CustomerInfoResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static CustomerInfoResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static CustomerInfoResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static CustomerInfoResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static CustomerInfoResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static CustomerInfoResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static CustomerInfoResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static CustomerInfoResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static CustomerInfoResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static CustomerInfoResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(CustomerInfoResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.kcustomer.facade.CustomerInfoResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.kcustomer.facade.CustomerInfoResponse)
      CustomerInfoResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return CustomerInfoFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_CustomerInfoResponse_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return CustomerInfoFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_CustomerInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              CustomerInfoResponse.class, Builder.class);
    }

    // Construct using com.kikitrade.kcustomer.facade.customer.CustomerInfoResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getCustomerFieldBuilder();
      }
    }
    @Override
    public Builder clear() {
      super.clear();
      success_ = false;

      message_ = "";

      if (customerBuilder_ == null) {
        customer_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        customerBuilder_.clear();
      }
      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return CustomerInfoFacadeOuterClass.internal_static_com_kikitrade_kcustomer_facade_CustomerInfoResponse_descriptor;
    }

    @Override
    public CustomerInfoResponse getDefaultInstanceForType() {
      return CustomerInfoResponse.getDefaultInstance();
    }

    @Override
    public CustomerInfoResponse build() {
      CustomerInfoResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public CustomerInfoResponse buildPartial() {
      CustomerInfoResponse result = new CustomerInfoResponse(this);
      int from_bitField0_ = bitField0_;
      result.success_ = success_;
      result.message_ = message_;
      if (customerBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          customer_ = java.util.Collections.unmodifiableList(customer_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.customer_ = customer_;
      } else {
        result.customer_ = customerBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @Override
    public Builder clone() {
      return super.clone();
    }
    @Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.setField(field, value);
    }
    @Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.addRepeatedField(field, value);
    }
    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof CustomerInfoResponse) {
        return mergeFrom((CustomerInfoResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(CustomerInfoResponse other) {
      if (other == CustomerInfoResponse.getDefaultInstance()) return this;
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        onChanged();
      }
      if (customerBuilder_ == null) {
        if (!other.customer_.isEmpty()) {
          if (customer_.isEmpty()) {
            customer_ = other.customer_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureCustomerIsMutable();
            customer_.addAll(other.customer_);
          }
          onChanged();
        }
      } else {
        if (!other.customer_.isEmpty()) {
          if (customerBuilder_.isEmpty()) {
            customerBuilder_.dispose();
            customerBuilder_ = null;
            customer_ = other.customer_;
            bitField0_ = (bitField0_ & ~0x00000001);
            customerBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getCustomerFieldBuilder() : null;
          } else {
            customerBuilder_.addAllMessages(other.customer_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      CustomerInfoResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (CustomerInfoResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private boolean success_ ;
    /**
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <code>bool success = 1;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {
      
      success_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool success = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      
      success_ = false;
      onChanged();
      return this;
    }

    private Object message_ = "";
    /**
     * <code>string message = 2;</code>
     * @return The message.
     */
    public String getMessage() {
      Object ref = message_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      message_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      
      message_ = getDefaultInstance().getMessage();
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      message_ = value;
      onChanged();
      return this;
    }

    private java.util.List<CustomerInfoDTO> customer_ =
      java.util.Collections.emptyList();
    private void ensureCustomerIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        customer_ = new java.util.ArrayList<CustomerInfoDTO>(customer_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        CustomerInfoDTO, CustomerInfoDTO.Builder, CustomerInfoDTOOrBuilder> customerBuilder_;

    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public java.util.List<CustomerInfoDTO> getCustomerList() {
      if (customerBuilder_ == null) {
        return java.util.Collections.unmodifiableList(customer_);
      } else {
        return customerBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public int getCustomerCount() {
      if (customerBuilder_ == null) {
        return customer_.size();
      } else {
        return customerBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public CustomerInfoDTO getCustomer(int index) {
      if (customerBuilder_ == null) {
        return customer_.get(index);
      } else {
        return customerBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder setCustomer(
        int index, CustomerInfoDTO value) {
      if (customerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCustomerIsMutable();
        customer_.set(index, value);
        onChanged();
      } else {
        customerBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder setCustomer(
        int index, CustomerInfoDTO.Builder builderForValue) {
      if (customerBuilder_ == null) {
        ensureCustomerIsMutable();
        customer_.set(index, builderForValue.build());
        onChanged();
      } else {
        customerBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder addCustomer(CustomerInfoDTO value) {
      if (customerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCustomerIsMutable();
        customer_.add(value);
        onChanged();
      } else {
        customerBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder addCustomer(
        int index, CustomerInfoDTO value) {
      if (customerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCustomerIsMutable();
        customer_.add(index, value);
        onChanged();
      } else {
        customerBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder addCustomer(
        CustomerInfoDTO.Builder builderForValue) {
      if (customerBuilder_ == null) {
        ensureCustomerIsMutable();
        customer_.add(builderForValue.build());
        onChanged();
      } else {
        customerBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder addCustomer(
        int index, CustomerInfoDTO.Builder builderForValue) {
      if (customerBuilder_ == null) {
        ensureCustomerIsMutable();
        customer_.add(index, builderForValue.build());
        onChanged();
      } else {
        customerBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder addAllCustomer(
        Iterable<? extends CustomerInfoDTO> values) {
      if (customerBuilder_ == null) {
        ensureCustomerIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, customer_);
        onChanged();
      } else {
        customerBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder clearCustomer() {
      if (customerBuilder_ == null) {
        customer_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        customerBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public Builder removeCustomer(int index) {
      if (customerBuilder_ == null) {
        ensureCustomerIsMutable();
        customer_.remove(index);
        onChanged();
      } else {
        customerBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public CustomerInfoDTO.Builder getCustomerBuilder(
        int index) {
      return getCustomerFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public CustomerInfoDTOOrBuilder getCustomerOrBuilder(
        int index) {
      if (customerBuilder_ == null) {
        return customer_.get(index);  } else {
        return customerBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public java.util.List<? extends CustomerInfoDTOOrBuilder>
         getCustomerOrBuilderList() {
      if (customerBuilder_ != null) {
        return customerBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(customer_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public CustomerInfoDTO.Builder addCustomerBuilder() {
      return getCustomerFieldBuilder().addBuilder(
          CustomerInfoDTO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public CustomerInfoDTO.Builder addCustomerBuilder(
        int index) {
      return getCustomerFieldBuilder().addBuilder(
          index, CustomerInfoDTO.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.CustomerInfoDTO customer = 3;</code>
     */
    public java.util.List<CustomerInfoDTO.Builder>
         getCustomerBuilderList() {
      return getCustomerFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        CustomerInfoDTO, CustomerInfoDTO.Builder, CustomerInfoDTOOrBuilder>
        getCustomerFieldBuilder() {
      if (customerBuilder_ == null) {
        customerBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            CustomerInfoDTO, CustomerInfoDTO.Builder, CustomerInfoDTOOrBuilder>(
                customer_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        customer_ = null;
      }
      return customerBuilder_;
    }
    @Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.kcustomer.facade.CustomerInfoResponse)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.kcustomer.facade.CustomerInfoResponse)
  private static final CustomerInfoResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new CustomerInfoResponse();
  }

  public static CustomerInfoResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CustomerInfoResponse>
      PARSER = new com.google.protobuf.AbstractParser<CustomerInfoResponse>() {
    @Override
    public CustomerInfoResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CustomerInfoResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CustomerInfoResponse> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<CustomerInfoResponse> getParserForType() {
    return PARSER;
  }

  @Override
  public CustomerInfoResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

