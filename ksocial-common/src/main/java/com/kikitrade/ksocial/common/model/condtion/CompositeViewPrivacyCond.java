package com.kikitrade.ksocial.common.model.condtion;

import java.io.Serializable;
import lombok.Data;

import java.math.BigInteger;
import java.util.List;

/**
 * @description:
 * @author: loopy
 * @create: 2024-09-20 17:02
 **/
@Data
public class CompositeViewPrivacyCond implements Serializable {
    private String type;

    private BigInteger operator;

    private String operatorName;

    private List<ViewPrivacyCond> activityCondList;

    private String conditionAddress;
}
