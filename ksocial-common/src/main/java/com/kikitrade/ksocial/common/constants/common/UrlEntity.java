package com.kikitrade.ksocial.common.constants.common;

import lombok.Data;

/**
 * link 链接相关
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/1/17 11:30 AM
 */
@Data
public class UrlEntity extends ActivityEntity {

    /**
     * 上传的文件http url
     */
    private String url;

    /**
     *  og or twitter title
     */
    private String title;
    /**
     * og or twitter description
     */
    private String description;
    /**
     *  og or twitter image
     */
    private String image;

    /**
     * <meta property="og:site_name" content="YouTube">
     */
    private String siteName;

    /**
     * <meta property="og:type" content="video.other">
     */
    private String type;

    /**
     * <meta property="og:article:author" content="作者名" />
     */
    private String articleAuthor;

}
