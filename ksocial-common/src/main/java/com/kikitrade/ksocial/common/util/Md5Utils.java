package com.kikitrade.ksocial.common.util;

import org.web3j.crypto.Hash;
import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Author: ZhangPengfei
 * @Date: 2023/6/6 7:52 PM
 */
public class Md5Utils {
    public static String contentMd5(String contentUri, String profile) {
        try {
            // 获取 MD5 消息摘要对象
            String content = profile + contentUri;
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 更新输入数据
            md.update(content.getBytes());
            // 完成哈希计算，得到摘要结果
            byte[] digest = md.digest();
            // 将摘要结果转换为十六进制字符串
            BigInteger bigInt = new BigInteger(1, digest);
            return bigInt.toString(16);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String hash(String tag) {
        try {
            String result = Numeric.toHexString(Hash.sha3(tag.getBytes(StandardCharsets.UTF_8)));
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
