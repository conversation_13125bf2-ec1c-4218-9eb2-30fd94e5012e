package org.opensocial.notify.opeanapi.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * DeviceSignResponseAllOf
 */

@JsonTypeName("DeviceSignResponse_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class DeviceSignResponseAllOf {

  @JsonProperty("data")
  private String data;

  public DeviceSignResponseAllOf data(String data) {
    this.data = data;
    return this;
  }

  /**
   * text to signs
   * @return data
  */
  
  @Schema(name = "data", description = "text to signs", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getData() {
    return data;
  }

  public void setData(String data) {
    this.data = data;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeviceSignResponseAllOf deviceSignResponseAllOf = (DeviceSignResponseAllOf) o;
    return Objects.equals(this.data, deviceSignResponseAllOf.data);
  }

  @Override
  public int hashCode() {
    return Objects.hash(data);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DeviceSignResponseAllOf {\n");
    sb.append("    data: ").append(toIndentedString(data)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

