package org.opensocial.notify.opeanapi.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * DeviceRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class DeviceRequest {

  @JsonProperty("signature")
  private String signature;

  @JsonProperty("platform")
  private String platform;

  @JsonProperty("token")
  private String token;

  public DeviceRequest signature(String signature) {
    this.signature = signature;
    return this;
  }

  /**
   * sign message by challenge text
   * @return signature
  */
  @NotNull 
  @Schema(name = "signature", description = "sign message by challenge text", requiredMode = Schema.RequiredMode.REQUIRED)
  public String getSignature() {
    return signature;
  }

  public void setSignature(String signature) {
    this.signature = signature;
  }

  public DeviceRequest platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * platform
   * @return platform
  */
  
  @Schema(name = "platform", description = "platform", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public DeviceRequest token(String token) {
    this.token = token;
    return this;
  }

  /**
   * token
   * @return token
  */
  
  @Schema(name = "token", description = "token", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeviceRequest deviceRequest = (DeviceRequest) o;
    return Objects.equals(this.signature, deviceRequest.signature) &&
        Objects.equals(this.platform, deviceRequest.platform) &&
        Objects.equals(this.token, deviceRequest.token);
  }

  @Override
  public int hashCode() {
    return Objects.hash(signature, platform, token);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DeviceRequest {\n");
    sb.append("    signature: ").append(toIndentedString(signature)).append("\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

