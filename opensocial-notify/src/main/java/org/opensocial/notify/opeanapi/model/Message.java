package org.opensocial.notify.opeanapi.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * messages info
 */

@Schema(name = "Message", description = "messages info")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Message {

  @JsonProperty("id")
  private String id;

  @JsonProperty("business_type")
  private String businessType;

  @JsonProperty("title")
  private String title;

  @JsonProperty("params")
  private String params;

  @JsonProperty("receiver")
  private String receiver;

  @JsonProperty("created")
  private Long created;

  public Message id(String id) {
    this.id = id;
    return this;
  }

  /**
   * 社区 id
   * @return id
  */
  
  @Schema(name = "id", description = "社区 id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public Message businessType(String businessType) {
    this.businessType = businessType;
    return this;
  }

  /**
   * trade|social|c2c
   * @return businessType
  */
  
  @Schema(name = "business_type", description = "trade|social|c2c", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getBusinessType() {
    return businessType;
  }

  public void setBusinessType(String businessType) {
    this.businessType = businessType;
  }

  public Message title(String title) {
    this.title = title;
    return this;
  }

  /**
   * title
   * @return title
  */
  
  @Schema(name = "title", description = "title", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public Message params(String params) {
    this.params = params;
    return this;
  }

  /**
   * app_id
   * @return params
  */
  
  @Schema(name = "params", description = "app_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getParams() {
    return params;
  }

  public void setParams(String params) {
    this.params = params;
  }

  public Message receiver(String receiver) {
    this.receiver = receiver;
    return this;
  }

  /**
   * address
   * @return receiver
  */
  
  @Schema(name = "receiver", description = "address", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public String getReceiver() {
    return receiver;
  }

  public void setReceiver(String receiver) {
    this.receiver = receiver;
  }

  public Message created(Long created) {
    this.created = created;
    return this;
  }

  /**
   * created time
   * @return created
  */
  
  @Schema(name = "created", description = "created time", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public Long getCreated() {
    return created;
  }

  public void setCreated(Long created) {
    this.created = created;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Message message = (Message) o;
    return Objects.equals(this.id, message.id) &&
        Objects.equals(this.businessType, message.businessType) &&
        Objects.equals(this.title, message.title) &&
        Objects.equals(this.params, message.params) &&
        Objects.equals(this.receiver, message.receiver) &&
        Objects.equals(this.created, message.created);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, businessType, title, params, receiver, created);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Message {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    businessType: ").append(toIndentedString(businessType)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    params: ").append(toIndentedString(params)).append("\n");
    sb.append("    receiver: ").append(toIndentedString(receiver)).append("\n");
    sb.append("    created: ").append(toIndentedString(created)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

