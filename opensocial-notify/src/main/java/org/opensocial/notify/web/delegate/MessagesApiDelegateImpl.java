package org.opensocial.notify.web.delegate;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.knotify.api.model.ListNotifyHistoryRequest;
import com.kikitrade.knotify.api.model.NotifyHistoryDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opensocial.common.exception.OspException;
import org.opensocial.common.exception.OspMessageEnum;
import org.opensocial.common.holder.AccessTokenHolder;
import org.opensocial.common.reference.DAppServiceReference;
import org.opensocial.common.utils.GlobalContextUtil;
import org.opensocial.common.utils.ResponseEntityUtils;
import org.opensocial.contract.api.model.AppResponse;
import org.opensocial.notify.opeanapi.controller.NotifyMessageApiDelegate;
import org.opensocial.notify.opeanapi.model.Message;
import org.opensocial.notify.opeanapi.model.MessagePagination;
import org.opensocial.notify.opeanapi.model.MessagePaginationResponse;
import org.opensocial.notify.reference.NotifyServiceReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessagesApiDelegateImpl implements NotifyMessageApiDelegate {

    @Autowired
    NotifyServiceReference notifyServiceReference;
    @Autowired
    private DAppServiceReference dAppServiceReference;

    @SneakyThrows
    @Override
    public ResponseEntity<MessagePaginationResponse> listMessage(String businessType,
                                                                 Integer limit,
                                                                 String nextToken) {
        AppResponse response = dAppServiceReference.getAppDataByCache(GlobalContextUtil.getGlobalAppId());
        String eoaAddress = AccessTokenHolder.getDefault().getEoaAddress();
        if (StringUtils.isNotBlank(eoaAddress)) {
            throw new OspException(OspMessageEnum.LOGIN_REQUIRED);
        }
        ListNotifyHistoryRequest listNotifyHistoryRequest = ListNotifyHistoryRequest.builder().appId(response.getAppId()).eoaAddress(eoaAddress).limit(limit).nextToken(nextToken).businessType(businessType).build();
        TokenPage<NotifyHistoryDTO> historyResponseTokenPage = notifyServiceReference.listNotify(listNotifyHistoryRequest);
        return ResponseEntityUtils.ok(toApiMessagesPaginationResponse(historyResponseTokenPage, limit));
    }

    public static MessagePaginationResponse toApiMessagesPaginationResponse(TokenPage list, Integer limit) {
        MessagePagination messagesPage = new MessagePagination();
        messagesPage.setLimit(limit);
        messagesPage.setNextToken(list.getNextToken());
        messagesPage.setRows(toApiMessagesList(list.getRows()));
        messagesPage.setTotal((int) list.getTotalCount());
        MessagePaginationResponse messagesPaginationResponse = new MessagePaginationResponse();
        messagesPaginationResponse.data(messagesPage);
        return messagesPaginationResponse;
    }

    public static List<Message> toApiMessagesList(List<NotifyHistoryDTO> rows) {
        return rows.stream().map(r -> toApiMessages(r)).collect(Collectors.toList());
    }

    public static Message toApiMessages(NotifyHistoryDTO response) {
        if (response == null) {
            return null;
        }
        Message messages = new Message();
        BeanUtil.copyProperties(response, messages);
        return messages;
    }
}
