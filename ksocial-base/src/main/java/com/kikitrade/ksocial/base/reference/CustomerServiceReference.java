package com.kikitrade.ksocial.base.reference;

import com.alibaba.fastjson.JSON;
import com.kikitrade.kcustomer.api.model.*;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/2/14
 * @desc
 **/
@Slf4j
@Service
@ConditionalOnProperty(prefix = "social.base", name = "use-customer-service", havingValue = "true")
public class CustomerServiceReference {
    @DubboReference(check = false, timeout = 5000)
    RemoteCustomerService remoteCustomerService;


    @DubboReference(check = false, timeout = 5000)
    private RemoteNotificationService remoteNotificationService;

    /**
     * 获取用户信息
     */
    public CustomerDTO getCustomerById(String saasId, String customerId) {
        try {
            CustomerDTO customer = remoteCustomerService.getById(saasId, customerId);
            log.info("getCustomerById customerId:{},result:{}", customerId, JSON.toJSONString(customer));
            return customer;
        } catch (Exception e) {
            log.error("getCustomerById customerId:{},error", customerId, e);
            return null;
        }
    }


    public boolean send(FirebaseMessageDTO messageDTO) {
        try {
            return remoteNotificationService.send(messageDTO);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean send(PushMessageDTO pushMessageDTO) {
        try {
            return remoteNotificationService.send(pushMessageDTO);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean send(DingTalkMessageDTO messageDTO) {
        try {
            return remoteNotificationService.send(messageDTO);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean send(EmailNotificationDTO emailMessageDTO) {
        try {
            return remoteNotificationService.send(emailMessageDTO);
        } catch (Exception e) {
            return false;
        }
    }
}
