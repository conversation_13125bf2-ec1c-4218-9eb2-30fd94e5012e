package com.kikitrade.ksocial.base.kol;

import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.ksocial.dal.model.CustomerKolDO;

import java.util.List;

/**
 * @author: penuel
 * @date: 2023/1/4 17:54
 * @desc: 社区kol基础服务
 */
public interface IKolBaseService {


    TokenPage<CustomerKolDO> listBySort(String saasId, String nextToken, int limit, String kolSort, String zone, List<String> userIds, List<String> filterUserIds);

    Page<CustomerKolDO> listByOffset(String saasId, Integer offset, int limit, String kolSort, String zone, List<String> userIds, List<String> filterUserIds);

    boolean upsert(CustomerKolDO customerKolDO);

    CustomerKolDO getByCustomerId(String customerId, String zone);

}
