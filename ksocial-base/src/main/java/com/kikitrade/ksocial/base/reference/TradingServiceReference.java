package com.kikitrade.ksocial.base.reference;

import com.kikitrade.ksocial.common.util.DateUtil;
import com.kikitrade.ksocial.dal.model.PointDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: ZhangPengfei
 * @Date: 2022/6/13 11:46 上午
 */

@Slf4j
@Service
public class TradingServiceReference {

//    @DubboReference(check = false, timeout = 5000)
//    RemoteTradingShareService remoteTradingShareService;

    public List<PointDO> getTradePointsByRemoteTrade(String saasId, String loginCustomerId, String baseCurrency, String quoteCurrency, Long startTime, Long endTime) {
        String from = DateUtil.dateMillisToString(startTime);
        String to = DateUtil.dateMillisToString(endTime);
        /*
        TradePointShareRequest tradRequest = new TradePointShareRequest(from, to, symbol, loginCustomerId);
        TradePointShareResponse tradeResponse = null;
        log.info("remoteTradingShareService tradePointShare request:{}", JSON.toJSONString(tradRequest));
        try {
            tradeResponse = remoteTradingShareService.tradePointShare(tradRequest);
        } catch (Exception e) {
            log.error("remoteTradingShareService.tradePointShare fail request:{}", JSON.toJSONString(tradRequest), e);
        }

        log.info("remoteTradingShareService tradePointShare customerId:{},response:{}", loginCustomerId, JSON.toJSONString(tradeResponse));
        if (null == tradeResponse || tradeResponse.getTradePointShareDTO() == null) {
            return Collections.emptyList();
        }
        List<PointDO> pointDTOS = new ArrayList<>();

        Map<TradingConstant.OrderSide, List<TradePointDTO>> tradeMap = tradeResponse.getTradePointShareDTO().getTradePointDTOMap();
        if (null != tradeMap && tradeMap.size() > 0) {
            List<TradePointDTO> buyPoints = tradeMap.get(TradingConstant.OrderSide.buy);
            if (null != buyPoints && buyPoints.size() > 0) {
                List<PointDO> buyPointDOS = buyPoints.stream().map(item -> {
                    PointDO pointDO = new PointDO();
                    pointDO.setUserId(loginCustomerId);
                    pointDO.setTime(DateUtil.timeToMillis(item.getDay()));
                    pointDO.setQuoteCurrency(quoteCurrency);
                    pointDO.setBaseCurrency(baseCurrency);
                    pointDO.setSaasId(saasId);
                    pointDO.setType(CustomerConstants.ViewType.BUY.getCode());
                    pointDO.setValue(item.getAvgTradePrice().toString());
                    pointDO.setId(pointDO.getId());
                    pointDO.setCreated(System.currentTimeMillis());
                    pointDO.setModified(System.currentTimeMillis());
                    return pointDO;
                }).collect(Collectors.toList());
                pointDTOS.addAll(buyPointDOS);
            }
            List<TradePointDTO> sellPoints = tradeMap.get(TradingConstant.OrderSide.sell);
            if (null != sellPoints && sellPoints.size() > 0) {
                List<PointDO> sellPointDOS = sellPoints.stream().map(item -> {
                    PointDO pointDO = new PointDO();
                    pointDO.setUserId(loginCustomerId);
                    pointDO.setTime(DateUtil.timeToMillis(item.getDay()));
                    pointDO.setQuoteCurrency(quoteCurrency);
                    pointDO.setBaseCurrency(baseCurrency);
                    pointDO.setSaasId(saasId);
                    pointDO.setType(CustomerConstants.ViewType.SELL.getCode());
                    pointDO.setValue(item.getAvgTradePrice().toString());
                    pointDO.setId(pointDO.getId());
                    pointDO.setCreated(System.currentTimeMillis());
                    pointDO.setModified(System.currentTimeMillis());
                    return pointDO;
                }).collect(Collectors.toList());
                pointDTOS.addAll(sellPointDOS);
            }
        }
        return pointDTOS;
         */
        return null;
    }

}
