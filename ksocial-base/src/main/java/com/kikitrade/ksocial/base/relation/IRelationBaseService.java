package com.kikitrade.ksocial.base.relation;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.ksocial.common.constants.relation.RelationEnum;
import com.kikitrade.ksocial.dal.model.UserRelationDO;
import com.kikitrade.ksocial.dal.model.param.RelationSearchParam;

import java.util.List;
import java.util.Map;

/**
 * @author: penuel
 * @date: 2023/1/4 21:00
 * @desc: 用户关系基础服务
 */
public interface IRelationBaseService {
    TokenPage<UserRelationDO> getReverseRelation(String userId, RelationEnum relation, String nextFollowerId, List<String> filterIncludeFollowerIds, int limit, List<String> excludeUserIds, boolean onlyChain);

    TokenPage<UserRelationDO> getForwardRelation(String userId, RelationEnum relation, String nextFollowedId, List<String> filterIncludeFollowedIds, int limit, List<String> excludeUserIds, boolean onlyChain);

    UserRelationDO addRelation(UserRelationDO relationDO);

    UserRelationDO getRelation(String userId, String targetId, RelationEnum relation);

    /**
     * 修改关系的tokenIds
     *
     * @param userRelationDO
     * @return
     */
    UserRelationDO updateRelation(UserRelationDO userRelationDO);

    UserRelationDO updateColumn(UserRelationDO userRelationDO, List<String> columnList);

    UserRelationDO removeRelation(String userId, String targetId, RelationEnum byRelation);

    List<UserRelationDO> batchAddRelation(List<UserRelationDO> userRelationDOS);

    Map<String, List<UserRelationDO>> getRelationMap(String operator, List<String> userIds, List<RelationEnum> withRelations);

    List<UserRelationDO> batchGetRelation(String operator, List<String> targetIds, List<RelationEnum> relationEnums, boolean reverse);


    List<UserRelationDO> batchGetRelation(Map<String,String> userRelationKeyMap,  List<RelationEnum> relationEnums);

    UserRelationDO getByTargetIdAndTokenId(String targetId, String tokenId, RelationEnum relation);

    /**
     * 调用需注意，无缓存 并且没有排序
     *
     * @param userId
     * @param relationEnum
     * @param reverse
     * @return
     */
    TokenPage<UserRelationDO> listRelations(String userId, RelationEnum relationEnum, boolean reverse, String nextToken);

    /**
     * 多元索引查询
     * @return
     */
    TokenPage<UserRelationDO> searchRelations(RelationSearchParam relationSearchParam);
}
