package com.kikitrade.ksocial.base.user;

import com.kikitrade.ksocial.base.common.IBaseService;
import com.kikitrade.ksocial.dal.model.UserAuthorityDO;

import java.util.List;


public interface IUserAuthorityBaseService extends IBaseService<UserAuthorityDO> {

    UserAuthorityDO update(UserAuthorityDO userAuthorityDO);

    UserAuthorityDO get(String ospAddress,String eoaAddress);

    List<UserAuthorityDO> getByOspAddress(String ospAddress);

    List<UserAuthorityDO> getByEoAddress(String eoaAddress);
}
