## language: zh-CN
#功能: customize-reward
#
#  自定义发奖包括：自动发奖，手动发奖，半自动发奖
#
#  场景大纲: 手动发奖-成功-一条发奖失败记录，手动发奖成功
#    假如customize-reward-创建活动<activity_entity>
#    假如customize-reward-创建任务批次<activity_batch>
#    假如customize-reward-配置测试数据activityCustomReward<reward>
#    当customize-reward-系统开始审核<audit>
#    那么customize-reward-校验activityBatchList为<activityBatchList>
#    那么customize-reward-校验activityCustomRewardList为<activityCustomRewardList>
#    例子:
#      | activity_batch                                                       | audit   | activity_entity                                                                                         | reward                                                                                                                                                                                                         | activityBatchList | activityCustomRewardList |
#      | {"id":"2022022116483070600200","name":"test","rewardConfig":"token"} | APPROVE | {"id":"2022022116483070600200","name":"customize-reward","saasId":"kiki","status":1,"type":"CUSTOMIZE"} | {"amount":"65.00","businessId":"2023010615003602181","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"SIGN_IN:2023010615003602181","status":"AWARD_FAILED","userType":"Normal"} | {}                | {}                       |
#
#  场景大纲: 手动发奖-成功-两条条发奖失败记录，手动发奖成功
#    假如customize-reward-创建活动<activity_entity>
#    假如customize-reward-创建任务批次<activity_batch>
#    假如customize-reward-配置测试数据activityCustomReward<reward>
#    假如customize-reward-配置测试数据activityCustomReward<reward2>
#    当customize-reward-系统开始审核<audit>
#
#    例子:
#      | activity_batch                                                       | audit   | activity_entity                                                                                         | reward                                                                                                                                                                                                         | reward2                                                                                                                                                                                                        |
#      | {"id":"2022022116483070600200","name":"test","rewardConfig":"token"} | APPROVE | {"id":"2022022116483070600200","name":"customize-reward","saasId":"kiki","status":1,"type":"CUSTOMIZE"} | {"amount":"65.00","businessId":"2023010615003602181","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"SIGN_IN:2023010615003602181","status":"AWARD_FAILED","userType":"Normal"} | {"amount":"65.00","businessId":"2023010615003602182","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"SIGN_IN:2023010615003602181","status":"AWARD_FAILED","userType":"Normal"} |

#  场景大纲: 半自动发奖-成功-一条发奖失败记录，手动发奖成功
#    假如customize-reward-创建活动<activity_entity>
#    假如customize-reward-创建任务批次<activity_batch>
#    假如customize-reward-配置测试数据activityCustomReward<reward>
#    当customize-reward-系统开始审核<audit>
#    那么customize-reward-校验activityBatchList为<activityBatchList>
#    那么customize-reward-校验activityCustomRewardList为<activityCustomRewardList>
#    例子:
#      | activity_batch                                                       | audit   | activity_entity                                                                                         | reward                                                                                                                                                                                                         | activityBatchList | activityCustomRewardList |
#      | {"id":"2022022116483070600200","name":"test","rewardConfig":"token"} | APPROVE | {"id":"2022022116483070600200","name":"customize-reward","saasId":"kiki","status":1,"type":"CUSTOMIZE"} | {"amount":"65.00","businessId":"2023010615003602181","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"SIGN_IN:2023010615003602181","status":"AWARD_FAILED","userType":"Normal"} | {}                | {}                       |


#  场景大纲: 自动发奖-成功-发奖成功
#    假如customize-reward-创建活动<activity_entity>
#    假如customize-reward-创建任务批次<activity_batch>
#    假如customize-reward-配置测试数据activityCustomReward<reward>
#    当customize-reward-执行自动发奖任务
#    那么customize-reward-校验activityBatchList为<activityBatchList>
#    那么customize-reward-校验activityCustomRewardList为<activityCustomRewardList>
#    例子:
#      | activity_batch                                                       | audit   | activity_entity                                                                                         | reward                                                                                                                                                                                                         | activityBatchList | activityCustomRewardList |
#      | {"id":"2022022116483070600200","name":"test","rewardConfig":"token"} | APPROVE | {"id":"2022022116483070600200","name":"customize-reward","saasId":"kiki","status":1,"type":"CUSTOMIZE"} | {"amount":"65.00","businessId":"2023010615003602181","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"SIGN_IN:2023010615003602181","status":"AWARD_FAILED","userType":"Normal"} | {}                | {}                       |



