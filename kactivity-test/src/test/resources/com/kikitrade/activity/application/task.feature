# language: zh-CN
功能: task

  背景:
    假如task-初始化任务code配置TaskCodeConfig
    同时task-设置任务任务白名单用户

    # 立即发布,任务未开始,白名单&非白名单用户均不可见
  场景大纲: taskList立即发布
    假如task-初始化任务配置task_config<taskConfig>
    同时task-设置任务<taskId>开始时间<start>结束时间<end>
    当task-获取taskList<taskListRequest>
    那么task-校验TaskListResponse<taskListResponses>
    例子:
      | taskId | start | end | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | taskListRequest                                                                                                           | taskListResponses |
  # 配置任务,4天后开始,白名单任务不可见
      | 400062 | 4     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016061","vipLevel":null} | []                |
  # 配置任务,4天后开始,非白名单任务不可见
      | 400062 | 4     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016062","vipLevel":null} | []                |
  # 配置任务,5天后开始,白名单任务不可见
      | 400062 | 5     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016061","vipLevel":null} | []                |
  # 配置任务,5天后开始,非白名单任务不可见
      | 400062 | 5     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016062","vipLevel":null} | []                |
  # 配置任务,6天后开始,白名单任务不可见
      | 400062 | 6     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016061","vipLevel":null} | []                |
  # 配置任务,6天后开始,非白名单任务不可见
      | 400062 | 6     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016062","vipLevel":null} | []                |

  # 灰度发布,白名单任务依据v1字段
  场景大纲: taskList灰度发布
    假如task-初始化任务配置task_config<taskConfig>
    同时task-设置任务<taskId>开始时间<start>结束时间<end>
    同时task-设置灰度任务<taskId>开始时间<grayStart>结束时间<grayEnd>
    当task-获取taskList<taskListRequest>
    那么task-校验TaskListResponse<taskListResponses>
    例子:
      | taskId | start | end | grayStart | grayEnd | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | taskListRequest                                                                                                           | taskListResponses                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
  # 配置任务,4天后开始,白名单可提前5天查看任务
      | 400062 | 4     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016061","vipLevel":null} | [{"btn":2,"code":"eoa_asset_persona_eth","cycle":"20250325","cycleEnum":"daily","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","domain":"wallet","endTime":1743735903842,"image":{},"limit":1,"link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"order":34,"postTaskStatus":0,"progress":0,"progressType":"add","rewardFrequency":1,"showProgress":false,"showReward":0,"status":0,"taskId":"400062","title":"Hold a Persona NFT on Ethereum","todayTaskStatus":0,"url":"https://magiceden.io/collections/ethereum/******************************************"}] |
  # 配置任务,4天后开始,非白名单任务不可见
      | 400062 | 4     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016062","vipLevel":null} | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
  # 配置任务,5天后开始,白名单可提前6天查看任务
      | 400062 | 5     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016061","vipLevel":null} | [{"btn":2,"code":"eoa_asset_persona_eth","cycle":"20250325","cycleEnum":"daily","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","domain":"wallet","endTime":1743735904082,"image":{},"limit":1,"link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"order":34,"postTaskStatus":0,"progress":0,"progressType":"add","rewardFrequency":1,"showProgress":false,"showReward":0,"status":0,"taskId":"400062","title":"Hold a Persona NFT on Ethereum","todayTaskStatus":0,"url":"https://magiceden.io/collections/ethereum/******************************************"}] |
  # 配置任务,5天后开始,非白名单任务不可见
      | 400062 | 5     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016062","vipLevel":null} | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
  # 配置任务,6天后开始,白名单可提前5天查看任务,当前任务不可见
      | 400062 | 6     | 10  | 1         | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016061","vipLevel":null} | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
  # 配置任务,6天后开始,非白名单任务不可见
      | 400062 | 6     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | {"saasId":"monster","channel":null,"position":null,"clientType":"","customerId":"2024071906435660016062","vipLevel":null} | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |

  场景大纲:taskEarlyBirdPop,立即发布
    假如task-初始化任务配置task_config<taskConfig>
    同时task-设置任务<taskId>开始时间<start>结束时间<end>
    同时task-设置灰度任务<taskId>开始时间<grayStart>结束时间<grayEnd>
    当task-获取taskEarlyBirdPop<taskPopRequest>
    那么task-校验TaskPopResponseList<taskPopResponseList>
    例子:
      | taskId | start | end | grayStart | grayEnd | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | taskPopRequest                                             | taskPopResponseList |
    # 配置任务,4天后开始,白名单任务不可见,不弹窗
      | 400053 | 4     | 10  | -1        | 10      | [{"taskId":"400053","groupId":"400053","isGroup":true,"showList":false,"title":"Weekly Badge","desc":"Weekly Badge","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"code":"early_bird","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"none","provideType":"auto","order":33,"domain":"twitter","vipLevel":"+0","btn":0,"showProgress":false,"channel":"app"},{"taskId":"300019","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.6","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"ledgerTitle":"Register by Sep.6","code":"subject_post_x","showCode":"early_bird_1","url":"https://twitter.com/compose/post?text=Wow!%20What%20an%20airdrOwOp!%20%F0%9F%98%B3%20Season%202%20is%20over%20but%20Season%203%20just%20got%20started%20on%20the%20%40SoMon_OwO%20app!%0A%0APsst...%20I%20heard%20that%20SoMon%20users%20%26%20%24OwO%20HODLers%20will%20get%20MORE%20of%20the%20future%20%40OpenSocialLabs%20token%3F%20Might%20not%20want%20to%20miss%20that%20%F0%9F%91%80%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"36","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"36","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":1,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"HODLers","register-time":"20240906"}},{"taskId":"300020","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.13","ledgerTitle":"Register by Sep.13","saasId":"monster","status":"ACTIVE","startTime":1726185600000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_2","url":"https://twitter.com/compose/post?text=Half%20way%20through%20the%20month%20on%20the%20%40SoMon_OwO%20app!%0A%0ASecure%20as%20many%20Points%20as%20possible%20with%20me%20and%20climb%20the%20leaderboard%20with%20Ultimate%20OwO%20POWER!!!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"37","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"37","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":2,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"through","register-time":"20240913"}},{"taskId":"300021","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.20","ledgerTitle":"Register by Sep.20","saasId":"monster","status":"ACTIVE","startTime":1726790400000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_3","url":"https://twitter.com/compose/post?text=Did%20you%20see%3F%20Tribes%20on%20%40SoMon_OwO%20are%20getting%20MAJOR%20upgrades!%0A%0AMore%20ways%20for%20Tribe%20owners%20to%20make%20some%20%24%24%24OwO%24%24%24!%0A%0ALet's%20goOwOo!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"38","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"38","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":3,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"MAJOR","register-time":"20240920"}},{"taskId":"300022","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.27","ledgerTitle":"Register by Sep.27","saasId":"monster","status":"ACTIVE","startTime":1727395200000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_4","url":"https://twitter.com/compose/post?text=%40SoMon_OWO%20Season%203%20is%20about%20to%20end%2C%20airdrOwOp%20imminent!!!%0A%0ALast%20chance%20to%20increase%20your%20rOwOrds!%0A%0AFight%20lil'Mons!!!%20(%E0%B9%91OwO)%D9%88%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"39","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"39","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":4,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"chance","register-time":"20240927"}}] | {"customerId":"2024071906435660016061","saasId":"monster"} | [{"pop":false}]     |
    # 配置任务,4天后开始,非白名单任务不可见,不弹窗
      | 400053 | 4     | 10  | -1        | 10      | [{"taskId":"400053","groupId":"400053","isGroup":true,"showList":false,"title":"Weekly Badge","desc":"Weekly Badge","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"code":"early_bird","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"none","provideType":"auto","order":33,"domain":"twitter","vipLevel":"+0","btn":0,"showProgress":false,"channel":"app"},{"taskId":"300019","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.6","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"ledgerTitle":"Register by Sep.6","code":"subject_post_x","showCode":"early_bird_1","url":"https://twitter.com/compose/post?text=Wow!%20What%20an%20airdrOwOp!%20%F0%9F%98%B3%20Season%202%20is%20over%20but%20Season%203%20just%20got%20started%20on%20the%20%40SoMon_OwO%20app!%0A%0APsst...%20I%20heard%20that%20SoMon%20users%20%26%20%24OwO%20HODLers%20will%20get%20MORE%20of%20the%20future%20%40OpenSocialLabs%20token%3F%20Might%20not%20want%20to%20miss%20that%20%F0%9F%91%80%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"36","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"36","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":1,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"HODLers","register-time":"20240906"}},{"taskId":"300020","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.13","ledgerTitle":"Register by Sep.13","saasId":"monster","status":"ACTIVE","startTime":1726185600000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_2","url":"https://twitter.com/compose/post?text=Half%20way%20through%20the%20month%20on%20the%20%40SoMon_OwO%20app!%0A%0ASecure%20as%20many%20Points%20as%20possible%20with%20me%20and%20climb%20the%20leaderboard%20with%20Ultimate%20OwO%20POWER!!!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"37","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"37","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":2,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"through","register-time":"20240913"}},{"taskId":"300021","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.20","ledgerTitle":"Register by Sep.20","saasId":"monster","status":"ACTIVE","startTime":1726790400000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_3","url":"https://twitter.com/compose/post?text=Did%20you%20see%3F%20Tribes%20on%20%40SoMon_OwO%20are%20getting%20MAJOR%20upgrades!%0A%0AMore%20ways%20for%20Tribe%20owners%20to%20make%20some%20%24%24%24OwO%24%24%24!%0A%0ALet's%20goOwOo!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"38","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"38","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":3,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"MAJOR","register-time":"20240920"}},{"taskId":"300022","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.27","ledgerTitle":"Register by Sep.27","saasId":"monster","status":"ACTIVE","startTime":1727395200000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_4","url":"https://twitter.com/compose/post?text=%40SoMon_OWO%20Season%203%20is%20about%20to%20end%2C%20airdrOwOp%20imminent!!!%0A%0ALast%20chance%20to%20increase%20your%20rOwOrds!%0A%0AFight%20lil'Mons!!!%20(%E0%B9%91OwO)%D9%88%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"39","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"39","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":4,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"chance","register-time":"20240927"}}] | {"customerId":"2024071906435660016062","saasId":"monster"} | [{"pop":false}]     |
    # 配置任务,6天后开始,白名单任务不可见,不弹窗
      | 400053 | 6     | 10  | 1         | 10      | [{"taskId":"400053","groupId":"400053","isGroup":true,"showList":false,"title":"Weekly Badge","desc":"Weekly Badge","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"code":"early_bird","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"none","provideType":"auto","order":33,"domain":"twitter","vipLevel":"+0","btn":0,"showProgress":false,"channel":"app"},{"taskId":"300019","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.6","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"ledgerTitle":"Register by Sep.6","code":"subject_post_x","showCode":"early_bird_1","url":"https://twitter.com/compose/post?text=Wow!%20What%20an%20airdrOwOp!%20%F0%9F%98%B3%20Season%202%20is%20over%20but%20Season%203%20just%20got%20started%20on%20the%20%40SoMon_OwO%20app!%0A%0APsst...%20I%20heard%20that%20SoMon%20users%20%26%20%24OwO%20HODLers%20will%20get%20MORE%20of%20the%20future%20%40OpenSocialLabs%20token%3F%20Might%20not%20want%20to%20miss%20that%20%F0%9F%91%80%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"36","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"36","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":1,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"HODLers","register-time":"20240906"}},{"taskId":"300020","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.13","ledgerTitle":"Register by Sep.13","saasId":"monster","status":"ACTIVE","startTime":1726185600000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_2","url":"https://twitter.com/compose/post?text=Half%20way%20through%20the%20month%20on%20the%20%40SoMon_OwO%20app!%0A%0ASecure%20as%20many%20Points%20as%20possible%20with%20me%20and%20climb%20the%20leaderboard%20with%20Ultimate%20OwO%20POWER!!!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"37","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"37","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":2,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"through","register-time":"20240913"}},{"taskId":"300021","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.20","ledgerTitle":"Register by Sep.20","saasId":"monster","status":"ACTIVE","startTime":1726790400000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_3","url":"https://twitter.com/compose/post?text=Did%20you%20see%3F%20Tribes%20on%20%40SoMon_OwO%20are%20getting%20MAJOR%20upgrades!%0A%0AMore%20ways%20for%20Tribe%20owners%20to%20make%20some%20%24%24%24OwO%24%24%24!%0A%0ALet's%20goOwOo!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"38","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"38","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":3,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"MAJOR","register-time":"20240920"}},{"taskId":"300022","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.27","ledgerTitle":"Register by Sep.27","saasId":"monster","status":"ACTIVE","startTime":1727395200000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_4","url":"https://twitter.com/compose/post?text=%40SoMon_OWO%20Season%203%20is%20about%20to%20end%2C%20airdrOwOp%20imminent!!!%0A%0ALast%20chance%20to%20increase%20your%20rOwOrds!%0A%0AFight%20lil'Mons!!!%20(%E0%B9%91OwO)%D9%88%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"39","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"39","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":4,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"chance","register-time":"20240927"}}] | {"customerId":"2024071906435660016061","saasId":"monster"} | [{"pop":false}]     |
    # 配置任务,6天后开始,非白名单任务不可见,不弹窗
      | 400053 | 6     | 10  | -1        | 10      | [{"taskId":"400053","groupId":"400053","isGroup":true,"showList":false,"title":"Weekly Badge","desc":"Weekly Badge","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"code":"early_bird","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"none","provideType":"auto","order":33,"domain":"twitter","vipLevel":"+0","btn":0,"showProgress":false,"channel":"app"},{"taskId":"300019","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.6","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"ledgerTitle":"Register by Sep.6","code":"subject_post_x","showCode":"early_bird_1","url":"https://twitter.com/compose/post?text=Wow!%20What%20an%20airdrOwOp!%20%F0%9F%98%B3%20Season%202%20is%20over%20but%20Season%203%20just%20got%20started%20on%20the%20%40SoMon_OwO%20app!%0A%0APsst...%20I%20heard%20that%20SoMon%20users%20%26%20%24OwO%20HODLers%20will%20get%20MORE%20of%20the%20future%20%40OpenSocialLabs%20token%3F%20Might%20not%20want%20to%20miss%20that%20%F0%9F%91%80%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"36","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"36","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":1,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"HODLers","register-time":"20240906"}},{"taskId":"300020","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.13","ledgerTitle":"Register by Sep.13","saasId":"monster","status":"ACTIVE","startTime":1726185600000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_2","url":"https://twitter.com/compose/post?text=Half%20way%20through%20the%20month%20on%20the%20%40SoMon_OwO%20app!%0A%0ASecure%20as%20many%20Points%20as%20possible%20with%20me%20and%20climb%20the%20leaderboard%20with%20Ultimate%20OwO%20POWER!!!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"37","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"37","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":2,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"through","register-time":"20240913"}},{"taskId":"300021","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.20","ledgerTitle":"Register by Sep.20","saasId":"monster","status":"ACTIVE","startTime":1726790400000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_3","url":"https://twitter.com/compose/post?text=Did%20you%20see%3F%20Tribes%20on%20%40SoMon_OwO%20are%20getting%20MAJOR%20upgrades!%0A%0AMore%20ways%20for%20Tribe%20owners%20to%20make%20some%20%24%24%24OwO%24%24%24!%0A%0ALet's%20goOwOo!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"38","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"38","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":3,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"MAJOR","register-time":"20240920"}},{"taskId":"300022","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.27","ledgerTitle":"Register by Sep.27","saasId":"monster","status":"ACTIVE","startTime":1727395200000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_4","url":"https://twitter.com/compose/post?text=%40SoMon_OWO%20Season%203%20is%20about%20to%20end%2C%20airdrOwOp%20imminent!!!%0A%0ALast%20chance%20to%20increase%20your%20rOwOrds!%0A%0AFight%20lil'Mons!!!%20(%E0%B9%91OwO)%D9%88%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"39","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"39","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":4,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"chance","register-time":"20240927"}}] | {"customerId":"2024071906435660016062","saasId":"monster"} | [{"pop":false}]     |


  场景大纲:taskDetail
    假如task-初始化任务配置task_config<taskConfig>
    同时task-设置任务<taskId>开始时间<start>结束时间<end>
    当task-获取taskDetail<taskDetailRequest>
    那么task-校验TaskDetailResponse<taskDetailResponse>

    例子:
      | taskId | start | end | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | taskDetailRequest                                         | taskDetailResponse |
    # 配置任务,4天后开始,白名单可查看任务详情
#      | 400062 | 4     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"customerId":"2024071906435660016061","taskId":"400062"} | [{"desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","endTime":4859769600000,"image":{},"platform":"wallet","process":0,"rewardStatus":0,"rewards":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}],"saasId":"monster","startTime":1730103158076,"status":1,"subTasks":[],"taskId":"400062","title":"Hold a Persona NFT on Ethereum","total":0}] |
    # 配置任务,4天后开始,非白名单不可查看
      | 400062 | 4     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"customerId":"2024071906435660016062","taskId":"400062"} | [null]             |
    # 配置任务,5天后开始,白名单不可查看任务详情
      | 400062 | 5     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"customerId":"2024071906435660016061","taskId":"400062"} | [null]             |
    # 配置任务,6天后开始,白名单不可查看任务详情
      | 400062 | 6     |     | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"ACTIVE","startTime":1730194251000,"endTime":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"domain":"wallet","vipLevel":"+0","btn":2,"url":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"channel":"app"}] | {"customerId":"2024071906435660016061","taskId":"400062"} | [null]             |

  场景大纲:taskProgress
    假如task-初始化任务配置task_config<taskConfig>
    同时task-设置任务<taskId>开始时间<start>结束时间<end>
    当task-获取taskProgress<taskProgressRequest>
    那么task-校验TaskDetailResponse<taskDetailResponse>

    例子:
      | taskId | start | end | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | taskProgressRequest                                                                          | taskDetailResponse |
      | 400053 | 4     |     | [{"taskId":"400053","groupId":"400053","isGroup":true,"showList":false,"title":"Weekly Badge","desc":"Weekly Badge","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"code":"early_bird","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"none","provideType":"auto","order":33,"domain":"twitter","vipLevel":"+0","btn":0,"showProgress":false,"channel":"app"},{"taskId":"300019","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.6","saasId":"monster","status":"ACTIVE","startTime":1725580800000,"endTime":1793266251000,"ledgerTitle":"Register by Sep.6","code":"subject_post_x","showCode":"early_bird_1","url":"https://twitter.com/compose/post?text=Wow!%20What%20an%20airdrOwOp!%20%F0%9F%98%B3%20Season%202%20is%20over%20but%20Season%203%20just%20got%20started%20on%20the%20%40SoMon_OwO%20app!%0A%0APsst...%20I%20heard%20that%20SoMon%20users%20%26%20%24OwO%20HODLers%20will%20get%20MORE%20of%20the%20future%20%40OpenSocialLabs%20token%3F%20Might%20not%20want%20to%20miss%20that%20%F0%9F%91%80%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"36","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"36","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":1,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"HODLers","register-time":"20240906"}},{"taskId":"300020","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.13","ledgerTitle":"Register by Sep.13","saasId":"monster","status":"ACTIVE","startTime":1726185600000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_2","url":"https://twitter.com/compose/post?text=Half%20way%20through%20the%20month%20on%20the%20%40SoMon_OwO%20app!%0A%0ASecure%20as%20many%20Points%20as%20possible%20with%20me%20and%20climb%20the%20leaderboard%20with%20Ultimate%20OwO%20POWER!!!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"37","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"37","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":2,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"through","register-time":"20240913"}},{"taskId":"300021","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.20","ledgerTitle":"Register by Sep.20","saasId":"monster","status":"ACTIVE","startTime":1726790400000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_3","url":"https://twitter.com/compose/post?text=Did%20you%20see%3F%20Tribes%20on%20%40SoMon_OwO%20are%20getting%20MAJOR%20upgrades!%0A%0AMore%20ways%20for%20Tribe%20owners%20to%20make%20some%20%24%24%24OwO%24%24%24!%0A%0ALet's%20goOwOo!%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"38","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"38","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":3,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"MAJOR","register-time":"20240920"}},{"taskId":"300022","groupId":"400053","isGroup":false,"showList":false,"title":"Weekly Badge","desc":"Register by Sep.27","ledgerTitle":"Register by Sep.27","saasId":"monster","status":"ACTIVE","startTime":1727395200000,"endTime":1793266251000,"code":"subject_post_x","showCode":"early_bird_4","url":"https://twitter.com/compose/post?text=%40SoMon_OWO%20Season%203%20is%20about%20to%20end%2C%20airdrOwOp%20imminent!!!%0A%0ALast%20chance%20to%20increase%20your%20rOwOrds!%0A%0AFight%20lil'Mons!!!%20(%E0%B9%91OwO)%D9%88%0Ahttps%3A%2F%2Fwww.social.monster","limitMap":{"NORMAL":1,"L1":1},"reward":{"0":[{"type":"BADGE","amount":"1","currency":"39","vipLevel":"L1"},{"type":"BADGE","amount":"1","currency":"39","vipLevel":"NORMAL"}]},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","order":4,"domain":"twitter","vipLevel":"+0","btn":2,"showProgress":false,"lastPost":true,"attr":{"twitter-keyword":"chance","register-time":"20240927"}}] | {"saasId":"monster","customerId":"2024071906435660016061","taskId":"400053","type":"reward"} | []                 |

  # 任务灰度发布,设置白名单开始任务时间,提前正式发布前做任务
  场景大纲:verify
    假如task-初始化任务配置task_config<taskConfig>
    同时task-设置任务<taskId>开始时间<start>结束时间<end>
    同时task-设置灰度任务<taskId>开始时间<grayStart>结束时间<grayEnd>
    当task-任务校验taskVerify<verifyInfo>
    那么luck-校验exceptions为<exceptions>

    例子:
      | taskId | start | end | grayStart | grayEnd | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | verifyInfo                                                                                                                                                                                                                                                | exceptions            |
    # 配置任务,4天后开始,白名单可verify
      | 400062 | 4     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | {"accessToken":{"accessToken":"","accessToken":"","socialCustomerId":"","vipLevel":"NORMAL","userName":"","loginCustomerId":"2024071906435660016061","address":"******************************************"},"taskId":"400062","saasId":"somon","ext":""} | SOCIAL_CALL_BACK_WAIT |

  # 任务灰度发布,设置白名单开始任务时间,提前正式发布前做任务
  场景大纲: verify&doTask
    假如task-初始化任务配置task_config<taskConfig>
    同时task-设置任务<taskId>开始时间<start>结束时间<end>
    同时task-设置灰度任务<taskId>开始时间<grayStart>结束时间<grayEnd>
    当task-任务校验taskVerify<verifyInfo>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
#    那么task-校验VerifyResponse<verifyResponse>
#    当task-手动领取奖励<receiveRewardRequest>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么luck-校验exceptions为<exceptions>
    例子:
      | taskId | start | end | grayStart | grayEnd | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | eventDTO                                                                                                                                                                                                                                                                                                                                                                                            | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | verifyInfo                                                                                                                                                                                                                                                | receiveRewardRequest                                                                                                                                       | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                             | assetTransferInRequest                                                                                                                                                                                                  | verifyResponse     | exceptions            |
    # 配置任务,4天后开始,白名单可verify,doTask
      | 400062 | 4     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | [{"eventCode":"eoa_asset_persona_eth","customerId":"2024071906435660016061","globalUid":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","eventTime":null,"targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","verb":"","targetCustomerId":null,"sourceCustomerId":null,"body":{"vipLevel":"NORMAL","amount":1,"saasId":"monster"},"inc":null}] | [{"businessId":"202410141659240555","completeThreshold":1,"completeTime":"2024-10-14 08:59:24","created":"2024-10-14 08:59:24","customerId":"2024071906435660016061","cycle":"20241014","event":"eoa_asset_persona_eth","expiredTime":"20241014235959","modified":"2024-10-14 08:59:24","progress":1,"status":"DONE","targetId":"-1","taskId":"400062"},{"businessId":"202410141659240555","completeTime":"2024-10-14 08:59:24","created":"2024-10-14 08:59:24","customerId":"2024071906435660016061","cycle":"20241014","event":"eoa_asset_persona_eth","modified":"2024-10-14 08:59:24","status":"DONE","targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","taskId":"400062"}] | {"accessToken":{"accessToken":"","accessToken":"","socialCustomerId":"","vipLevel":"NORMAL","userName":"","loginCustomerId":"2024071906435660016061","address":"******************************************"},"taskId":"400062","saasId":"somon","ext":""} | {"customerId":"2024033102484135902204","address":null,"taskId":"400062","extendAttr":{"amount":1,"customerId":"2024092711325413517532","taskId":"400062"}} | [{"amount":"30.0","batchId":"20241010","businessId":"2024101013435202201","businessType":"activity_task","currency":"POINT","customerId":"2024071906435660016061","extendParam":"{\"desc\":\"Hold a Persona NFT on Ethereum\"}","extendParamMap":{"desc":"Hold a Persona NFT on Ethereum"},"rewardType":"POINT","saasId":"monster","seq":"osp_callback:2024101013435202201:POINT","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":30.0,"businessId":"2024101013460702851","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024071906435660016061","desc":"Hold a Persona NFT on Ethereum","saasId":"monster","type":"POINT"}] | [{"success":true}] | SOCIAL_CALL_BACK_WAIT |
    # 配置任务,4天后开始,非白名单不能verify,doTask
      | 400062 | 4     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | [{"eventCode":"eoa_asset_persona_eth","customerId":"2024033102484135902204","globalUid":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","eventTime":null,"targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","verb":"","targetCustomerId":null,"sourceCustomerId":null,"body":{"vipLevel":"NORMAL","amount":1,"saasId":"monster"},"inc":null}] | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | {"accessToken":{"accessToken":"","accessToken":"","socialCustomerId":"","vipLevel":"NORMAL","userName":"","loginCustomerId":"2024033102484135902204","address":"******************************************"},"taskId":"400062","saasId":"somon","ext":""} | {"customerId":"2024033102484135902204","address":null,"taskId":"400062","extendAttr":{"amount":1,"customerId":"2024092711325413517532","taskId":"400062"}} | []                                                                                                                                                                                                                                                                                                                                                                                                                               | []                                                                                                                                                                                                                      | [{"success":true}] | null                  |
    # 配置任务,5天后开始,白名单可verify,doTask
      | 400062 | 5     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | [{"eventCode":"eoa_asset_persona_eth","customerId":"2024071906435660016061","globalUid":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","eventTime":null,"targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","verb":"","targetCustomerId":null,"sourceCustomerId":null,"body":{"vipLevel":"NORMAL","amount":1,"saasId":"monster"},"inc":null}] | [{"businessId":"202410141659240555","completeThreshold":1,"completeTime":"2024-10-14 08:59:24","created":"2024-10-14 08:59:24","customerId":"2024071906435660016061","cycle":"20241014","event":"eoa_asset_persona_eth","expiredTime":"20241014235959","modified":"2024-10-14 08:59:24","progress":1,"status":"DONE","targetId":"-1","taskId":"400062"},{"businessId":"202410141659240555","completeTime":"2024-10-14 08:59:24","created":"2024-10-14 08:59:24","customerId":"2024071906435660016061","cycle":"20241014","event":"eoa_asset_persona_eth","modified":"2024-10-14 08:59:24","status":"DONE","targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","taskId":"400062"}] | {"accessToken":{"accessToken":"","accessToken":"","socialCustomerId":"","vipLevel":"NORMAL","userName":"","loginCustomerId":"2024071906435660016061","address":"******************************************"},"taskId":"400062","saasId":"somon","ext":""} | {"customerId":"2024033102484135902204","address":null,"taskId":"400062","extendAttr":{"amount":1,"customerId":"2024092711325413517532","taskId":"400062"}} | [{"amount":"30.0","batchId":"20241010","businessId":"2024101013435202201","businessType":"activity_task","currency":"POINT","customerId":"2024071906435660016061","extendParam":"{\"desc\":\"Hold a Persona NFT on Ethereum\"}","extendParamMap":{"desc":"Hold a Persona NFT on Ethereum"},"rewardType":"POINT","saasId":"monster","seq":"osp_callback:2024101013435202201:POINT","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":30.0,"businessId":"2024101013460702851","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024071906435660016061","desc":"Hold a Persona NFT on Ethereum","saasId":"monster","type":"POINT"}] | [{"success":true}] | SOCIAL_CALL_BACK_WAIT |
    # 配置任务,5天后开始,非白名单不能verify,doTask
      | 400062 | 5     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | [{"eventCode":"eoa_asset_persona_eth","customerId":"2024033102484135902204","globalUid":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","eventTime":null,"targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","verb":"","targetCustomerId":null,"sourceCustomerId":null,"body":{"vipLevel":"NORMAL","amount":1,"saasId":"monster"},"inc":null}] | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | {"accessToken":{"accessToken":"","accessToken":"","socialCustomerId":"","vipLevel":"NORMAL","userName":"","loginCustomerId":"2024033102484135902204","address":"******************************************"},"taskId":"400062","saasId":"somon","ext":""} | {"customerId":"2024033102484135902204","address":null,"taskId":"400062","extendAttr":{"amount":1,"customerId":"2024092711325413517532","taskId":"400062"}} | []                                                                                                                                                                                                                                                                                                                                                                                                                               | []                                                                                                                                                                                                                      | [{"success":true}] | null                  |
    # 配置任务,6天后开始,白名单不能verify,doTask
      | 400062 | 6     | 10  | 1         | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | [{"eventCode":"eoa_asset_persona_eth","customerId":"2024071906435660016061","globalUid":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","eventTime":null,"targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","verb":"","targetCustomerId":null,"sourceCustomerId":null,"body":{"vipLevel":"NORMAL","amount":1,"saasId":"monster"},"inc":null}] | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | {"accessToken":{"accessToken":"","accessToken":"","socialCustomerId":"","vipLevel":"NORMAL","userName":"","loginCustomerId":"2024071906435660016061","address":"******************************************"},"taskId":"400062","saasId":"somon","ext":""} | {"customerId":"2024033102484135902204","address":null,"taskId":"400062","extendAttr":{"amount":1,"customerId":"2024092711325413517532","taskId":"400062"}} | []                                                                                                                                                                                                                                                                                                                                                                                                                               | []                                                                                                                                                                                                                      | [{"success":true}] | null                  |
    # 配置任务,6天后开始,非白名单不能verify,doTask
      | 400062 | 6     | 10  | -1        | 10      | [{"taskId":"400062","groupId":null,"isGroup":true,"showList":true,"title":"Hold a Persona NFT on Ethereum","titleV1":"Hold a Persona NFT on Ethereum","desc":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","descV1":"Get [[p30]] points daily by opening the app if your connected wallet is holding Persona NFT (Ethereum)","saasId":"monster","status":"GRAY","startTime":1730194251000,"startTimeV1":1730194251000,"endTime":4859769600000,"endTimeV1":4859769600000,"code":"eoa_asset_persona_eth","showCode":"eoa_asset_persona_eth","showCodeV1":"eoa_asset_persona_eth","limitMap":{"NORMAL":1,"L1":1},"limitMapV1":{"NORMAL":1,"L1":1},"rewardFrequency":1,"rewardFrequencyV1":1,"cycle":"daily","cycleV1":"daily","progressType":"add","progressTypeV1":"add","rewardForm":"fixed","rewardFormV1":"fixed","provideType":"auto","provideTypeV1":"auto","reward":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"rewardV1":{"0":[{"amount":"#map['amount'].doubleValue() > 0 ? 30 : 0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"}]},"order":34,"orderV1":34,"domain":"wallet","domainV1":"wallet","vipLevel":"+0","vipLevelV1":"+0","btn":2,"btnV1":2,"url":"https://magiceden.io/collections/ethereum/******************************************","urlV1":"https://magiceden.io/collections/ethereum/******************************************","link":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"linkV1":{"get now":"https://magiceden.io/collections/ethereum/******************************************"},"attr":{"ospCallBack":"true","taskType":"EOA_ASSET"},"attrV1":{"ospCallBack":"true","taskType":"EOA_ASSET"},"showProgress":false,"showProgressV1":false,"channel":"app"}] | [{"eventCode":"eoa_asset_persona_eth","customerId":"2024033102484135902204","globalUid":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","eventTime":null,"targetId":"monster_eoa_asset_completeTask20240627024221402120434000622024-10-14","verb":"","targetCustomerId":null,"sourceCustomerId":null,"body":{"vipLevel":"NORMAL","amount":1,"saasId":"monster"},"inc":null}] | []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | {"accessToken":{"accessToken":"","accessToken":"","socialCustomerId":"","vipLevel":"NORMAL","userName":"","loginCustomerId":"2024033102484135902204","address":"******************************************"},"taskId":"400062","saasId":"somon","ext":""} | {"customerId":"2024033102484135902204","address":null,"taskId":"400062","extendAttr":{"amount":1,"customerId":"2024092711325413517532","taskId":"400062"}} | []                                                                                                                                                                                                                                                                                                                                                                                                                               | []                                                                                                                                                                                                                      | [{"success":true}] | null                  |



  #配置赛季1开始时间,后续赛季推算
  场景大纲: 执行memberRankingJob
    当task-执行memberRankingJob
    例子:
      |  |
      |  |
