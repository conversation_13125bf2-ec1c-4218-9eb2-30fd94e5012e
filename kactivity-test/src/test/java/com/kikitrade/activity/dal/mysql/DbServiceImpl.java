package com.kikitrade.activity.dal.mysql;

import com.kikitrade.activity.dal.mysql.dao.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Slf4j
@Component
public class DbServiceImpl implements InitializingBean {

    @Resource
    ActivityLotteryConfigTestDao activityLotteryConfigTestDao;
    @Resource
    ActivityLotteryDetailConfigTestDao activityLotteryDetailConfigTestDao;
    @Resource
    ActivityLotteryStoreLogTestDao activityLotteryStoreLogTestDao;
    @Resource
    ActivityLotteryPoolConfigTestDao activityLotteryPoolConfigTestDao;
    @Resource
    LuckFortuneRuleTestDao luckFortuneRuleTestDao;
    @Resource
    ActivityTaskConfigTestDao activityTaskConfigTestDao;
    @Resource
    ActivityEntityTestDao activityEntityTestDao;


    @Override
    public void afterPropertiesSet() throws Exception {
        //#######################创建数据库表###############################
        activityLotteryConfigTestDao.createTableIfNotExist();
        activityLotteryDetailConfigTestDao.createTableIfNotExist();
        activityLotteryStoreLogTestDao.createTableIfNotExist();
        activityLotteryPoolConfigTestDao.createTableIfNotExist();
        luckFortuneRuleTestDao.createTableIfNotExist();
        activityTaskConfigTestDao.createTableIfNotExist();
        activityEntityTestDao.createTableIfNotExist();
        log.info("-----数据库表已创建完成");

        //#######################初始化表数据###############################
/*        activityLotteryConfigTestDao.init();
        activityLotteryDetailConfigTestDao.init();
        log.info("-----表数据已初始化完成");*/

        luckFortuneRuleTestDao.init();
        log.info("-----luck_fortune_rule表数据已初始化完成");



    }
}
