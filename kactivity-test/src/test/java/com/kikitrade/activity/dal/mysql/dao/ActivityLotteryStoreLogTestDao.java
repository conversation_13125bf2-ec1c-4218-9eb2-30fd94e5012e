package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryStoreLog;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ActivityLotteryStoreLogTestDao {

    void createTableIfNotExist();

    @Select("select * from activity_lottery_store_log")
    List<ActivityLotteryStoreLog> selectAll();

    @Delete("truncate activity_lottery_store_log")
    void truncate();
}
