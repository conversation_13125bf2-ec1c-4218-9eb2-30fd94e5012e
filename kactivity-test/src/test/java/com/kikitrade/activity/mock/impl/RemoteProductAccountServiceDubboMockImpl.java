package com.kikitrade.activity.mock.impl;

import com.kikitrade.accounting.api.RemoteProductAccountService;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountConfigDTO;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountConstant;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountDTO;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountParam;
import com.kikitrade.activity.mock.RemoteProductAccountServiceDubboMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.common.util.BeanUtil;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class RemoteProductAccountServiceDubboMockImpl implements RemoteProductAccountServiceDubboMock {
    @Resource
    RemoteProductAccountService remoteProductAccountService;

    @Override
    public void freeze() throws Exception {
        Mockito.doAnswer(c -> {
            ProductAccountParam param = c.getArgument(0);
            BusinessOP.productAccountParam = c.getArgument(0);
            BusinessOP.productAccountParamList.add(param);
            return null;
        }).when(remoteProductAccountService).freeze(Mockito.any(ProductAccountParam.class));

    }

    @Override
    public void getProductAccountConfig(){
        Mockito.doAnswer(c -> {
            ProductAccountConfigDTO productAccountConfigDTO = BusinessOP.productAccountConfigDTO;

            if(productAccountConfigDTO!=null){
                ProductAccountConfigDTO productAccountConfigDTO1 = new ProductAccountConfigDTO();
                BeanUtil.copyProperties(productAccountConfigDTO, productAccountConfigDTO1);
                return productAccountConfigDTO1;
            }
            return null;
        }).when(remoteProductAccountService).getProductAccountConfig(anyString(),anyString(),anyString(),anyString());
    }

    @Override
    public void createConfig() {
        Mockito.doAnswer(c -> {
            Response response = new Response();
            response.setSuccess(true);
            return response;
        }).when(remoteProductAccountService).createConfig(Mockito.any(ProductAccountConfigDTO.class));
    }

    @Override
    public void updateProductAccountConfig() {
        Mockito.doAnswer(c -> {
            Response response = new Response();
            response.setSuccess(true);
            return response;
        }).when(remoteProductAccountService).updateProductAccountConfig(Mockito.any(ProductAccountConfigDTO.class));
    }

    @Override
    public void freezeError() throws Exception {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("remoteProductAccountService.freeze error...");
        }).when(remoteProductAccountService).freeze(Mockito.any(ProductAccountParam.class));

    }
}
