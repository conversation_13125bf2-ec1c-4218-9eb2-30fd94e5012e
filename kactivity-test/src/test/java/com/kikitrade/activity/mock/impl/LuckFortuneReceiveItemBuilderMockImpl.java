package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.mock.LuckFortuneReceiveItemBuilderMock;
import com.kikitrade.framework.common.util.BeanUtil;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.luckFortuneReceiveItemMap;
import static org.mockito.ArgumentMatchers.anyString;

@Component
public class LuckFortuneReceiveItemBuilderMockImpl implements LuckFortuneReceiveItemBuilderMock {

    @Resource
    LuckFortuneReceiveItemBuilder luckFortuneReceiveItemBuilder;


    @Override
    public void findByCustomerIdAndReleaseId() {
        Mockito.doAnswer(c -> {
            if(luckFortuneReceiveItemMap.isEmpty()){
                return null;
            }

            LuckFortuneReceiveItem item = luckFortuneReceiveItemMap.values().stream().findFirst().get();

            String customerId = c.getArgument(0);
            String releaseId = c.getArgument(1);

            if (item.getCustomerId().equals(customerId)&&item.getReleaseId().equals(releaseId)) {
                LuckFortuneReceiveItem luckFortuneReceiveItem2 = new LuckFortuneReceiveItem();
                BeanUtil.copyProperties(item, luckFortuneReceiveItem2);
                return luckFortuneReceiveItem2;
            }
            return null;
        }).when(luckFortuneReceiveItemBuilder).findByCustomerIdAndReleaseId(anyString(), anyString());


    }

    @Override
    public void insert() {
        Mockito.doAnswer(c -> {
            LuckFortuneReceiveItem luckFortuneReceiveItem = c.getArgument(0);

            String id = luckFortuneReceiveItem.getId();

            if (luckFortuneReceiveItemMap.containsKey(id)) {
                return false;
            }
            luckFortuneReceiveItemMap.put(id, luckFortuneReceiveItem);
            return true;
        }).when(luckFortuneReceiveItemBuilder).insert(Mockito.any(LuckFortuneReceiveItem.class));


    }

    @Override
    public void findById() {
        Mockito.doAnswer(c -> {
            String id = c.getArgument(0);
            if (luckFortuneReceiveItemMap.containsKey(id)) {
                LuckFortuneReceiveItem luckFortuneReceiveItem = luckFortuneReceiveItemMap.get(id);
                LuckFortuneReceiveItem luckFortuneReceiveItem2 = new LuckFortuneReceiveItem();
                BeanUtil.copyProperties(luckFortuneReceiveItem, luckFortuneReceiveItem2);
                return luckFortuneReceiveItem2;
            }
            return null;
        }).when(luckFortuneReceiveItemBuilder).findById(anyString());
    }

    @Override
    public void update() {

        Mockito.doAnswer(c -> {
            LuckFortuneReceiveItem luckFortuneReceiveItem = c.getArgument(0);

            String id = luckFortuneReceiveItem.getId();

            if (luckFortuneReceiveItemMap.containsKey(id)) {
                luckFortuneReceiveItemMap.get(id).setStatus(luckFortuneReceiveItem.getStatus());
                return true;
            }
            return false;
        }).when(luckFortuneReceiveItemBuilder).update(Mockito.any(LuckFortuneReceiveItem.class));


    }

    @Override
    public void findByReleaseId() {
        Mockito.doAnswer(c -> {
            String releaseId = c.getArgument(0);

            return luckFortuneReceiveItemMap.values().stream().filter(item->releaseId.equals(item.getReleaseId())).collect(Collectors.toList());

        }).when(luckFortuneReceiveItemBuilder).findByReleaseId(Mockito.any(String.class));

    }
}
