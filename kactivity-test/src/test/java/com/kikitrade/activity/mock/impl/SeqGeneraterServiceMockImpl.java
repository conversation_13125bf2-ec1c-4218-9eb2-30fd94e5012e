package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.mock.SeqGeneraterServiceMock;
import com.kikitrade.activity.util.DateUtil;
import com.kikitrade.activity.util.SeqUtil;
import com.kikitrade.kseq.api.model.SeqRule;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.UUID;

@Component
public class SeqGeneraterServiceMockImpl implements SeqGeneraterServiceMock {
    @Resource
    SeqGeneraterService seqGeneraterService;

    @Override
    public void next() {
        Mockito.doAnswer(c -> {
            SeqRule seqRule = c.getArgument(0);
            String type = seqRule.getType();

            Thread.sleep(50);

            return DateUtil.getNow();
        }).when(seqGeneraterService).next(Mockito.any(SeqRule.class));

    }

    @Override
    public void next2() {
        Mockito.doAnswer(c -> {
            return SeqUtil.getSeq();
        }).when(seqGeneraterService).next(Mockito.any(SeqRule.class));
    }
}
