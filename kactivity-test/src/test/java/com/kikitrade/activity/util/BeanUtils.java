package com.kikitrade.activity.util;

import java.util.ArrayList;
import java.util.List;

public class BeanUtils {

    //copy一个对象
    public static <T> T toBean(Object bean) throws InstantiationException, IllegalAccessException {
        Class<T> clazz = (Class<T>) bean.getClass();
        T newInstance = clazz.newInstance();
        org.springframework.beans.BeanUtils.copyProperties(bean, newInstance);
        return newInstance;
    }
    
    public static <T> List<T> toBeanList(List<T> beanList) throws InstantiationException, IllegalAccessException {
        List<T> list = new ArrayList<>();

        if(beanList.size()==0){
            return list;
        }

        for (Object bean : beanList) {
            Class<T> clazz = (Class<T>) bean.getClass();
            T newInstance = clazz.newInstance();
            org.springframework.beans.BeanUtils.copyProperties(bean, newInstance);
            list.add(newInstance);
        }
        return list;
    }

}



