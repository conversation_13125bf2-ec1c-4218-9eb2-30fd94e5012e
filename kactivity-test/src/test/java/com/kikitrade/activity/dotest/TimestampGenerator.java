package com.kikitrade.activity.dotest;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class TimestampGenerator {
    public static List<Long> generateTimestamps(LocalDateTime startTime, LocalDateTime endTime) {
        List<Long> timestamps = new ArrayList<>();
        LocalDateTime current = startTime;
        while (!current.isAfter(endTime)) {
            timestamps.add(current.toEpochSecond(java.time.ZoneOffset.UTC));
            current = current.plus(1, ChronoUnit.SECONDS);
        }
        return timestamps;
    }

    public static void main(String[] args) {
        LocalDateTime startTime = LocalDateTime.of(2023, 6, 2, 10, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2023, 6, 2, 10, 0, 30);
        List<Long> timestamps = generateTimestamps(startTime, endTime);
        for (Long timestamp : timestamps) {
            System.out.println(timestamp);
        }
    }
}

