package org.opensocial.platform.opeanapi.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.opensocial.platform.opeanapi.model.Profile;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Profiles
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Profiles {

  @JsonProperty("rows")
  @Valid
  private List<Profile> rows = null;

  public Profiles rows(List<Profile> rows) {
    this.rows = rows;
    return this;
  }

  public Profiles addRowsItem(Profile rowsItem) {
    if (this.rows == null) {
      this.rows = new ArrayList<>();
    }
    this.rows.add(rowsItem);
    return this;
  }

  /**
   * Get rows
   * @return rows
  */
  @Valid 
  @Schema(name = "rows", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  public List<Profile> getRows() {
    return rows;
  }

  public void setRows(List<Profile> rows) {
    this.rows = rows;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Profiles profiles = (Profiles) o;
    return Objects.equals(this.rows, profiles.rows);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rows);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Profiles {\n");
    sb.append("    rows: ").append(toIndentedString(rows)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

