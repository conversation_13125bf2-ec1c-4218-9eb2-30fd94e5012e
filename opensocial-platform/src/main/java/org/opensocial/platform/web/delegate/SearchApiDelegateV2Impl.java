package org.opensocial.platform.web.delegate;

import com.kikitrade.ksearch.api.Constants.SearchTypeEnum;
import com.kikitrade.ksearch.api.model.request.SearchCommonRequest;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opensocial.common.config.OpenSocialProperties;
import org.opensocial.common.utils.ResponseEntityUtils;
import org.opensocial.platform.opeanapi.controller.SearchV2ApiDelegate;
import org.opensocial.platform.opeanapi.model.DocMultiplePaginationResponse;
import org.opensocial.platform.opeanapi.model.SearchMultipleDocRequest;
import org.opensocial.platform.web.converter.SearchViewConverter;
import org.opensocial.search.reference.SearchServiceReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: ZhangPengfei
 * @Date: 2024/2/7 10:05 AM
 */
@Service
@Slf4j
public class SearchApiDelegateV2Impl implements SearchV2ApiDelegate {

    @Resource
    private OpenSocialProperties openSocialProperties;

    @Autowired
    private SearchServiceReference searchServiceReference;

    private static final String CHAIN_ID = "chainId";
    private static final String APP_ID = "appId";

    @SneakyThrows
    @Override
    public ResponseEntity<DocMultiplePaginationResponse> searchMultiple(SearchMultipleDocRequest searchRequest) {
        log.info("searchMultiple request is {}", searchRequest);
        String keyword = processKeywords(searchRequest.getKeywords());
        log.info("searchMultiple request fixed keyword is {}", keyword);
        searchRequest.setKeywords(keyword);

        String chainId = null;
        if (searchRequest.getParams() != null && searchRequest.getParams().containsKey(CHAIN_ID)){
            chainId = searchRequest.getParams().get(CHAIN_ID).toString();
        }

        String appId = null;
        if (searchRequest.getParams() != null && searchRequest.getParams().containsKey(APP_ID)){
            appId = searchRequest.getParams().get(APP_ID).toString();
        }

        SearchCommonRequest request = SearchCommonRequest.builder().
                keyWords(searchRequest.getKeywords()).
                pageNumber(searchRequest.getPage()).
                pageSize(searchRequest.getLimit()).
                sort(searchRequest.getSort()).
                param(searchRequest.getParams()).
                chainId(chainId).
                appId(appId).
                build();
        List<SearchTypeEnum> searchTypeEnums = searchRequest.getTypes().stream().map(SearchViewConverter::convertSearchEnum).collect(Collectors.toList());
        Map map = searchServiceReference.searchMultipleTypeDoc(request, searchTypeEnums);
        return ResponseEntityUtils.ok(SearchViewConverter.convertMultiple(map));
    }

    /**
     * 检查 keywords 是否满足要求，不满足要求进行 fix
     * @param keywords keywords
     */
    private String processKeywords(String keywords){
        // 是 null 或者 empty 不检查
        if (StringUtils.isEmpty(keywords)){
            return keywords;
        }
        // 长度不能大于 10
        if (keywords.length() > openSocialProperties.getSearchKeywordsMaxLength()){
            return keywords.substring(0, openSocialProperties.getSearchKeywordsMaxLength());
        }
        return keywords;
    }
}
