syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
package com.kikitrade.kmember.facade;
option java_package = "com.kikitrade.kmember.facade";
option java_multiple_files = true;

message Decimal {
  string value = 1;
}

enum Status {
  Active = 0;
  Disable = 1;
}

enum MemberStatus {
  MemberActive = 0;
  MemberExpired = 1;
  MemberDisable = 2;
}

enum EquityType {
  UN_QUANTIFIED = 0;
  WITHDRAW_QUOTA_PROMOTION = 1;
  MARGIN_INTEREST_DISCOUNT = 2;
  TREASURE_INTEREST_PROMOTION = 3;
  TRADE_REBATE=4;  // 交易返佣
}

enum Scope{
  platform=0;
  celebrity=1;
}

message PointLedger {
  string id = 1;
  string customerId = 2;
  Decimal amount = 3; // 积分数值
  Decimal available = 4; // 操作后积分
  string businessType = 5; // 操作类型
  google.protobuf.Timestamp timestamp = 6; // 时间
}

message Membership {
  string customerId = 1;
  string userName = 2;
  string email = 3;
  string phone = 4;
  MemberStatus status = 5;
  string level = 6;
  Decimal point = 7;
  Decimal receivePoint = 8;
  google.protobuf.Timestamp expireTime = 9;
  google.protobuf.Timestamp created = 10;
  google.protobuf.Timestamp modified = 11;
  string saasId = 12;
  repeated PointLedger ledgers = 13;
}

message Equity {
  int32 id = 1; // 新增时，id传-1；更新时，id传原来的id
  string nameZh = 2; // 中文名称
  string nameHk = 3; // 繁体名称
  string nameEn = 4; // 英文名称
  string remarkZh = 5; // 中文简介
  string remarkHk = 6; // 繁体简介
  string remarkEn = 7; // 英文简介
  Status status = 8; //
  string icon = 9; // 图标
  string hyperlink = 10;
  EquityType type = 12;
  repeated string params = 13;
  bool memberVisible = 14;
  bool propsVisible = 15;
  google.protobuf.Timestamp created = 16; // 创建时间，新增时不用传，修改时传原来的数值
  google.protobuf.Timestamp modified = 17;  // 修改时间，新增/修改时都不用传
  string saasId = 18;
  int64 weight = 19;
  Scope scope = 20; //权益领域：平台权益 明星权益
  string namespace = 21; //明星权益，权益命名空间。如果是平台权益，namespace=0
}

message EquityLight {
  int32 id = 1;
  string name = 2;
  string remark = 3;
  Status status = 4;
  int64 weight = 5;
  Scope scope = 6; //权益领域：平台权益 明星权益
  string namespace = 7; //明星权益，权益命名空间。如果是平台权益，namespace=0
}

message MemberLevelLight {
  string level = 1;
  string name = 2;
  Status status = 3;
  Decimal point = 4;
  int32 validDays = 5;
  Scope scope = 6; //领域：平台权益 明星权益
  string namespace = 7; //命名空间。如果是平台等级，namespace=0

}

message MemberLevel {
  string level = 1;
  string name = 2;
  Status status = 3;
  string icon = 4;
  int32 validDays = 5; // 有效天数
  repeated MemberLevelCondition conditions = 6;
  repeated EquityLight equities = 7;
  google.protobuf.Timestamp created = 8; // 创建时间，新增时不用传，修改时传原来的数值
  google.protobuf.Timestamp modified = 9; // 修改时间，新增/修改时都不用传
  string saasId = 10; // saas id
  int32 sort = 11; // vip等级的排序
  Scope scope = 12; //领域：平台等级 明星等级
  string namespace = 13; //命名空间。如果是平台等级，namespace=0
}

message MemberLevelCondition {
  int32 id = 1; // 新建的时候，传-1，修改的时候传原来的值
  string level = 2;
  string priorLevel = 3;
  int32 point = 4;
  google.protobuf.Timestamp created = 5; // 创建时间，新增时不用传，修改时传原来的数值
  google.protobuf.Timestamp modified = 6; // 修改时间，新增/修改时都不用传
  string saasId = 7; // saas id
}

message Reply {
  bool success = 1;
  string message = 2;
}

message MemberLevelQueryRequest {
  string saasId = 1;
  string namespace = 2; //等级命名空间：如果是平台等级 传0
}

message MemberLevelQueryReply {
  bool success = 1;
  string message = 2;
  repeated MemberLevelLight memberLevels = 3;
}

message MemberLevelGetRequest {
  string saasId = 1;
  string level = 2;
}

message MemberLevelGetReply {
  bool success = 1;
  string message = 2;
  MemberLevel memberLevel = 3;
}

message MemberLevelUpsertRequest {
  string saasId = 1;
  MemberLevel memberLevel = 2;
}

message MemberLevelUpsertReply {
  bool success = 1;
  string message = 2;
  MemberLevel memberLevel = 3;
}

message EquityQueryRequest {
  string saasId = 1;
  string nameEn = 2;  // 按照name查询权益时候的搜索词
  string type = 3; // member-会员权益, props-道具
  string namespace = 4; //平台权益传 0，明星权益传 明星id
}

message EquityQueryReply {
  bool success = 1;
  string message = 2;
  repeated EquityLight equities = 3;
}

message EquityUpsertRequest {
  Equity equity = 1;
}

message EquityUpsertReply {
  bool success = 1;
  string message = 2;
  Equity equities = 3;
}

message EquityGetRequest {
  string id = 1;
}

message EquityReply {
  bool success = 1;
  string message = 2;
  Equity equity = 3;
}

message MembershipQueryRequest {
  string saasId = 1; // 必填
  string customerId = 2; // 非必填
  string phone = 3; // 非必填
  string email = 4; // 非必填
  string level = 5; // 非必填，后端默认值为所有等级
  string limit = 6; // 查询数量，20条
  string cursorCid = 7; // 倒序排列的开始用户ID
}

message MembershipQueryReply {
  bool success = 1;
  string message = 2;
  repeated Membership memberships = 3;
}

message MembershipGetRequest {
  string saasId = 1;
  string customerId = 2;
  int32 limit = 3; // 查询数量限定，200条
  google.protobuf.Timestamp start = 4;// 开始时间
  google.protobuf.Timestamp end = 5;// 结束时间
}

message MembershipGetReply {
  bool success = 1;
  string message = 2;
  Membership membership = 3;
}

message MembershipUpdateRequest {
  string saasId = 1;
  string customerId = 2;  // 用户id
  string level = 3;   // 修改之后的vip等级，不改的话传原值或者不传
  google.protobuf.Timestamp expireTime = 4; // 修改之后的vip过期时间，不改的话传原值或者不传
}

message MembershipUpdateReply {
  bool success = 1;
  string message = 2;
  Membership membership = 3;  // 修改之后的会员等级信息
}

message PointLedgerQryRequest {
  string saasId = 1;
  string customerId = 2;
  google.protobuf.Timestamp start = 3;// 开始时间
  google.protobuf.Timestamp end = 4;// 结束时间
  int32 limit = 5; // 查询数量限定，200
}

service MemberFacade {

  /**
    查询会员等级（列表）
   */
  rpc queryLevels(MemberLevelQueryRequest) returns (MemberLevelQueryReply);
  /**
    获取会员等级信息
   */
  rpc getLevel(MemberLevelGetRequest) returns (MemberLevelGetReply);
  /**
    更新会员等级信息
   */
  rpc upsertLevel(MemberLevelUpsertRequest) returns (MemberLevelUpsertReply);

  /**
    查询权益列表
   */
  rpc queryEquities(EquityQueryRequest) returns (EquityQueryReply);

  /**
    新增/修改权益信息
   */
  rpc upsertEquity(EquityUpsertRequest) returns (EquityUpsertReply);

  /**
    获取权益信息
   */
  rpc getEquity(EquityGetRequest) returns (EquityReply);

  /**
    会员列表查询-不适配明星会员
   */
  rpc queryMembership(MembershipQueryRequest) returns (MembershipQueryReply);

  /**
    会员详情&积分账本-不适配明星会员
   */
  rpc getMembership(MembershipGetRequest) returns (MembershipGetReply);

  /**
    修改会员信息
   */
  rpc updateMemberShip(MembershipUpdateRequest) returns (MembershipUpdateReply);
}
