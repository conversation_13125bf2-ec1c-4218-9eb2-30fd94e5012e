package com.kikitrade.kweb.config;

import com.kikitrade.kweb.utils.PemUtils;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/3 11:01
 */
@Configuration
public class RSAConfiguration {

    @Resource
    private QuestWebProperties questWebProperties;

    @Bean
    public PemUtils pemUtils(){
        return new PemUtils(questWebProperties.getPrivateKey(), questWebProperties.getPublicKey());
    }
}
