package com.kikitrade.member.service.product;

import com.kikitrade.member.dal.tablestore.model.MemberProduceItemDO;
import com.kikitrade.member.model.MemberProductItemDTO;
import com.kikitrade.member.service.ons.CustomerRegisterDTO;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/13 10:42
 */
public interface MemberCastleService {

    /**
     * 初始化仓库
     * @param registerDTO
     * @return
     */
    MemberProduceItemDO initCastle(CustomerRegisterDTO registerDTO);

    /**
     * 结算当前生产的东西
     * @param customerId
     * @return
     */
    Boolean settleProduct(String saasId, String customerId, String vipLevel, Integer inviteCount, Integer cap);

    /**
     * 收割仓库
     * @param saasId
     * @param customerId
     * @return
     */
    Boolean harvestProduct(String saasId, String customerId);

    /**
     * 查询当前用户的仓库
     * @param saasId
     * @param customerId
     * @return
     */
    MemberProductItemDTO castle(String saasId, String customerId);
}
