package com.kikitrade.member.service.membership.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.member.dal.tablestore.model.MembershipDO;
import com.kikitrade.member.model.MemberEventNotifyMsg;
import com.kikitrade.member.model.MemberLevelDTO;
import com.kikitrade.member.model.constant.EventConstant;
import com.kikitrade.member.model.constant.MemberConstant;
import com.kikitrade.member.service.common.KmemberProperties;
import com.kikitrade.member.service.common.NotifyProperties;
import com.kikitrade.member.service.level.MemberLevelService;
import com.kikitrade.member.service.membership.MembershipNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Date;

/**
 * 会员等级过期、升级通知服务
 * <AUTHOR>
 * @create 2022/11/12 11:24 上午
 * @modify
 */
@Service
@Slf4j
public class MembershipNotifyServiceImpl implements MembershipNotifyService {

    @Resource
    private KmemberProperties kmemberProperties;

    @Resource
    private OnsProducer onsProducer;

    @Resource
    private OnsProperties onsProperties;

    @Resource
    private MemberLevelService memberLevelService;

    @Resource
    private EventClient eventClient;

    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public boolean doNotifyExpiring(MembershipDO membership) {
        if (!notifyEventCheck(EventConstant.EventType.EXPIRING)) {
            log.info("doNotifyExpiring, EventType.EXPIRING not config, skip");
            return true;
        }
        // 用户开启自动续费情况下push通知用户
        if (!kmemberProperties.isAutoRenewalDefault()) {
            if (membership.getAutoRenewal() != null && !membership.getAutoRenewal()) {
                log.info("doNotifyExpiring, customer[{}] auto renewal is false, skip", membership.getCustomerId());
                return true;
            }
        }

        // 构造过期推送消息
        MemberEventNotifyMsg msg = new MemberEventNotifyMsg();
        msg.setCustomerId(membership.getCustomerId());
        msg.setEvent(EventConstant.EventType.EXPIRING);
        msg.setOldLevel(membership.getLevel()); // 降级之前的等级
//      msg.setNextLevel();   // 预降级的等级
        msg.setExpireInDays(getIntervalDays(membership.getExpireTime()));
        msg.setTimestamp(System.currentTimeMillis());

        return doNotify(msg);
    }


    @Override
    public boolean doNotifyUpgraded(String customerId, String newLevel, String oldLevel, Date expireTime) {
        if (!notifyEventCheck(EventConstant.EventType.UPGRADED)) {
            log.info("doNotifyUpgraded, EventType.UPGRADED not config, skip");
            return true;
        }
        // 构造过期推送消息
        MemberEventNotifyMsg msg = new MemberEventNotifyMsg();
        msg.setCustomerId(customerId);
        msg.setEvent(EventConstant.EventType.UPGRADED);
        msg.setOldLevel(oldLevel);      // 升级之前的等级
        msg.setNextLevel(newLevel); // 升级之后的等级
        msg.setExpireInDays(getIntervalDays(expireTime));
        msg.setTimestamp(System.currentTimeMillis());

        return doNotify(msg);
    }

    @Override
    public boolean doNotifyLevelChanged(String saasId, String customerId, String oldLevel, String newLevel, String orderId, Date expireTime, String desc) {
        MemberLevelDTO ol = memberLevelService.getLevel(saasId, oldLevel);
        MemberLevelDTO nl = memberLevelService.getLevel(saasId, newLevel);
        if (ol == null || nl == null) {
            log.error("MembershipNotifyServiceImpl doNotifyChanged, oldLevel or newLevel is null, oldLevel: {}, newLevel: {}", oldLevel, newLevel);
            return false;
        }

        EventConstant.EventType type = getEventType(ol, nl);
        if (!notifyEventCheck(type)) {
            log.info("doNotifyLevelChanged, {} type msg not config, skip", type);
            return true;
        }

        MemberEventNotifyMsg msg = new MemberEventNotifyMsg();
        msg.setEvent(type);
        msg.setSaasId(saasId);
        msg.setCustomerId(customerId);
        msg.setOldLevel(oldLevel);
        msg.setNextLevel(newLevel); // 升级之后的等级
        msg.setOrderId(orderId);
        msg.setExpireTime(expireTime.getTime());
        msg.setTimestamp(System.currentTimeMillis());
        msg.setDesc(desc);

        return doNotify(msg);
    }

    private EventConstant.EventType getEventType(MemberLevelDTO oldLevel, MemberLevelDTO newLevel) {
        if (newLevel.getSort() < oldLevel.getSort()) {
            return EventConstant.EventType.DOWNGRADE;
        } else if(newLevel.getSort() > oldLevel.getSort()) {
            return EventConstant.EventType.UPGRADED;
        } else {
            return EventConstant.EventType.RENEWAL;
        }
    }

    private boolean doNotify(MemberEventNotifyMsg msg) {
        try {
            String message = JSON.toJSONString(msg);
            log.info("doNotify start, send topic={}, message={}", kmemberProperties.getMemberEventTopic(), message);
            EventDTO eventDTO = new EventDTO();
            eventDTO.setName(msg.getEvent().name());
            eventDTO.setGlobalUid(msg.getOrderId());
            eventDTO.setBody(JSON.parseObject(message));
            eventDTO.setTime(new Date().getTime());
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(msg.getSaasId(), msg.getCustomerId());
            if(customerBindDTO != null){
                eventDTO.setCustomerId(customerBindDTO.getCid());
            }
            boolean success = eventClient.push(eventDTO);
            //SendResult sendResult = onsProducer.send(kmemberProperties.getMemberEventTopic(), onsProperties.getTag(), message);
            log.info("doNotify finish, message={}, messageId={}", message, success);
            return true;
        } catch (Exception e) {
            log.error("doNotify exception, msg={}", msg, e);
            return false;
        }
    }

    private Integer getIntervalDays(Date expireDate) {
        Days days = Days.daysBetween(new DateTime(new Date()).toLocalDate(), new DateTime(expireDate).toLocalDate());
        return days.getDays();
    }

    private boolean notifyEventCheck(EventConstant.EventType eventType) {
        NotifyProperties notifyProperties = kmemberProperties.getNotify();
        if (notifyProperties != null && StringUtils.isNotBlank(notifyProperties.getNotifyEvents())) {
            for (String event : kmemberProperties.getNotify().getNotifyEvents().split(",")) {
                if (EventConstant.EventType.valueOf(event.trim()) == eventType) {
                    return true;
                }
            }
        }
        return false;
    }

}
