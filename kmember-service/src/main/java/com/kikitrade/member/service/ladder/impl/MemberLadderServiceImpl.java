package com.kikitrade.member.service.ladder.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.asset.service.common.converter.MemberLadderConverter;
import com.kikitrade.member.dal.tablestore.builder.MemberLadderBuilder;
import com.kikitrade.member.dal.tablestore.model.MemberLadderDO;
import com.kikitrade.member.model.MemberLadderCacheMsg;
import com.kikitrade.member.model.MemberLadderDTO;
import com.kikitrade.member.model.constant.MemberConstant;
import com.kikitrade.member.service.cache.CacheService;
import com.kikitrade.member.service.common.KmemberProperties;
import com.kikitrade.member.service.common.RedisKeyConst;
import com.kikitrade.member.service.ladder.MemberLadderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MemberLadderServiceImpl implements MemberLadderService {

    private final static String CACHE_TYPE = "memberLadder";

    private final static String STAT_DATE_PATTERN= "yyyy-MM-dd";

    @Resource
    private KmemberProperties properties;

    @Resource
    private CacheService cacheService;

    @Resource
    private MemberLadderBuilder memberLadderBuilder;

    @Resource(type = RedisTemplate.class)
    private RedisTemplate<String,String> redisTemplate;

    @Override
    public MemberLadderDTO myLadder(String customerId, String statDate, MemberConstant.LadderType type, String namespace) {
        if (namespace == null ) {
            namespace = MemberConstant.DEFAULT_NAMESPACE;
        }
        //如果redis中当前排行榜有效日期数据为空，则以参数数据为准
        String value = redisTemplate.opsForValue().get(RedisKeyConst.ladderStatDateKey(namespace));
        if (StringUtils.isNotBlank(value)) {
            if (!value.equals(statDate)) {
                log.info("redis key [{}] is working!!",RedisKeyConst.ladderStatDateKey(namespace));
                statDate = value;
            }
        }
        MemberLadderDO myLadder = memberLadderBuilder.myLadder(customerId, statDate, type, namespace);
        log.info("MemberLadderServiceImpl, query myLadder, customerId:{}, statDate:{}, type:{}, result:{}",
                customerId,statDate,type, JSON.toJSONString(myLadder));
        return MemberLadderConverter.convertDo2Dto(myLadder);
    }

    @Override
    public List<MemberLadderDTO> ladders(String statDate, MemberConstant.LadderType type,String namespace, int ladderCount) {
        if (namespace == null ) {
            namespace = MemberConstant.DEFAULT_NAMESPACE;
        }
        List<MemberLadderDTO> ladders = getLadders(statDate, type, namespace, ladderCount);
        if (!CollectionUtils.isEmpty(ladders)) {
            return ladders;
        }
        //如果没有数据，则往前推一天
        LocalDate date = LocalDate.parse(statDate, DateTimeFormatter.ofPattern(STAT_DATE_PATTERN));
        String theDayBefore = date.minusDays(1).toString();
        return getLadders(theDayBefore, type, namespace, ladderCount);
    }

    @Override
    public void checkRefresh() {
        String today = DateFormatUtils.format(new Date(), STAT_DATE_PATTERN);
        String namespace = MemberConstant.DEFAULT_NAMESPACE;
        String statDate = redisTemplate.opsForValue().get(RedisKeyConst.ladderStatDateKey(namespace));
        if (StringUtils.isBlank(statDate) || !statDate.equals(today)) {
            Arrays.stream(MemberConstant.LadderType.values()).filter(statType ->
                            !CollectionUtils.isEmpty(getLadders(today, statType, namespace, properties.getLadderMaxRank())))
                    .forEach(statType -> cacheService.sendRefresh(JSON.toJSONString(new MemberLadderCacheMsg(CACHE_TYPE, statType, today))));
        }
    }

    /**
     * 获取排行榜数据
     * @param statDate
     * @param type
     * @param ladderCount
     * @return
     */
    private List<MemberLadderDTO> getLadders(String statDate, MemberConstant.LadderType type, String namespace, int ladderCount) {
        List<MemberLadderDO> ladders = memberLadderBuilder.ladders(statDate, type, namespace, ladderCount, properties.getLadderMaxRank());
        if (CollectionUtils.isEmpty(ladders)) {
            log.warn("ladders is empty, statDate:{},ladderType:{}", statDate, type);
            redisTemplate.delete(RedisKeyConst.ladderStatDateKey(namespace));
            return new ArrayList<>();
        }
        ladders = ladders.stream().sorted(Comparator.comparing(MemberLadderDO::getRank)).collect(Collectors.toList());
        redisTemplate.opsForValue().set(RedisKeyConst.ladderStatDateKey(namespace), statDate);
        ladders = ladders.size() > ladderCount ? ladders.subList(0, ladderCount - 1) : ladders;
        return ladders.stream().map(e -> MemberLadderConverter.convertDo2Dto(e)).collect(Collectors.toList());
    }
}
