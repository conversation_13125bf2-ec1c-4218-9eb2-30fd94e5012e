package com.kikitrade.member.service.cache.impl;

import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.framework.ons.OnsBroadcastProducer;
import com.kikitrade.member.model.constant.CacheConstant;
import com.kikitrade.member.service.cache.CacheService;
import com.kikitrade.member.service.common.KmemberProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2022/8/2 1:57 下午
 * @modify
 */
@Lazy
@Slf4j
@Component
public class CacheServiceImpl implements CacheService {

    @Resource
    private OnsBroadcastProducer producer;

    @Resource
    private KmemberProperties kmemberProperties;

    @Value("${kweb.ons-topic-cache-refresh}")
    private String kwebOnsTopicCacheRefresh;

    @Override
    public void sendRefresh(CacheConstant.Key cacheKey) {
        try {
            if (null != cacheKey) {
                log.info("send cache msg {}", cacheKey);
                SendResult sendResult = producer.send(kmemberProperties.getCacheTopic(), cacheKey.name());
                log.info("send cache msg success, result={}", sendResult);
            }
        } catch (Exception e) {
            log.error("send kmember cache broadcast msg error, cacheKey={}", cacheKey, e);
        }
    }

    @Override
    public void sendRefresh(String msg) {
        try {
            if (StringUtils.isNotBlank(msg)) {
                log.info("send msg {}", msg);
                SendResult sendResult = producer.send(kwebOnsTopicCacheRefresh, msg);
                log.info("send msg success, result={}", sendResult);
            }
        } catch (Exception e) {
            log.error("send kmember cache broadcast msg error, cacheKey={}", msg, e);
        }
    }
}
