package com.kikitrade.member.service.cache;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.framework.ons.OnsBroadcastMessageListener;
import com.kikitrade.member.model.constant.CacheConstant;
import com.kikitrade.member.service.common.KmemberProperties;
import com.kikitrade.member.service.equity.EquityService;
import com.kikitrade.member.service.level.MemberLevelConditionService;
import com.kikitrade.member.service.level.MemberLevelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2022/7/29 1:54 下午
 * @modify
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kmember.cache-topic")
public class CacheListener implements OnsBroadcastMessageListener {

    @Resource
    private KmemberProperties kmemberProperties;

    @Resource
    private MemberLevelService memberLevelService;

    @Resource
    private MemberLevelConditionService memberLevelConditionService;

    @Resource
    private EquityService equityService;

    @Override
    public String topic() {
        return kmemberProperties.getCacheTopic();
    }

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String msgId = null, body = null;
        try {
            msgId = message.getMsgID();
            body = new String(message.getBody(), "utf-8");
            log.info("CacheListener, receive message, start... message={}-{}", msgId, body);

            // 刷新缓存
            refreshByKey(CacheConstant.Key.valueOf(body));
            log.info("CacheListener, receive message, end message={}-{}", msgId, body);
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error("CacheListener, consume exception. message={}-{}", msgId, body, e);
            return Action.ReconsumeLater;
        }
    }

    /**
     * 按指定的key，刷新对应缓存
     * @param cachekey
     */
    private void refreshByKey(CacheConstant.Key cachekey) {
        switch (cachekey) {
            case EQUITY:
                equityService.refresh();
                break;
            case MEMBER_LEVEL:
                memberLevelConditionService.refresh();
                memberLevelService.refresh();
                break;
        }
    }

}
