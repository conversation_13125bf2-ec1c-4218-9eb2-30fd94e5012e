package com.kikitrade.member.service.ladder;

import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.member.model.constant.MemberConstant;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/2 12:01
 */
public interface LadderCacheService {

    String genLadderKey(String saasId, String assetType, MemberConstant.LadderType ladderType);

    Boolean cacheLadder(String saasId, String assetType, String customerId, Double score, MemberConstant.LadderType ladderType);

    Boolean delLadder(String saasId, String assetType);

    Long getLadderSize(String saasId, String assetType, MemberConstant.LadderType ladderType);

    Double getLadderScore(String saasId, String assetType, String customerId, MemberConstant.LadderType ladderType);

    Long getLadderRank(String saasId, String assetType, CustomerBindDTO customerBindDTO, MemberConstant.LadderType ladderType);

    double getScore(AssetDTO assetDO, MemberConstant.LadderType ladderType);
}
