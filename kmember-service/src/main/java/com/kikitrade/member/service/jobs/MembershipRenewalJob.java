package com.kikitrade.member.service.jobs;

import com.kikitrade.member.service.membership.MembershipService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022-08-09
 * 自动续费/过期调度任务
 */
@Component
@Slf4j
public class MembershipRenewalJob implements SimpleJob {

    @Resource
    @Lazy
    private MembershipService membershipService;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            membershipService.renewal();
        } catch (Exception e) {
            log.error("membership renewal exception", e);
        }
    }
}
