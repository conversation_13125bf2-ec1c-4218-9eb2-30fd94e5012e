package com.kikitrade.member.service.membership;

import com.kikitrade.member.dal.tablestore.model.MembershipDO;

import java.util.Date;

public interface MembershipNotifyService {

    boolean doNotifyExpiring(MembershipDO membership);

    boolean doNotifyUpgraded(String customerId, String newLevel, String oldLevel, Date expireTime);

    boolean doNotifyLevelChanged(String saasId, String customerId, String oldLevel, String newLevel, String vipBusinessId, Date expireTime, String desc);
}
