package com.kikitrade.asset.service.operation.impl;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.asset.dal.tablestore.builder.SeasonSettlementItemBuilder;
import com.kikitrade.asset.dal.tablestore.builder.SeasonSettlementSummaryBuilder;
import com.kikitrade.asset.dal.tablestore.model.SeasonSettlementItem;
import com.kikitrade.asset.dal.tablestore.model.SeasonSettlementSummary;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.request.AssetTransferInRequest;
import com.kikitrade.asset.model.request.AssetTransferOutRequest;
import com.kikitrade.asset.model.response.AssetOperateResponse;
import com.kikitrade.asset.service.mq.SeasonSettlementDTO;
import com.kikitrade.asset.service.operation.AssetOperateService;
import com.kikitrade.asset.service.operation.SeasonSettlementItemService;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.member.service.common.KmemberProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/16 15:32
 */
@Service
@Slf4j
public class SeasonSettlementItemServiceImpl implements SeasonSettlementItemService {

    @Resource
    private SeasonSettlementItemBuilder seasonSettlementItemBuilder;
    @Resource
    private AssetOperateService assetOperateService;
    @Resource
    private SeasonSettlementSummaryBuilder seasonSettlementSummaryBuilder;
    @Resource
    private KmemberProperties kmemberProperties;
    @Resource
    private OnsProducer onsProducer;

    /**
     * 结算
     * @param saasId
     * @param season
     * @param limit
     */
    @Override
    public void settlement(String saasId, String season, String customerIds, Integer limit) {
        if(customerIds != null){
            List<SeasonSettlementItem> items = new ArrayList<>();
            for(String customerId : customerIds.split(",")){
                SeasonSettlementItem item = seasonSettlementItemBuilder.getById(saasId, season, customerId);
                if(!item.getIsSettlement()){
                    items.add(item);
                }
            }
            settlement(items);
        }else if(limit != null){
            Page<SeasonSettlementItem> itemPage = seasonSettlementItemBuilder.getByLimit(saasId, season, limit, null);
            if(itemPage != null && !CollectionUtils.isEmpty(itemPage.getRows())){
                settlement(itemPage.getRows());
            }
        }else{
            for (int i = 0; i <= 2; i++) {
                SeasonSettlementDTO settlementDTO = new SeasonSettlementDTO();
                settlementDTO.setSaasId(saasId);
                settlementDTO.setSeason(season);
                settlementDTO.setCustomerSuffix(i);
                SendResult sendResult = onsProducer.send(kmemberProperties.getEqualizationSettlementTopic(), JSON.toJSONString(settlementDTO));
                log.info("send to EqualizationSettlementTopic:{},{},{}", sendResult.getTopic(), sendResult.getMessageId(), settlementDTO);
            }
        }
    }

    @Override
    public Long equalizationSettlement(String saasId, String season, Integer customerSuffix) {
        log.info("saasId = {}, season = {}, customerSuffix = {}", saasId, season, customerSuffix);
        long totalCount = 0L;
        try {
            Page<SeasonSettlementItem> itemPage;
            List<Integer> suffixList = getCustomerSuffixList(customerSuffix);
            if(CollectionUtils.isEmpty(suffixList)){
                return 0L;
            }
            int offset = 0;
            do{
                itemPage = seasonSettlementItemBuilder.pageSearch(saasId, season, offset, 100, false, suffixList);
                log.info("itemPage = {}", JSON.toJSONString(itemPage));
                if(itemPage == null || CollectionUtils.isEmpty(itemPage.getRows())){
                    offset = 0;
                    itemPage = seasonSettlementItemBuilder.pageSearch(saasId, season, offset, 100, false, suffixList);
                }
                if(itemPage != null && !CollectionUtils.isEmpty(itemPage.getRows())){
                    offset += itemPage.getRows().size();
                    totalCount = totalCount == 0 ? itemPage.getTotalCount() : totalCount;
                    settlement(itemPage.getRows());
                }
            }while (itemPage != null && !CollectionUtils.isEmpty(itemPage.getRows()));
        } catch (Exception e){
            log.error("equalizationSettlement error", e);
        }
        return totalCount;
    }

    private void settlement(List<SeasonSettlementItem> list){
        for(SeasonSettlementItem item : list){
            if(item.getPoint().compareTo(BigDecimal.ZERO) <= 0){
                item.setIsSettlement(true);
                seasonSettlementItemBuilder.update(item);
                continue;
            }
            try{
                long start = System.currentTimeMillis();
                boolean success = transferOut(item) && transferIn(item);
                log.info("settlement, customerId:{}, time:{}, result:{}", item.getCustomerId(), System.currentTimeMillis() - start, success);
                if(success){
                    item.setIsSettlement(true);
                    seasonSettlementItemBuilder.update(item);
                }
            }catch (Exception ex){
                log.error("Sleep error: {}", item.getCustomerId(), ex);
            }
        }
    }

    private Boolean transferOut(SeasonSettlementItem item){
        try{
            AssetTransferOutRequest req = AssetTransferOutRequest.builder()
                    .amount(item.getPoint())
                    .businessId(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd000000")) + "5" + item.getCustomerId())
                    .customerId(item.getCustomerId())
                    .businessType(AssetBusinessType.SEASON_SETTLEMENT)
                    .category(AssetCategory.NORMAL)
                    .type(AssetType.POINT)
                    .desc("Points carryover")
                    .saasId(item.getSaasId())
                    .build();
            AssetOperateResponse res = assetOperateService.transferOut(req);
            return true;
        }catch (Exception ex){
            log.error("season settlement transferOut error:{}", item, ex);
            return false;
        }
    }

    private Boolean transferIn(SeasonSettlementItem item){
        try {
            BigDecimal amount = item.getPoint().multiply(BigDecimal.valueOf(0.02).multiply(BigDecimal.valueOf(item.getVipCount() == null ? 0 : item.getVipCount()))).setScale(0, RoundingMode.HALF_DOWN);
            if(amount.compareTo(BigDecimal.ZERO) <= 0){
                return true;
            }
            AssetTransferInRequest req = AssetTransferInRequest.builder()
                    .amount(amount)
                    .businessId(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd000000")) + "4" + item.getCustomerId())
                    .customerId(item.getCustomerId())
                    .businessType(AssetBusinessType.SEASON_SETTLEMENT)
                    .category(AssetCategory.NORMAL)
                    .type(AssetType.POINT)
                    .desc("Points carryover")
                    .saasId(item.getSaasId())
                    .build();

            AssetOperateResponse res = assetOperateService.transferIn(req);
            return true;
        }catch (Exception ex){
            log.error("season settlement transferIn error:{}", item, ex);
            return false;
        }
    }

    /**
     * 结算回滚
     *
     * @param saasId
     * @param season
     * @param limit
     */
    @Override
    public void settlementBack(String saasId, String season, String customerIds, Integer limit) {
        if(customerIds != null){
            List<SeasonSettlementItem> items = new ArrayList<>();
            for(String customerId : customerIds.split(",")){
                SeasonSettlementItem item = seasonSettlementItemBuilder.getById(saasId, season, customerId);
                if(item.getIsSettlement()){
                    items.add(item);
                }
            }
            settlementBack(items);
        }else if(limit != null){
            Page<SeasonSettlementItem> itemPage = seasonSettlementItemBuilder.getByLimit(saasId, season, limit, true);
            if(itemPage != null && !CollectionUtils.isEmpty(itemPage.getRows())){
                settlementBack(itemPage.getRows());
            }
        }else{
            new Thread(() -> {
                Page<SeasonSettlementItem> itemPage;
                int offset = 0;
                do{
                    itemPage = seasonSettlementItemBuilder.pageSearch(saasId, season, 0, 100, true, null);
                    if(itemPage != null && !CollectionUtils.isEmpty(itemPage.getRows())){
                        log.info("offset:{}", offset + itemPage.getRows().size());
                        settlementBack(itemPage.getRows());
                    }
                }while (itemPage != null && !CollectionUtils.isEmpty(itemPage.getRows()));
                log.info("settlement end");
            }).start();
        }
    }

    private void settlementBack(List<SeasonSettlementItem> list){
        for(SeasonSettlementItem item : list){
            if(item.getPoint().compareTo(BigDecimal.ZERO) <= 0){
                item.setIsSettlement(false);
                seasonSettlementItemBuilder.update(item);
                continue;
            }
            long start = System.currentTimeMillis();
            boolean success = transferOutBack(item) && transferInBack(item);
            log.info("settlement, customerId:{}, time:{}, result:{}", item.getCustomerId(), System.currentTimeMillis() - start, success);
            try{
                item.setIsSettlement(false);
                seasonSettlementItemBuilder.update(item);
            }catch (Exception ex){
                log.error("Sleep error", ex);
            }
        }
    }

    private Boolean transferOutBack(SeasonSettlementItem item){
        try{
            AssetTransferInRequest req = AssetTransferInRequest.builder()
                    .amount(item.getPoint())
                    .businessId(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd000000")) + "6" + item.getCustomerId())
                    .customerId(item.getCustomerId())
                    .businessType(AssetBusinessType.SEASON_SETTLEMENT)
                    .category(AssetCategory.NORMAL)
                    .type(AssetType.POINT)
                    .desc(AssetBusinessType.SEASON_SETTLEMENT.getDesc())
                    .saasId(item.getSaasId())
                    .build();
            AssetOperateResponse res = assetOperateService.transferIn(req);
            return true;
        }catch (Exception ex){
            log.error("season settlement transferOut error:{}", item, ex);
            return false;
        }
    }

    private Boolean transferInBack(SeasonSettlementItem item){
        try {
            BigDecimal amount = item.getPoint().multiply(BigDecimal.valueOf(0.02).multiply(BigDecimal.valueOf(item.getVipCount() == null ? 0 : item.getVipCount()))).setScale(1, RoundingMode.HALF_DOWN);
            if(amount.compareTo(BigDecimal.ZERO) <= 0){
                return true;
            }
            AssetTransferOutRequest req = AssetTransferOutRequest.builder()
                    .amount(amount)
                    .businessId(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd000000")) + "7" + item.getCustomerId())
                    .customerId(item.getCustomerId())
                    .businessType(AssetBusinessType.SEASON_SETTLEMENT)
                    .category(AssetCategory.NORMAL)
                    .type(AssetType.POINT)
                    .desc("Points carryover")
                    .saasId(item.getSaasId())
                    .build();

            AssetOperateResponse res = assetOperateService.transferOut(req);
            return true;
        }catch (Exception ex){
            log.error("season settlement transferIn error:{}", item, ex);
            return false;
        }
    }

    private List<Integer> getCustomerSuffixList(Integer customerSuffix){
         return switch (customerSuffix){
            case 0 -> Arrays.asList(0, 1, 2);
            case 1 -> Arrays.asList(3, 4, 5);
            case 2 -> Arrays.asList(6, 7, 8, 9);
            default -> new ArrayList<>();
        };
    }
}
