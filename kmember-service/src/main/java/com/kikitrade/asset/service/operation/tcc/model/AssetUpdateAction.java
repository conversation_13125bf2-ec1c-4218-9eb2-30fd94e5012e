package com.kikitrade.asset.service.operation.tcc.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Asset Update Action
 *
 * <AUTHOR>
 * @create 2022/7/28 11:17 上午
 * @modify
 */
@Data
public class AssetUpdateAction extends AssetBaseAction {
    /**
     * 账务变动数量
     */
    private BigDecimal amount;
    /**
     * 解冻数量
     */
    private BigDecimal unfreezeAmount;

    /**
     * 支出
     */
    private BigDecimal sent;

    /**
     * 收入
     */
    private BigDecimal received;
}
