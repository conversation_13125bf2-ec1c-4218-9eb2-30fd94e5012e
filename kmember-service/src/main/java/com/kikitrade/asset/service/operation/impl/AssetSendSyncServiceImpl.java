package com.kikitrade.asset.service.operation.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.asset.dal.mysql.operate.model.AssetDO;
import com.kikitrade.asset.service.operation.AssetSendSyncService;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.member.service.common.KmemberProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/11 17:56
 */
@Service
@Slf4j
public class AssetSendSyncServiceImpl implements AssetSendSyncService {

    @Resource
    private OnsProducer onsProducer;
    @Resource
    private KmemberProperties kmemberProperties;

    @Override
    public void send(Map<String, AssetDO> message) {
        SendResult sendResult = onsProducer.send(kmemberProperties.getAssetKernelSyncTopic(), JSON.toJSONString(message));
        log.info("AssetKernelSync:{},{}", sendResult.getTopic(), sendResult.getMessageId());
    }
}
