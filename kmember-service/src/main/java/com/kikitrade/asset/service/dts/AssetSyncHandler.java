package com.kikitrade.asset.service.dts;

import com.alibaba.fastjson.JSON;
import com.aliyun.dts.subscribe.clients.record.DefaultUserRecord;
import com.aliyun.dts.subscribe.clients.record.OperationType;
import com.aliyun.dts.subscribe.clients.record.RecordField;
import com.aliyun.dts.subscribe.clients.record.RowImage;
import com.aliyun.dts.subscribe.clients.record.value.Value;
import com.aliyun.openservices.ons.api.SendResult;
import com.csvreader.CsvReader;
import com.kikitrade.asset.dal.tablestore.builder.AssetStoreBuilder;
import com.kikitrade.asset.dal.tablestore.model.AssetReadonlyDO;
import com.kikitrade.asset.dal.tablestore.model.constant.AssetOtsConstant;
import com.kikitrade.asset.service.dts.consts.SourceAssetConstant;
import com.kikitrade.asset.service.util.CommonUtil;
import com.kikitrade.framework.dts.subscribe.BaseSyncHandler;
import com.kikitrade.framework.dts.subscribe.SyncHandler;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.member.dal.tablestore.builder.QuestsLadderBuilder;
import com.kikitrade.member.dal.tablestore.model.QuestsPointRankingDO;
import com.kikitrade.member.model.MemberLevelDTO;
import com.kikitrade.member.model.MembershipDTO;
import com.kikitrade.member.model.constant.CacheConstant;
import com.kikitrade.member.model.constant.MemberConstant;
import com.kikitrade.member.service.common.KmemberProperties;
import com.kikitrade.member.service.level.MemberLevelService;
import com.kikitrade.member.service.membership.MembershipService;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.member.service.common.KmemberProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * AssetSyncHandler
 *
 * <AUTHOR>
 * @create 2022/7/28 23:26 下午
 * @modify
 */
@Component
@Slf4j
public class AssetSyncHandler extends BaseSyncHandler implements SyncHandler {

    @Resource
    private AssetStoreBuilder assetStoreBuilder;
    @Resource
    private KmemberProperties kmemberProperties;
    @Resource
    private OnsProducer onsProducer;;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private QuestsLadderBuilder questsLadderBuilder;
    @Resource
    private MembershipService membershipService;
    @Resource
    private MemberLevelService memberLevelService;

    @Override
    public Pattern table() {
        return AssetOtsConstant.SINGLE_TABLE_NAME_PATTERN;
    }

    @Override
    public boolean syncData(DefaultUserRecord record) {

        log.info("AssetSyncHandler record {}", record.getOffset());

        /*AssetReadonlyDO asset = null;
        try {
            asset = parseFromRecord(record);
            if(asset == null){
                log.info("AssetSyncHandler asset is null");
                return true;
            }
            Map<String, String> message = new HashMap<>();
            message.put("data", JSON.toJSONString(asset));
            switch (record.getOperationType()) {
                case INSERT:
                    message.put("operationType", OperationType.INSERT.name());
                    break;
                case UPDATE:
                    message.put("operationType", OperationType.UPDATE.name());
                    break;
            }
            if(message.containsKey("data") && message.containsKey("operationType")){
                SendResult sendResult = onsProducer.send(kmemberProperties.getAssetSyncTopic(), JSON.toJSONString(message));
                log.info("AssetSyncHandler:{},{}", sendResult.getTopic(), sendResult.getMessageId());
            }
        } catch (Exception e) {
            log.error("syncData fail, asset:{}", null == asset ? "null" : JSON.toJSONString(asset), e);
            return false;
        }*/
        return true;

    }

    private AssetReadonlyDO parseFromRecord(DefaultUserRecord record) {
        OperationType operationType = record.getOperationType();
        boolean updateOpt = false;
        switch (operationType) {
            case INSERT:
                break;
            case UPDATE:
                updateOpt = true;
                break;
            default:
                return null;
        }

        List<RecordField> fields = record.getSchema().getFields();

        if (fields == null || fields.isEmpty()) {
            return null;
        }

        RowImage afterImage = record.getAfterImage();

        AssetReadonlyDO asset = new AssetReadonlyDO();

        for (int i = 0; i < fields.size(); i++) {


            RecordField field = fields.get(i);

            // TODO check
            // String val = getFormattedFieldValue(field);
            Value value = afterImage.getValue(i);
            // val is null, not set the field
            if (value == null) {
                continue;
            }
            String val = value.toString();

            String fieldName = field.getFieldName();
            switch (fieldName) {
                case SourceAssetConstant.ID:
                    asset.setId(val);
                    break;
                case SourceAssetConstant.VERSION:
                    asset.setVersion(val);
                    break;
                case SourceAssetConstant.NAMESPACE:
                    asset.setNamespace(val);
                    break;
                case SourceAssetConstant.CUSTOMER_ID:
                    asset.setCustomerId(val);
                    break;
                case SourceAssetConstant.AVAILABLE:
                    //todo 查询会员信息
                    asset.setAvailable(toBigDecimal(val));
                    asset.setOriginalAvailable(toBigDecimal(val));
                    break;
                case SourceAssetConstant.FROZEN:
                    asset.setFrozen(toBigDecimal(val));
                    break;
                case SourceAssetConstant.TOTAL_RECEIVED:
                    asset.setOriginalTotalReceived(toBigDecimal(val));
                    asset.setTotalReceived(toBigDecimal(val));
                    break;
                case SourceAssetConstant.TOTAL_SENT:
                    asset.setTotalSent(toBigDecimal(val));
                    break;
                case SourceAssetConstant.STATUS:
                    asset.setStatus(toInteger(val));
                    break;
                case SourceAssetConstant.ASSET_TYPE:
                    asset.setAssetType(toInteger(val));
                    break;
                case SourceAssetConstant.INSTITUTE_ID:
                    asset.setInstituteId(toInteger(val));
                    break;
                case SourceAssetConstant.CATEGORY:
                    asset.setCategory(val);
                    break;
                case SourceAssetConstant.CURRENCY:
                    asset.setCurrency(val);
                    break;
                case SourceAssetConstant.CURRENCY_ID:
                    asset.setCurrencyId(toInteger(val));
                    break;
                case SourceAssetConstant.CREATED:
                    asset.setCreated(toTimestamp(val).getTime());
                    break;
                case SourceAssetConstant.MODIFIED:
                    asset.setModified(toTimestamp(val).getTime());
                    break;
                case SourceAssetConstant.SAAS_ID:
                    asset.setSaasId(val);
                    break;
                default:
                    log.info("unknown asset fieldName {}.{} - {} value:{}", record.getSchema().getSchemaName().get(),
                            record.getSchema().getTableName().get(), fieldName, val);
                    break;
            }
        }

        return asset;

    }


    @Override
    public boolean dataSupplement(CsvReader csvReader) {

        try {
            AssetReadonlyDO asset = new AssetReadonlyDO();
            asset.setId(csvReader.get(SourceAssetConstant.ID));
            asset.setCustomerId(csvReader.get(SourceAssetConstant.CUSTOMER_ID));
            asset.setNamespace(csvReader.get(SourceAssetConstant.NAMESPACE));
            asset.setVersion(csvReader.get(SourceAssetConstant.VERSION));
            asset.setAvailable(toBigDecimal(csvReader.get(SourceAssetConstant.AVAILABLE)));
            asset.setFrozen(toBigDecimal(csvReader.get(SourceAssetConstant.FROZEN)));
            asset.setTotalReceived(toBigDecimal(csvReader.get(SourceAssetConstant.TOTAL_RECEIVED)));
            asset.setTotalSent(toBigDecimal(csvReader.get(SourceAssetConstant.TOTAL_SENT)));
            asset.setStatus(toInteger(csvReader.get(SourceAssetConstant.STATUS)));
            asset.setAssetType(toInteger(csvReader.get(SourceAssetConstant.ASSET_TYPE)));
            asset.setInstituteId(toInteger(csvReader.get(SourceAssetConstant.INSTITUTE_ID)));
            asset.setCurrencyId(toInteger(csvReader.get(SourceAssetConstant.CURRENCY_ID)));
            asset.setCurrency(csvReader.get(SourceAssetConstant.CURRENCY));
            asset.setCategory(csvReader.get(SourceAssetConstant.CATEGORY));
            asset.setCreated(toTimestamp(csvReader.get(SourceAssetConstant.CREATED)).getTime());
            asset.setModified(toTimestamp(csvReader.get(SourceAssetConstant.MODIFIED)).getTime());
            asset.setSaasId(csvReader.get(SourceAssetConstant.SAAS_ID));
            // write to ots
            assetStoreBuilder.create(asset);

        } catch (Exception e) {
            log.info(csvReader.getRawRecord());
            log.error("AssetDataSupplementHandler process failed.", e);
            return false;
        }
        return true;
    }
}
