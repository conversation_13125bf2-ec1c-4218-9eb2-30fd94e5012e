// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kmember/MemberFacade.proto

package com.kikitrade.kmember.facade;

/**
 * Protobuf type {@code com.kikitrade.kmember.facade.EquityReply}
 */
public final class EquityReply extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.kmember.facade.EquityReply)
    EquityReplyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use EquityReply.newBuilder() to construct.
  private EquityReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private EquityReply() {
    message_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new EquityReply();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private EquityReply(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            success_ = input.readBool();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            message_ = s;
            break;
          }
          case 26: {
            com.kikitrade.kmember.facade.Equity.Builder subBuilder = null;
            if (equity_ != null) {
              subBuilder = equity_.toBuilder();
            }
            equity_ = input.readMessage(com.kikitrade.kmember.facade.Equity.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(equity_);
              equity_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_EquityReply_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_EquityReply_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.kmember.facade.EquityReply.class, com.kikitrade.kmember.facade.EquityReply.Builder.class);
  }

  public static final int SUCCESS_FIELD_NUMBER = 1;
  private boolean success_;
  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object message_;
  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  @java.lang.Override
  public java.lang.String getMessage() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EQUITY_FIELD_NUMBER = 3;
  private com.kikitrade.kmember.facade.Equity equity_;
  /**
   * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
   * @return Whether the equity field is set.
   */
  @java.lang.Override
  public boolean hasEquity() {
    return equity_ != null;
  }
  /**
   * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
   * @return The equity.
   */
  @java.lang.Override
  public com.kikitrade.kmember.facade.Equity getEquity() {
    return equity_ == null ? com.kikitrade.kmember.facade.Equity.getDefaultInstance() : equity_;
  }
  /**
   * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.kmember.facade.EquityOrBuilder getEquityOrBuilder() {
    return getEquity();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (success_ != false) {
      output.writeBool(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
    }
    if (equity_ != null) {
      output.writeMessage(3, getEquity());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
    }
    if (equity_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getEquity());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.kmember.facade.EquityReply)) {
      return super.equals(obj);
    }
    com.kikitrade.kmember.facade.EquityReply other = (com.kikitrade.kmember.facade.EquityReply) obj;

    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (hasEquity() != other.hasEquity()) return false;
    if (hasEquity()) {
      if (!getEquity()
          .equals(other.getEquity())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    if (hasEquity()) {
      hash = (37 * hash) + EQUITY_FIELD_NUMBER;
      hash = (53 * hash) + getEquity().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.EquityReply parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.kmember.facade.EquityReply prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.kmember.facade.EquityReply}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.kmember.facade.EquityReply)
      com.kikitrade.kmember.facade.EquityReplyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_EquityReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_EquityReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.kmember.facade.EquityReply.class, com.kikitrade.kmember.facade.EquityReply.Builder.class);
    }

    // Construct using com.kikitrade.kmember.facade.EquityReply.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      success_ = false;

      message_ = "";

      if (equityBuilder_ == null) {
        equity_ = null;
      } else {
        equity_ = null;
        equityBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_EquityReply_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.EquityReply getDefaultInstanceForType() {
      return com.kikitrade.kmember.facade.EquityReply.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.EquityReply build() {
      com.kikitrade.kmember.facade.EquityReply result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.EquityReply buildPartial() {
      com.kikitrade.kmember.facade.EquityReply result = new com.kikitrade.kmember.facade.EquityReply(this);
      result.success_ = success_;
      result.message_ = message_;
      if (equityBuilder_ == null) {
        result.equity_ = equity_;
      } else {
        result.equity_ = equityBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.kmember.facade.EquityReply) {
        return mergeFrom((com.kikitrade.kmember.facade.EquityReply)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.kmember.facade.EquityReply other) {
      if (other == com.kikitrade.kmember.facade.EquityReply.getDefaultInstance()) return this;
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        onChanged();
      }
      if (other.hasEquity()) {
        mergeEquity(other.getEquity());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.kikitrade.kmember.facade.EquityReply parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.kikitrade.kmember.facade.EquityReply) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private boolean success_ ;
    /**
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <code>bool success = 1;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {
      
      success_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool success = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object message_ = "";
    /**
     * <code>string message = 2;</code>
     * @return The message.
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      message_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      
      message_ = getDefaultInstance().getMessage();
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      message_ = value;
      onChanged();
      return this;
    }

    private com.kikitrade.kmember.facade.Equity equity_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kmember.facade.Equity, com.kikitrade.kmember.facade.Equity.Builder, com.kikitrade.kmember.facade.EquityOrBuilder> equityBuilder_;
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     * @return Whether the equity field is set.
     */
    public boolean hasEquity() {
      return equityBuilder_ != null || equity_ != null;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     * @return The equity.
     */
    public com.kikitrade.kmember.facade.Equity getEquity() {
      if (equityBuilder_ == null) {
        return equity_ == null ? com.kikitrade.kmember.facade.Equity.getDefaultInstance() : equity_;
      } else {
        return equityBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     */
    public Builder setEquity(com.kikitrade.kmember.facade.Equity value) {
      if (equityBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        equity_ = value;
        onChanged();
      } else {
        equityBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     */
    public Builder setEquity(
        com.kikitrade.kmember.facade.Equity.Builder builderForValue) {
      if (equityBuilder_ == null) {
        equity_ = builderForValue.build();
        onChanged();
      } else {
        equityBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     */
    public Builder mergeEquity(com.kikitrade.kmember.facade.Equity value) {
      if (equityBuilder_ == null) {
        if (equity_ != null) {
          equity_ =
            com.kikitrade.kmember.facade.Equity.newBuilder(equity_).mergeFrom(value).buildPartial();
        } else {
          equity_ = value;
        }
        onChanged();
      } else {
        equityBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     */
    public Builder clearEquity() {
      if (equityBuilder_ == null) {
        equity_ = null;
        onChanged();
      } else {
        equity_ = null;
        equityBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     */
    public com.kikitrade.kmember.facade.Equity.Builder getEquityBuilder() {
      
      onChanged();
      return getEquityFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     */
    public com.kikitrade.kmember.facade.EquityOrBuilder getEquityOrBuilder() {
      if (equityBuilder_ != null) {
        return equityBuilder_.getMessageOrBuilder();
      } else {
        return equity_ == null ?
            com.kikitrade.kmember.facade.Equity.getDefaultInstance() : equity_;
      }
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Equity equity = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kmember.facade.Equity, com.kikitrade.kmember.facade.Equity.Builder, com.kikitrade.kmember.facade.EquityOrBuilder> 
        getEquityFieldBuilder() {
      if (equityBuilder_ == null) {
        equityBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.kikitrade.kmember.facade.Equity, com.kikitrade.kmember.facade.Equity.Builder, com.kikitrade.kmember.facade.EquityOrBuilder>(
                getEquity(),
                getParentForChildren(),
                isClean());
        equity_ = null;
      }
      return equityBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.kmember.facade.EquityReply)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.kmember.facade.EquityReply)
  private static final com.kikitrade.kmember.facade.EquityReply DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.kmember.facade.EquityReply();
  }

  public static com.kikitrade.kmember.facade.EquityReply getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<EquityReply>
      PARSER = new com.google.protobuf.AbstractParser<EquityReply>() {
    @java.lang.Override
    public EquityReply parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new EquityReply(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<EquityReply> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<EquityReply> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.kmember.facade.EquityReply getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

