// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kmember/MemberFacade.proto

package com.kikitrade.kmember.facade;

/**
 * Protobuf type {@code com.kikitrade.kmember.facade.MemberLevelUpsertRequest}
 */
public final class MemberLevelUpsertRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.kmember.facade.MemberLevelUpsertRequest)
    MemberLevelUpsertRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MemberLevelUpsertRequest.newBuilder() to construct.
  private MemberLevelUpsertRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MemberLevelUpsertRequest() {
    saasId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MemberLevelUpsertRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelUpsertRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelUpsertRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.kmember.facade.MemberLevelUpsertRequest.class, com.kikitrade.kmember.facade.MemberLevelUpsertRequest.Builder.class);
  }

  public static final int SAASID_FIELD_NUMBER = 1;
  private volatile java.lang.Object saasId_;
  /**
   * <code>string saasId = 1;</code>
   * @return The saasId.
   */
  @java.lang.Override
  public java.lang.String getSaasId() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      saasId_ = s;
      return s;
    }
  }
  /**
   * <code>string saasId = 1;</code>
   * @return The bytes for saasId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSaasIdBytes() {
    java.lang.Object ref = saasId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      saasId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MEMBERLEVEL_FIELD_NUMBER = 2;
  private com.kikitrade.kmember.facade.MemberLevel memberLevel_;
  /**
   * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
   * @return Whether the memberLevel field is set.
   */
  @java.lang.Override
  public boolean hasMemberLevel() {
    return memberLevel_ != null;
  }
  /**
   * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
   * @return The memberLevel.
   */
  @java.lang.Override
  public com.kikitrade.kmember.facade.MemberLevel getMemberLevel() {
    return memberLevel_ == null ? com.kikitrade.kmember.facade.MemberLevel.getDefaultInstance() : memberLevel_;
  }
  /**
   * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
   */
  @java.lang.Override
  public com.kikitrade.kmember.facade.MemberLevelOrBuilder getMemberLevelOrBuilder() {
    return getMemberLevel();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, saasId_);
    }
    if (memberLevel_ != null) {
      output.writeMessage(2, getMemberLevel());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(saasId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, saasId_);
    }
    if (memberLevel_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getMemberLevel());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.kmember.facade.MemberLevelUpsertRequest)) {
      return super.equals(obj);
    }
    com.kikitrade.kmember.facade.MemberLevelUpsertRequest other = (com.kikitrade.kmember.facade.MemberLevelUpsertRequest) obj;

    if (!getSaasId()
        .equals(other.getSaasId())) return false;
    if (hasMemberLevel() != other.hasMemberLevel()) return false;
    if (hasMemberLevel()) {
      if (!getMemberLevel()
          .equals(other.getMemberLevel())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SAASID_FIELD_NUMBER;
    hash = (53 * hash) + getSaasId().hashCode();
    if (hasMemberLevel()) {
      hash = (37 * hash) + MEMBERLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getMemberLevel().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.kmember.facade.MemberLevelUpsertRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.kmember.facade.MemberLevelUpsertRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.kmember.facade.MemberLevelUpsertRequest)
      com.kikitrade.kmember.facade.MemberLevelUpsertRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelUpsertRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelUpsertRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.kmember.facade.MemberLevelUpsertRequest.class, com.kikitrade.kmember.facade.MemberLevelUpsertRequest.Builder.class);
    }

    // Construct using com.kikitrade.kmember.facade.MemberLevelUpsertRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      saasId_ = "";

      if (memberLevelBuilder_ == null) {
        memberLevel_ = null;
      } else {
        memberLevel_ = null;
        memberLevelBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelUpsertRequest_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.MemberLevelUpsertRequest getDefaultInstanceForType() {
      return com.kikitrade.kmember.facade.MemberLevelUpsertRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.MemberLevelUpsertRequest build() {
      com.kikitrade.kmember.facade.MemberLevelUpsertRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.MemberLevelUpsertRequest buildPartial() {
      com.kikitrade.kmember.facade.MemberLevelUpsertRequest result = new com.kikitrade.kmember.facade.MemberLevelUpsertRequest(this);
      result.saasId_ = saasId_;
      if (memberLevelBuilder_ == null) {
        result.memberLevel_ = memberLevel_;
      } else {
        result.memberLevel_ = memberLevelBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.kmember.facade.MemberLevelUpsertRequest) {
        return mergeFrom((com.kikitrade.kmember.facade.MemberLevelUpsertRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.kmember.facade.MemberLevelUpsertRequest other) {
      if (other == com.kikitrade.kmember.facade.MemberLevelUpsertRequest.getDefaultInstance()) return this;
      if (!other.getSaasId().isEmpty()) {
        saasId_ = other.saasId_;
        onChanged();
      }
      if (other.hasMemberLevel()) {
        mergeMemberLevel(other.getMemberLevel());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              saasId_ = input.readStringRequireUtf8();

              break;
            } // case 10
            case 18: {
              input.readMessage(
                  getMemberLevelFieldBuilder().getBuilder(),
                  extensionRegistry);

              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }

    private java.lang.Object saasId_ = "";
    /**
     * <code>string saasId = 1;</code>
     * @return The saasId.
     */
    public java.lang.String getSaasId() {
      java.lang.Object ref = saasId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        saasId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string saasId = 1;</code>
     * @return The bytes for saasId.
     */
    public com.google.protobuf.ByteString
        getSaasIdBytes() {
      java.lang.Object ref = saasId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        saasId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string saasId = 1;</code>
     * @param value The saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      saasId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSaasId() {
      
      saasId_ = getDefaultInstance().getSaasId();
      onChanged();
      return this;
    }
    /**
     * <code>string saasId = 1;</code>
     * @param value The bytes for saasId to set.
     * @return This builder for chaining.
     */
    public Builder setSaasIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      saasId_ = value;
      onChanged();
      return this;
    }

    private com.kikitrade.kmember.facade.MemberLevel memberLevel_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kmember.facade.MemberLevel, com.kikitrade.kmember.facade.MemberLevel.Builder, com.kikitrade.kmember.facade.MemberLevelOrBuilder> memberLevelBuilder_;
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     * @return Whether the memberLevel field is set.
     */
    public boolean hasMemberLevel() {
      return memberLevelBuilder_ != null || memberLevel_ != null;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     * @return The memberLevel.
     */
    public com.kikitrade.kmember.facade.MemberLevel getMemberLevel() {
      if (memberLevelBuilder_ == null) {
        return memberLevel_ == null ? com.kikitrade.kmember.facade.MemberLevel.getDefaultInstance() : memberLevel_;
      } else {
        return memberLevelBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     */
    public Builder setMemberLevel(com.kikitrade.kmember.facade.MemberLevel value) {
      if (memberLevelBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        memberLevel_ = value;
        onChanged();
      } else {
        memberLevelBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     */
    public Builder setMemberLevel(
        com.kikitrade.kmember.facade.MemberLevel.Builder builderForValue) {
      if (memberLevelBuilder_ == null) {
        memberLevel_ = builderForValue.build();
        onChanged();
      } else {
        memberLevelBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     */
    public Builder mergeMemberLevel(com.kikitrade.kmember.facade.MemberLevel value) {
      if (memberLevelBuilder_ == null) {
        if (memberLevel_ != null) {
          memberLevel_ =
            com.kikitrade.kmember.facade.MemberLevel.newBuilder(memberLevel_).mergeFrom(value).buildPartial();
        } else {
          memberLevel_ = value;
        }
        onChanged();
      } else {
        memberLevelBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     */
    public Builder clearMemberLevel() {
      if (memberLevelBuilder_ == null) {
        memberLevel_ = null;
        onChanged();
      } else {
        memberLevel_ = null;
        memberLevelBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     */
    public com.kikitrade.kmember.facade.MemberLevel.Builder getMemberLevelBuilder() {
      
      onChanged();
      return getMemberLevelFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     */
    public com.kikitrade.kmember.facade.MemberLevelOrBuilder getMemberLevelOrBuilder() {
      if (memberLevelBuilder_ != null) {
        return memberLevelBuilder_.getMessageOrBuilder();
      } else {
        return memberLevel_ == null ?
            com.kikitrade.kmember.facade.MemberLevel.getDefaultInstance() : memberLevel_;
      }
    }
    /**
     * <code>.com.kikitrade.kmember.facade.MemberLevel memberLevel = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kmember.facade.MemberLevel, com.kikitrade.kmember.facade.MemberLevel.Builder, com.kikitrade.kmember.facade.MemberLevelOrBuilder> 
        getMemberLevelFieldBuilder() {
      if (memberLevelBuilder_ == null) {
        memberLevelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.kikitrade.kmember.facade.MemberLevel, com.kikitrade.kmember.facade.MemberLevel.Builder, com.kikitrade.kmember.facade.MemberLevelOrBuilder>(
                getMemberLevel(),
                getParentForChildren(),
                isClean());
        memberLevel_ = null;
      }
      return memberLevelBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.kmember.facade.MemberLevelUpsertRequest)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.kmember.facade.MemberLevelUpsertRequest)
  private static final com.kikitrade.kmember.facade.MemberLevelUpsertRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.kmember.facade.MemberLevelUpsertRequest();
  }

  public static com.kikitrade.kmember.facade.MemberLevelUpsertRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MemberLevelUpsertRequest>
      PARSER = new com.google.protobuf.AbstractParser<MemberLevelUpsertRequest>() {
    @java.lang.Override
    public MemberLevelUpsertRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MemberLevelUpsertRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MemberLevelUpsertRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.kmember.facade.MemberLevelUpsertRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

