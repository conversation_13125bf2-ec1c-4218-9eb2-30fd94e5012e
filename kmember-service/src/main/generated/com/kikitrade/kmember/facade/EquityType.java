// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kmember/MemberFacade.proto

package com.kikitrade.kmember.facade;

/**
 * Protobuf enum {@code com.kikitrade.kmember.facade.EquityType}
 */
public enum EquityType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>UN_QUANTIFIED = 0;</code>
   */
  UN_QUANTIFIED(0),
  /**
   * <code>WITHDRAW_QUOTA_PROMOTION = 1;</code>
   */
  WITHDRAW_QUOTA_PROMOTION(1),
  /**
   * <code>MARGIN_INTEREST_DISCOUNT = 2;</code>
   */
  MARGIN_INTEREST_DISCOUNT(2),
  /**
   * <code>TREASURE_INTEREST_PROMOTION = 3;</code>
   */
  TREASURE_INTEREST_PROMOTION(3),
  /**
   * <pre>
   * 交易返佣
   * </pre>
   *
   * <code>TRADE_REBATE = 4;</code>
   */
  TRADE_REBATE(4),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>UN_QUANTIFIED = 0;</code>
   */
  public static final int UN_QUANTIFIED_VALUE = 0;
  /**
   * <code>WITHDRAW_QUOTA_PROMOTION = 1;</code>
   */
  public static final int WITHDRAW_QUOTA_PROMOTION_VALUE = 1;
  /**
   * <code>MARGIN_INTEREST_DISCOUNT = 2;</code>
   */
  public static final int MARGIN_INTEREST_DISCOUNT_VALUE = 2;
  /**
   * <code>TREASURE_INTEREST_PROMOTION = 3;</code>
   */
  public static final int TREASURE_INTEREST_PROMOTION_VALUE = 3;
  /**
   * <pre>
   * 交易返佣
   * </pre>
   *
   * <code>TRADE_REBATE = 4;</code>
   */
  public static final int TRADE_REBATE_VALUE = 4;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static EquityType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static EquityType forNumber(int value) {
    switch (value) {
      case 0: return UN_QUANTIFIED;
      case 1: return WITHDRAW_QUOTA_PROMOTION;
      case 2: return MARGIN_INTEREST_DISCOUNT;
      case 3: return TREASURE_INTEREST_PROMOTION;
      case 4: return TRADE_REBATE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<EquityType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      EquityType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<EquityType>() {
          public EquityType findValueByNumber(int number) {
            return EquityType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.kmember.facade.MemberFacadeOuterClass.getDescriptor().getEnumTypes().get(2);
  }

  private static final EquityType[] VALUES = values();

  public static EquityType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private EquityType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.kmember.facade.EquityType)
}

