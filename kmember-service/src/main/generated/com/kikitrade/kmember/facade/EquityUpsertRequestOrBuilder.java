// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kmember/MemberFacade.proto

package com.kikitrade.kmember.facade;

public interface EquityUpsertRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.kmember.facade.EquityUpsertRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.com.kikitrade.kmember.facade.Equity equity = 1;</code>
   * @return Whether the equity field is set.
   */
  boolean hasEquity();
  /**
   * <code>.com.kikitrade.kmember.facade.Equity equity = 1;</code>
   * @return The equity.
   */
  com.kikitrade.kmember.facade.Equity getEquity();
  /**
   * <code>.com.kikitrade.kmember.facade.Equity equity = 1;</code>
   */
  com.kikitrade.kmember.facade.EquityOrBuilder getEquityOrBuilder();
}
