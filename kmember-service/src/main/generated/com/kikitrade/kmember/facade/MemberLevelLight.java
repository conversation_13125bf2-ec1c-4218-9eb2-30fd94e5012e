// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kmember/MemberFacade.proto

package com.kikitrade.kmember.facade;

/**
 * Protobuf type {@code com.kikitrade.kmember.facade.MemberLevelLight}
 */
public final class MemberLevelLight extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.kmember.facade.MemberLevelLight)
    MemberLevelLightOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MemberLevelLight.newBuilder() to construct.
  private MemberLevelLight(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MemberLevelLight() {
    level_ = "";
    name_ = "";
    status_ = 0;
    scope_ = 0;
    namespace_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MemberLevelLight();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MemberLevelLight(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            level_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            status_ = rawValue;
            break;
          }
          case 34: {
            com.kikitrade.kmember.facade.Decimal.Builder subBuilder = null;
            if (point_ != null) {
              subBuilder = point_.toBuilder();
            }
            point_ = input.readMessage(com.kikitrade.kmember.facade.Decimal.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(point_);
              point_ = subBuilder.buildPartial();
            }

            break;
          }
          case 40: {

            validDays_ = input.readInt32();
            break;
          }
          case 48: {
            int rawValue = input.readEnum();

            scope_ = rawValue;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            namespace_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelLight_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelLight_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.kmember.facade.MemberLevelLight.class, com.kikitrade.kmember.facade.MemberLevelLight.Builder.class);
  }

  public static final int LEVEL_FIELD_NUMBER = 1;
  private volatile java.lang.Object level_;
  /**
   * <code>string level = 1;</code>
   * @return The level.
   */
  @java.lang.Override
  public java.lang.String getLevel() {
    java.lang.Object ref = level_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      level_ = s;
      return s;
    }
  }
  /**
   * <code>string level = 1;</code>
   * @return The bytes for level.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLevelBytes() {
    java.lang.Object ref = level_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      level_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object name_;
  /**
   * <code>string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 3;
  private int status_;
  /**
   * <code>.com.kikitrade.kmember.facade.Status status = 3;</code>
   * @return The enum numeric value on the wire for status.
   */
  @java.lang.Override public int getStatusValue() {
    return status_;
  }
  /**
   * <code>.com.kikitrade.kmember.facade.Status status = 3;</code>
   * @return The status.
   */
  @java.lang.Override public com.kikitrade.kmember.facade.Status getStatus() {
    @SuppressWarnings("deprecation")
    com.kikitrade.kmember.facade.Status result = com.kikitrade.kmember.facade.Status.valueOf(status_);
    return result == null ? com.kikitrade.kmember.facade.Status.UNRECOGNIZED : result;
  }

  public static final int POINT_FIELD_NUMBER = 4;
  private com.kikitrade.kmember.facade.Decimal point_;
  /**
   * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
   * @return Whether the point field is set.
   */
  @java.lang.Override
  public boolean hasPoint() {
    return point_ != null;
  }
  /**
   * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
   * @return The point.
   */
  @java.lang.Override
  public com.kikitrade.kmember.facade.Decimal getPoint() {
    return point_ == null ? com.kikitrade.kmember.facade.Decimal.getDefaultInstance() : point_;
  }
  /**
   * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
   */
  @java.lang.Override
  public com.kikitrade.kmember.facade.DecimalOrBuilder getPointOrBuilder() {
    return getPoint();
  }

  public static final int VALIDDAYS_FIELD_NUMBER = 5;
  private int validDays_;
  /**
   * <code>int32 validDays = 5;</code>
   * @return The validDays.
   */
  @java.lang.Override
  public int getValidDays() {
    return validDays_;
  }

  public static final int SCOPE_FIELD_NUMBER = 6;
  private int scope_;
  /**
   * <pre>
   *领域：平台权益 明星权益
   * </pre>
   *
   * <code>.com.kikitrade.kmember.facade.Scope scope = 6;</code>
   * @return The enum numeric value on the wire for scope.
   */
  @java.lang.Override public int getScopeValue() {
    return scope_;
  }
  /**
   * <pre>
   *领域：平台权益 明星权益
   * </pre>
   *
   * <code>.com.kikitrade.kmember.facade.Scope scope = 6;</code>
   * @return The scope.
   */
  @java.lang.Override public com.kikitrade.kmember.facade.Scope getScope() {
    @SuppressWarnings("deprecation")
    com.kikitrade.kmember.facade.Scope result = com.kikitrade.kmember.facade.Scope.valueOf(scope_);
    return result == null ? com.kikitrade.kmember.facade.Scope.UNRECOGNIZED : result;
  }

  public static final int NAMESPACE_FIELD_NUMBER = 7;
  private volatile java.lang.Object namespace_;
  /**
   * <pre>
   *命名空间。如果是平台等级，namespace=0
   * </pre>
   *
   * <code>string namespace = 7;</code>
   * @return The namespace.
   */
  @java.lang.Override
  public java.lang.String getNamespace() {
    java.lang.Object ref = namespace_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      namespace_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *命名空间。如果是平台等级，namespace=0
   * </pre>
   *
   * <code>string namespace = 7;</code>
   * @return The bytes for namespace.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNamespaceBytes() {
    java.lang.Object ref = namespace_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      namespace_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(level_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, level_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (status_ != com.kikitrade.kmember.facade.Status.Active.getNumber()) {
      output.writeEnum(3, status_);
    }
    if (point_ != null) {
      output.writeMessage(4, getPoint());
    }
    if (validDays_ != 0) {
      output.writeInt32(5, validDays_);
    }
    if (scope_ != com.kikitrade.kmember.facade.Scope.platform.getNumber()) {
      output.writeEnum(6, scope_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(namespace_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, namespace_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(level_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, level_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (status_ != com.kikitrade.kmember.facade.Status.Active.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, status_);
    }
    if (point_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getPoint());
    }
    if (validDays_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, validDays_);
    }
    if (scope_ != com.kikitrade.kmember.facade.Scope.platform.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, scope_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(namespace_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, namespace_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.kmember.facade.MemberLevelLight)) {
      return super.equals(obj);
    }
    com.kikitrade.kmember.facade.MemberLevelLight other = (com.kikitrade.kmember.facade.MemberLevelLight) obj;

    if (!getLevel()
        .equals(other.getLevel())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (status_ != other.status_) return false;
    if (hasPoint() != other.hasPoint()) return false;
    if (hasPoint()) {
      if (!getPoint()
          .equals(other.getPoint())) return false;
    }
    if (getValidDays()
        != other.getValidDays()) return false;
    if (scope_ != other.scope_) return false;
    if (!getNamespace()
        .equals(other.getNamespace())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getLevel().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    if (hasPoint()) {
      hash = (37 * hash) + POINT_FIELD_NUMBER;
      hash = (53 * hash) + getPoint().hashCode();
    }
    hash = (37 * hash) + VALIDDAYS_FIELD_NUMBER;
    hash = (53 * hash) + getValidDays();
    hash = (37 * hash) + SCOPE_FIELD_NUMBER;
    hash = (53 * hash) + scope_;
    hash = (37 * hash) + NAMESPACE_FIELD_NUMBER;
    hash = (53 * hash) + getNamespace().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kmember.facade.MemberLevelLight parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.kmember.facade.MemberLevelLight prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.kikitrade.kmember.facade.MemberLevelLight}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.kmember.facade.MemberLevelLight)
      com.kikitrade.kmember.facade.MemberLevelLightOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelLight_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelLight_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.kmember.facade.MemberLevelLight.class, com.kikitrade.kmember.facade.MemberLevelLight.Builder.class);
    }

    // Construct using com.kikitrade.kmember.facade.MemberLevelLight.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      level_ = "";

      name_ = "";

      status_ = 0;

      if (pointBuilder_ == null) {
        point_ = null;
      } else {
        point_ = null;
        pointBuilder_ = null;
      }
      validDays_ = 0;

      scope_ = 0;

      namespace_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.kmember.facade.MemberFacadeOuterClass.internal_static_com_kikitrade_kmember_facade_MemberLevelLight_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.MemberLevelLight getDefaultInstanceForType() {
      return com.kikitrade.kmember.facade.MemberLevelLight.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.MemberLevelLight build() {
      com.kikitrade.kmember.facade.MemberLevelLight result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.kmember.facade.MemberLevelLight buildPartial() {
      com.kikitrade.kmember.facade.MemberLevelLight result = new com.kikitrade.kmember.facade.MemberLevelLight(this);
      result.level_ = level_;
      result.name_ = name_;
      result.status_ = status_;
      if (pointBuilder_ == null) {
        result.point_ = point_;
      } else {
        result.point_ = pointBuilder_.build();
      }
      result.validDays_ = validDays_;
      result.scope_ = scope_;
      result.namespace_ = namespace_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.kmember.facade.MemberLevelLight) {
        return mergeFrom((com.kikitrade.kmember.facade.MemberLevelLight)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.kmember.facade.MemberLevelLight other) {
      if (other == com.kikitrade.kmember.facade.MemberLevelLight.getDefaultInstance()) return this;
      if (!other.getLevel().isEmpty()) {
        level_ = other.level_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (other.hasPoint()) {
        mergePoint(other.getPoint());
      }
      if (other.getValidDays() != 0) {
        setValidDays(other.getValidDays());
      }
      if (other.scope_ != 0) {
        setScopeValue(other.getScopeValue());
      }
      if (!other.getNamespace().isEmpty()) {
        namespace_ = other.namespace_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.kikitrade.kmember.facade.MemberLevelLight parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.kikitrade.kmember.facade.MemberLevelLight) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object level_ = "";
    /**
     * <code>string level = 1;</code>
     * @return The level.
     */
    public java.lang.String getLevel() {
      java.lang.Object ref = level_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        level_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string level = 1;</code>
     * @return The bytes for level.
     */
    public com.google.protobuf.ByteString
        getLevelBytes() {
      java.lang.Object ref = level_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        level_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string level = 1;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      level_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string level = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      
      level_ = getDefaultInstance().getLevel();
      onChanged();
      return this;
    }
    /**
     * <code>string level = 1;</code>
     * @param value The bytes for level to set.
     * @return This builder for chaining.
     */
    public Builder setLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      level_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <code>.com.kikitrade.kmember.facade.Status status = 3;</code>
     * @return The enum numeric value on the wire for status.
     */
    @java.lang.Override public int getStatusValue() {
      return status_;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Status status = 3;</code>
     * @param value The enum numeric value on the wire for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusValue(int value) {
      
      status_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Status status = 3;</code>
     * @return The status.
     */
    @java.lang.Override
    public com.kikitrade.kmember.facade.Status getStatus() {
      @SuppressWarnings("deprecation")
      com.kikitrade.kmember.facade.Status result = com.kikitrade.kmember.facade.Status.valueOf(status_);
      return result == null ? com.kikitrade.kmember.facade.Status.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Status status = 3;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(com.kikitrade.kmember.facade.Status value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Status status = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      
      status_ = 0;
      onChanged();
      return this;
    }

    private com.kikitrade.kmember.facade.Decimal point_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kmember.facade.Decimal, com.kikitrade.kmember.facade.Decimal.Builder, com.kikitrade.kmember.facade.DecimalOrBuilder> pointBuilder_;
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     * @return Whether the point field is set.
     */
    public boolean hasPoint() {
      return pointBuilder_ != null || point_ != null;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     * @return The point.
     */
    public com.kikitrade.kmember.facade.Decimal getPoint() {
      if (pointBuilder_ == null) {
        return point_ == null ? com.kikitrade.kmember.facade.Decimal.getDefaultInstance() : point_;
      } else {
        return pointBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     */
    public Builder setPoint(com.kikitrade.kmember.facade.Decimal value) {
      if (pointBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        point_ = value;
        onChanged();
      } else {
        pointBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     */
    public Builder setPoint(
        com.kikitrade.kmember.facade.Decimal.Builder builderForValue) {
      if (pointBuilder_ == null) {
        point_ = builderForValue.build();
        onChanged();
      } else {
        pointBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     */
    public Builder mergePoint(com.kikitrade.kmember.facade.Decimal value) {
      if (pointBuilder_ == null) {
        if (point_ != null) {
          point_ =
            com.kikitrade.kmember.facade.Decimal.newBuilder(point_).mergeFrom(value).buildPartial();
        } else {
          point_ = value;
        }
        onChanged();
      } else {
        pointBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     */
    public Builder clearPoint() {
      if (pointBuilder_ == null) {
        point_ = null;
        onChanged();
      } else {
        point_ = null;
        pointBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     */
    public com.kikitrade.kmember.facade.Decimal.Builder getPointBuilder() {
      
      onChanged();
      return getPointFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     */
    public com.kikitrade.kmember.facade.DecimalOrBuilder getPointOrBuilder() {
      if (pointBuilder_ != null) {
        return pointBuilder_.getMessageOrBuilder();
      } else {
        return point_ == null ?
            com.kikitrade.kmember.facade.Decimal.getDefaultInstance() : point_;
      }
    }
    /**
     * <code>.com.kikitrade.kmember.facade.Decimal point = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.kikitrade.kmember.facade.Decimal, com.kikitrade.kmember.facade.Decimal.Builder, com.kikitrade.kmember.facade.DecimalOrBuilder> 
        getPointFieldBuilder() {
      if (pointBuilder_ == null) {
        pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.kikitrade.kmember.facade.Decimal, com.kikitrade.kmember.facade.Decimal.Builder, com.kikitrade.kmember.facade.DecimalOrBuilder>(
                getPoint(),
                getParentForChildren(),
                isClean());
        point_ = null;
      }
      return pointBuilder_;
    }

    private int validDays_ ;
    /**
     * <code>int32 validDays = 5;</code>
     * @return The validDays.
     */
    @java.lang.Override
    public int getValidDays() {
      return validDays_;
    }
    /**
     * <code>int32 validDays = 5;</code>
     * @param value The validDays to set.
     * @return This builder for chaining.
     */
    public Builder setValidDays(int value) {
      
      validDays_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 validDays = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearValidDays() {
      
      validDays_ = 0;
      onChanged();
      return this;
    }

    private int scope_ = 0;
    /**
     * <pre>
     *领域：平台权益 明星权益
     * </pre>
     *
     * <code>.com.kikitrade.kmember.facade.Scope scope = 6;</code>
     * @return The enum numeric value on the wire for scope.
     */
    @java.lang.Override public int getScopeValue() {
      return scope_;
    }
    /**
     * <pre>
     *领域：平台权益 明星权益
     * </pre>
     *
     * <code>.com.kikitrade.kmember.facade.Scope scope = 6;</code>
     * @param value The enum numeric value on the wire for scope to set.
     * @return This builder for chaining.
     */
    public Builder setScopeValue(int value) {
      
      scope_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *领域：平台权益 明星权益
     * </pre>
     *
     * <code>.com.kikitrade.kmember.facade.Scope scope = 6;</code>
     * @return The scope.
     */
    @java.lang.Override
    public com.kikitrade.kmember.facade.Scope getScope() {
      @SuppressWarnings("deprecation")
      com.kikitrade.kmember.facade.Scope result = com.kikitrade.kmember.facade.Scope.valueOf(scope_);
      return result == null ? com.kikitrade.kmember.facade.Scope.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *领域：平台权益 明星权益
     * </pre>
     *
     * <code>.com.kikitrade.kmember.facade.Scope scope = 6;</code>
     * @param value The scope to set.
     * @return This builder for chaining.
     */
    public Builder setScope(com.kikitrade.kmember.facade.Scope value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      scope_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *领域：平台权益 明星权益
     * </pre>
     *
     * <code>.com.kikitrade.kmember.facade.Scope scope = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearScope() {
      
      scope_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object namespace_ = "";
    /**
     * <pre>
     *命名空间。如果是平台等级，namespace=0
     * </pre>
     *
     * <code>string namespace = 7;</code>
     * @return The namespace.
     */
    public java.lang.String getNamespace() {
      java.lang.Object ref = namespace_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        namespace_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *命名空间。如果是平台等级，namespace=0
     * </pre>
     *
     * <code>string namespace = 7;</code>
     * @return The bytes for namespace.
     */
    public com.google.protobuf.ByteString
        getNamespaceBytes() {
      java.lang.Object ref = namespace_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        namespace_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *命名空间。如果是平台等级，namespace=0
     * </pre>
     *
     * <code>string namespace = 7;</code>
     * @param value The namespace to set.
     * @return This builder for chaining.
     */
    public Builder setNamespace(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      namespace_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *命名空间。如果是平台等级，namespace=0
     * </pre>
     *
     * <code>string namespace = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearNamespace() {
      
      namespace_ = getDefaultInstance().getNamespace();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *命名空间。如果是平台等级，namespace=0
     * </pre>
     *
     * <code>string namespace = 7;</code>
     * @param value The bytes for namespace to set.
     * @return This builder for chaining.
     */
    public Builder setNamespaceBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      namespace_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.kmember.facade.MemberLevelLight)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.kmember.facade.MemberLevelLight)
  private static final com.kikitrade.kmember.facade.MemberLevelLight DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.kmember.facade.MemberLevelLight();
  }

  public static com.kikitrade.kmember.facade.MemberLevelLight getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MemberLevelLight>
      PARSER = new com.google.protobuf.AbstractParser<MemberLevelLight>() {
    @java.lang.Override
    public MemberLevelLight parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MemberLevelLight(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MemberLevelLight> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MemberLevelLight> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.kmember.facade.MemberLevelLight getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

