package com.kikitrade.member.api;

import com.kikitrade.member.model.MemberProductItemDTO;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/17 15:53
 */
public interface RemoteMemberCastleService {

    /**
     * 查询会员的仓库信息
     * @param saasId
     * @param customerId
     * @return
     */
    MemberProductItemDTO memberCastle(String saasId, String customerId);

    /**
     * 收割城堡中的商品
     * @param saasId
     * @param customerId
     * @return
     */
    Boolean harvestProduct(String saasId, String customerId);
}
