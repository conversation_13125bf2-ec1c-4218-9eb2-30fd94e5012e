package com.drex.core.api;

import com.drex.core.api.request.NoticeDTO;
import com.drex.core.api.request.OperateConfig;
import com.drex.core.api.response.ManageResponse;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

public interface RemoteManageService {

    /**
     * 查询指定参数配置
     * @return
     */
    Response<List<OperateConfig>> getOperateConfigs(List<String> keys);

    /**
     * 刷新参数配置
     * @param operateConfig
     * @return
     */
    Response<ManageResponse> refreshOperateConfig(OperateConfig operateConfig);

    /**
     * 保存通知
     *
     * @param noticeDTO 通知DTO
     * @return 是否保存成功
     */
    Response<Boolean> saveNotice(NoticeDTO noticeDTO);

    /**
     * 添加内测用户白名单
     *
     * @param customerIds 用户ID列表
     * @return 是否添加成功
     */
    Response<Boolean> addInnerTestUsers(List<String> customerIds);

    /**
     * 根据状态和发送目标获取通知列表
     *
     * @return 通知DTO列表
     */
    Response<List<NoticeDTO>> getNotices(String customerId, Long lastFetchNotifyTime);

}
