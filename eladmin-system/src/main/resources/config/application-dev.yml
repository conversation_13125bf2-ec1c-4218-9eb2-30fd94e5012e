#配置数据源
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:rm-t4nvmql4d7h5he6x6yo.mysql.singapore.rds.aliyuncs.com}:${DB_PORT:3306}/${DB_NAME:eladmin}?useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
    username: ${DB_USER:root}
    password: ${DB_PWD:d9RdtyHrMT6f}
    # url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:eladmin}?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true
    # username: ${DB_USER:root}
    # password: ${DB_PWD:12345}
    druid:
      initial-size: 5
      min-idle: 15
      max-active: 30
      remove-abandoned-timeout: 180
      max-wait: 3000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      validation-query: select 1
      webStatFilter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  data:
    redis:
      #数据库索引
      database: ${REDIS_DB:3}
      #    host: ${REDIS_HOST:duom-dev-manage-java-redis}
      host: ${REDIS_HOST:r-t4nc76b73a4d5ca4pd.redis.singapore.rds.aliyuncs.com}
      #    ssl: ${REDIS_SSL:true}
      #    ssl-key-store: ${REDIS_KEY_STORE:}
      #    ssl-key-store-password: ${REDIS_KEY_STORE_PASSWORD:}
      port: ${REDIS_PORT:6379}
      #连接超时时间
      timeout: 5000
      password: aG7UWFokPNaj
      # Redis连接池配置
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0
      # Redis序列化配置
      serialization:
        key: org.springframework.data.redis.serializer.StringRedisSerializer
        value: org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer
        hash-key: org.springframework.data.redis.serializer.StringRedisSerializer
        hash-value: org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer

# 登录相关配置
login:
  #  是否限制单用户登录
  single-login: false
  # Redis 用户登录缓存配置
  user-cache:
    # 存活时间/秒
    idle-time: 21600
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: arithmetic
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    height: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体
    font-name:
    # 字体大小
    font-size: 25

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少 88 位的 Base64 对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 14400000
  # 在线用户 key
  online-key: "online-token:"
  # 验证码
  code-key: "captcha-code:"
  # token 续期检查时间范围（默认 30 分钟，单位毫秒），在 token 即将过期的一段时间内用户操作了，则给用户的 token 续期
  detect: 1800000
  # 续期时间范围，默认 1 小时，单位毫秒
  renew: 3600000

#是否允许生成代码，生产环境设置为 false
generator:
  enabled: true

#是否开启 swagger-ui
swagger:
  enabled: true

log-store:
  host: ap-southeast-1.log.aliyuncs.com
  project: ex-beta
  name: kactivity

dubbo:
  application:
    id: manager
    name: manager
    parameters:
      router: traffic
    parameters[router]: traffic
    qos-accept-foreign-ip: true
    qos-anonymous-access-permission-level: PROTECTED
    serialize-check-status: WARN
    version: 1.0.0
  consumer:
    check: false
    group: kktd
    mock: return null
  protocol:
    name: dubbo
    port: 20880
    queues: 1000
    server: netty
    threadpool: fixed
    threads: 500
  provider:
    group: kktd
    parameters:
      traffic: blue
    parameters[traffic]: blue
    prefer-serialization: hessian2,fastjson2
    retries: 1
    serialization: hessian2
    timeout: 5000
  reference:
    check: false
  registry:
    #address: zookeeper://zookeeper.quests-dev.svc.kiki.local:2181
    address: zookeeper://127.0.0.1:2181
    timeout: 60000
    check: false
  config-center:
    timeout: 60000
  metadata-report:
    timeout: 60000
  tracing:
    enabled: true
  traffic:
    version: 1.0.0-blue

aliyun-oss:
  endpoint: http://oss-ap-southeast-1.aliyuncs.com
  bucket-name: quests-dev
  role-name: QuestsManagejavaRoleDev

server:
  servlet:
    context-path: /
    session:
      timeout: 30m
