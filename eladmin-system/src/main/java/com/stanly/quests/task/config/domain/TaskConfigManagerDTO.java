package com.stanly.quests.task.config.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/26 16:45
 */
@Data
public class TaskConfigManagerDTO implements Serializable {

    @Schema(name = "数据主键")
    private String id;
    @NotBlank
    @Schema(name = "SaasId")
    private String saasId;

    @Schema(name = "任务id")
    private String taskId;

    @Schema(name = "分组id")
    private String groupId;

    @Schema(name = "是否在分组内(0 否 1 是)")
    private String isGroup;

    @Schema(name = "是否列表展示(0 否 1 是)")
    private String showList;

    @Schema(name = "积分流水描述")
    private String ledgerTitle;

    @Schema(name = "任务标题")
    private String title;

    @Schema(name = "任务标题(APP)")
    private String titleApp;

    @Schema(name = "任务标题(PC)")
    private String titlePc;

    @Schema(name = "任务描述(APP非会员)")
    private String titleDescAppNormal;

    @Schema(name = "任务描述(APP会员L1)")
    private String titleDescAppL1;

    @Schema(name = "任务描述(PC非会员)")
    private String titleDescPcNormal;

    @Schema(name = "任务描述(PC会员L1)")
    private String titleDescPcL1;

    @Schema(name = "任务角标内容")
    private String labelName;

    @Schema(name = "任务角标颜色")
    private String labelColor;

    @Schema(name = "任务状态(ACTIVE、DISABLE)")
    private String status;

    @Schema(name = "任务开始时间，utc毫秒时间戳")
    private Timestamp startTime;

    @Schema(name = "任务结束时间，utc毫秒时间戳")
    private Timestamp endTime;

    @Schema(name = "任务事件编号")
    private String code;

    @Schema(name = "前端需要显示的事件，目前主要映射icon")
    private String showCode;

    @Schema(name = "任务次数上限(非会员)")
    private Integer limitCountNormal;

    @Schema(name = "任务次数上限(会员L1)")
    private Integer limitCountL1;

    @Schema(name = "任务列表图片")
    private String taskListImage;

    @Schema(name = "任务详情图片")
    private String taskDetailImage;

    @Schema(name = "发奖频率，每完成n次任务，发一次奖")
    private Integer rewardFrequency;

    @Schema(name = "任务刷新周期")
    private String cycle;

    @Schema(name = "进度计算方式")
    private String progressType;

    @Schema(name = "奖品计算方式")
    private String rewardForm;

    @Schema(name = "积分领取方式")
    private String provideType;

    @Schema(name = "任务排序")
    private Integer order;

    @Schema(name = "任务所属模块twitter、discord")
    private String domain;

    @Schema(name = "twitter被关注的人")
    private String twitterFollow;

    @Schema(name = "发帖包含的关键字")
    private String twitterKeyword;

    @Schema(name = "贴文追加的文案")
    private String twitterRandomAppendText;

    @Schema(name = "贴文替换的文案")
    private String twitterRandomText;

    @Schema(name = "回复任务要求指定的用户")
    private String twitterKols;

    @Schema(name = "twitter用户名包含的关键字")
    private String twitterUsername;

    @Schema(name = "查最近帖文")
    private String lastPost;

    @Schema(name = "discord服务器id")
    private String discordGuild;

    @Schema(name = "拥有discord某个角色")
    private String discordGuildRole;

    @Schema(name = "任务要求的vip等级，+0: 普通用户以上，1: 会员专享")
    private String vipLevel;

    @Schema(name = "前端需要显示的按钮，默认0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击go后才会出现verify按钮")
    private Integer btn;

    @Schema(name = "是否显示任务showProgress进度条")
    private String showProgress;

    @Schema(name = "任务的详情页")
    private String url;

    @Schema(name = "任务描述下的按钮文字")
    private String linkName;

    @Schema(name = "任务描述下的按钮链接")
    private String linkUrl;

    @Schema(name = "任务所属渠道，pc | app")
    private String channel;

    @Schema(name = "任务需要显示的页面位置")
    private String position;

    @Schema(name = "是否回调注册任务，默认true，库里null表示true")
    private String callRegister;

    @Schema(name = "任务状态判断，默认任务表状态twitterAuth：twitter授权过discordAuth: discord授权过")
    private String taskStatusCondition;

    @Schema(name = "是否跳过验证")
    private String skipVerification;

    @Schema(name = "任务所属客户端类型，android｜ios")
    private String clientType;

    @Schema(name = "是否需要回调osp")
    private Integer ospCallBack;

    @Schema(name = "osp事件类型")
    private String taskType;

    @Schema(name = "osp事件类型")
    private String rewardType;

    @Schema(name = "osp事件类型")
    private String rewardAmount;

    @Schema(name = "展示奖励数量")
    private String showRewardAmount;

    @Schema(name = "osp事件类型")
    private List<Reward> rewards;

    @Schema(name = "appId")
    private String appId;

    @Schema(name = "chainId")
    private String chainId;

    @Schema(name = "discord授权地址")
    private String discordAuthUrlApp;

    @Schema(name = "discord pcs授权地址")
    private String discordAuthUrlPc;

    @Schema(name = "奖励币种")
    private String rewardCurrency;

    @Schema(name = "校验任务是否执行")
    private String checkReward;
}
