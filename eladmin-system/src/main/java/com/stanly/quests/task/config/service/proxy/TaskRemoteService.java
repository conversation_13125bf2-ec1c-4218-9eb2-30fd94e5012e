package com.stanly.quests.task.config.service.proxy;

import com.alibaba.fastjson2.JSON;
import com.drex.activity.task.api.RemoteManageActivityConfigService;
import com.drex.activity.task.model.dto.TaskConfigDTO;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.common.util.BeanUtil;
import com.stanly.quests.task.precision.domain.PrecisionPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/6 15:26
 */
@Slf4j
@Service
public class TaskRemoteService {

    @DubboReference
    private RemoteManageActivityConfigService remoteTaskService;


   public boolean syncTaskConfig(TaskConfigDTO taskConfigDTO, boolean isGray) {
            log.info("taskRemoteService syncTaskConfig, taskConfigDTO:{}, isGray:{}", JSON.toJSONString(taskConfigDTO), isGray);
           Response<Boolean> response = remoteTaskService.syncTaskConfig(taskConfigDTO);
           if (response.isSuccess()) {
               return true;
           } else {
               log.error("TaskRemoteService syncTaskConfig error. taskConfigDTO:{}, isGray:{}", JSON.toJSONString(taskConfigDTO), isGray, ex);
               return false;
           }

    }

    public TaskManageResponse deleteTaskConfig(String taskId) {
        TaskManageResponse response = new TaskManageResponse();
        try{
            log.info("taskRemoteService deleteTaskConfig, taskId:{}", taskId);
            return remoteTaskService.delete(taskId);
        }catch (Exception ex){
            log.error("TaskRemoteService deleteTaskConfig error. taskId:{}", taskId, ex);
            response.setSuccess(false);
            return response;
        }
    }

    public TaskManageResponse syncPrecisionPool(PrecisionPool precisionPool) {
        TaskManageResponse response = new TaskManageResponse();
        try{
            log.info("taskRemoteService syncPrecisionPool, precisionPool:{}", JSON.toJSONString(precisionPool));
            PrecisionPoolDTO precisionPoolDTO = BeanUtil.copyProperties(precisionPool, new PrecisionPoolDTO());
            return remoteTaskService.syncPrecisionPool(precisionPoolDTO);
        }catch (Exception ex){
            log.error("TaskRemoteService syncPrecisionPool error. precisionPool:{}", JSON.toJSONString(precisionPool), ex);
            response.setSuccess(false);
            return response;
        }
    }
}
