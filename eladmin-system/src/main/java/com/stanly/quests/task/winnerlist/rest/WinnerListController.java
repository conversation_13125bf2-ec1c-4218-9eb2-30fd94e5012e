//package com.stanly.quests.task.winnerlist.rest;
//
//import com.stanly.quests.task.winnerlist.domain.WinnerList;
//import com.stanly.quests.task.winnerlist.service.WinnerListService;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
//@RestController
//@RequiredArgsConstructor
//@Tag(name = "WinnerListController管理")
//@RequestMapping("/api/winnerlist")
//public class WinnerListController {
//
//    private final  WinnerListService winnerListService;
//
//    @PostMapping
//    public Boolean createWinnerList(@RequestBody WinnerList winnerList) {
//        return winnerListService.createWinnerList(winnerList);
//    }
//
//    @PostMapping(value = "/delete")
//    public Boolean deleteWinnerList(@RequestBody WinnerList winnerList) {
//        return winnerListService.deleteWinnerList(
//                winnerList.getSaasId(), winnerList.getActivityId(), winnerList.getPoolId(), winnerList.getPrizeAddress());
//    }
//
//    @GetMapping
//    public List<WinnerList> getWinnerLists(String saasId, String activityId, String poolId) {
//        return winnerListService.getWinnerLists(saasId, activityId, poolId);
//    }
//
//}