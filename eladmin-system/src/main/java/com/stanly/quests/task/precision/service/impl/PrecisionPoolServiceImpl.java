///*
//*  Copyright 2019-2020 <PERSON>
//*
//*  Licensed under the Apache License, Version 2.0 (the "License");
//*  you may not use this file except in compliance with the License.
//*  You may obtain a copy of the License at
//*
//*  http://www.apache.org/licenses/LICENSE-2.0
//*
//*  Unless required by applicable law or agreed to in writing, software
//*  distributed under the License is distributed on an "AS IS" BASIS,
//*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//*  See the License for the specific language governing permissions and
//*  limitations under the License.
//*/
//package com.stanly.quests.task.precision.service.impl;
//
//import com.stanly.quests.task.config.service.proxy.TaskRemoteService;
//import com.stanly.quests.task.precision.domain.PrecisionPool;
//import com.stanly.admin.utils.ValidationUtil;
//import com.stanly.admin.utils.FileUtil;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.RequiredArgsConstructor;
//import com.stanly.quests.task.precision.repository.PrecisionPoolRepository;
//import com.stanly.quests.task.precision.service.PrecisionPoolService;
//import com.stanly.quests.task.precision.service.dto.PrecisionPoolDto;
//import com.stanly.quests.task.precision.service.dto.PrecisionPoolQueryCriteria;
//import com.stanly.quests.task.precision.service.mapstruct.PrecisionPoolMapper;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import cn.hutool.core.util.IdUtil;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import com.stanly.admin.utils.PageUtil;
//import com.stanly.admin.utils.QueryHelp;
//import java.util.List;
//import java.util.Map;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.LinkedHashMap;
//import com.stanly.admin.utils.PageResult;
//
///**
//* @website https://eladmin.vip
//* @description 服务实现
//* <AUTHOR>
//* @date 2025-03-20
//**/
//@Service
//@RequiredArgsConstructor
//public class PrecisionPoolServiceImpl implements PrecisionPoolService {
//
//    private final PrecisionPoolRepository precisionPoolRepository;
//    private final PrecisionPoolMapper precisionPoolMapper;
//    private final TaskRemoteService taskRemoteService;
//
//    @Override
//    public PageResult<PrecisionPoolDto> queryAll(PrecisionPoolQueryCriteria criteria, Pageable pageable){
//        Page<PrecisionPool> page = precisionPoolRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
//        return PageUtil.toPage(page.map(precisionPoolMapper::toDto));
//    }
//
//    @Override
//    public List<PrecisionPoolDto> queryAll(PrecisionPoolQueryCriteria criteria){
//        return precisionPoolMapper.toDto(precisionPoolRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
//    }
//
//    @Override
//    @Transactional
//    public PrecisionPoolDto findById(String id) {
//        PrecisionPool precisionPool = precisionPoolRepository.findById(id).orElseGet(PrecisionPool::new);
//        ValidationUtil.isNull(precisionPool.getId(),"PrecisionPool","id",id);
//        return precisionPoolMapper.toDto(precisionPool);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void create(PrecisionPool resources) {
//        precisionPoolRepository.save(resources);
//        taskRemoteService.syncPrecisionPool(resources);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void update(PrecisionPool resources) {
//        PrecisionPool precisionPool = precisionPoolRepository.findById(resources.getId()).orElseGet(PrecisionPool::new);
//        ValidationUtil.isNull( precisionPool.getId(),"PrecisionPool","id",resources.getId());
//        precisionPool.copy(resources);
//        precisionPoolRepository.save(precisionPool);
//        taskRemoteService.syncPrecisionPool(resources);
//    }
//
//    @Override
//    public void deleteAll(String[] ids) {
//        for (String id : ids) {
//            precisionPoolRepository.deleteById(id);
//        }
//    }
//
//    @Override
//    public void download(List<PrecisionPoolDto> all, HttpServletResponse response) throws IOException {
//        List<Map<String, Object>> list = new ArrayList<>();
//        for (PrecisionPoolDto precisionPool : all) {
//            Map<String,Object> map = new LinkedHashMap<>();
//            map.put("投放渠道,twitter", precisionPool.getChannel());
//            map.put("关联任务/活动,保留字段", precisionPool.getTask());
//            map.put("所属者,edgen目前写死", precisionPool.getOwner());
//            map.put("distribution投放金额,3000", precisionPool.getDistributionAmount());
//            map.put("contribution_ai投放金额,2100", precisionPool.getContributionAiAmount());
//            map.put("contribution_human投放金额,4200", precisionPool.getContributionHumanAmount());
//            map.put("contribution_deep_human投放金额,700 * 6", precisionPool.getContributionDeepHumanAmount());
//            map.put("distribution用户获得的最大金额,30", precisionPool.getDistributionMaxAmount());
//            map.put("contribution_ai单个用户最大金额,21", precisionPool.getContributionAiMaxAmount());
//            map.put("contribution_human单个用户最大金额,42", precisionPool.getContributionHumanMaxAmount());
//            map.put("contribution_deep_human单个用户最大值,7 * 6", precisionPool.getContributionDeepHumanMaxAmount());
//            map.put("资深用户粉丝数", precisionPool.getPrecisionTrackCheckFollowsCount());
//            map.put("ai最少打分数", precisionPool.getPrecisionTrackCheckAiPoint());
//            map.put("资深用户aura值", precisionPool.getPrecisionTrackCheckAsset());
//            list.add(map);
//        }
//        FileUtil.downloadExcel(list, response);
//    }
//}
