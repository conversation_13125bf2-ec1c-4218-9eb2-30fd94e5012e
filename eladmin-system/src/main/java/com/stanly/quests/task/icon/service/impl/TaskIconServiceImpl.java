/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.icon.service.impl;

import com.stanly.quests.task.icon.domain.TaskIcon;
import com.stanly.admin.utils.ValidationUtil;
import com.stanly.admin.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.stanly.quests.task.icon.repository.TaskIconRepository;
import com.stanly.quests.task.icon.service.TaskIconService;
import com.stanly.quests.task.icon.service.dto.TaskIconDto;
import com.stanly.quests.task.icon.service.dto.TaskIconQueryCriteria;
import com.stanly.quests.task.icon.service.mapstruct.TaskIconMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.stanly.admin.utils.PageUtil;
import com.stanly.admin.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-09-25
**/
@Service
@RequiredArgsConstructor
public class TaskIconServiceImpl implements TaskIconService {

    private final TaskIconRepository taskIconRepository;
    private final TaskIconMapper taskIconMapper;

    @Override
    public PageResult<TaskIconDto> queryAll(TaskIconQueryCriteria criteria, Pageable pageable){
        Page<TaskIcon> page = taskIconRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskIconMapper::toDto));
    }

    @Override
    public List<TaskIconDto> queryAll(TaskIconQueryCriteria criteria){
        return taskIconMapper.toDto(taskIconRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskIconDto findById(String id) {
        TaskIcon taskIcon = taskIconRepository.findById(id).orElseGet(TaskIcon::new);
        ValidationUtil.isNull(taskIcon.getId(),"TaskIcon","id",id);
        return taskIconMapper.toDto(taskIcon);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(TaskIcon resources) {
        resources.setId(IdUtil.simpleUUID()); 
        taskIconRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskIcon resources) {
        TaskIcon taskIcon = taskIconRepository.findById(resources.getId()).orElseGet(TaskIcon::new);
        ValidationUtil.isNull( taskIcon.getId(),"TaskIcon","id",resources.getId());
        taskIcon.copy(resources);
        taskIconRepository.save(taskIcon);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            taskIconRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TaskIconDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskIconDto taskIcon : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" saasId",  taskIcon.getSaasId());
            map.put(" code",  taskIcon.getCode());
            map.put(" icon",  taskIcon.getIcon());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}