package com.panl;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/28 20:21
 * @description:
 */
public class TimeLearn {
  public static void main(String[] args) {
    //        System.out.println(System.currentTimeMillis());
    //        Instant now = Instant.now();
    //        System.out.println(now.getEpochSecond());//秒
    //        System.out.println(now.toEpochMilli());//毫秒
    //        System.out.println(ZoneId.systemDefault());
    //        Instant ins = Instant.ofEpochSecond(1732796068);
    //        ZonedDateTime zdt = ins.atZone(ZoneId.systemDefault());
    //        System.out.println(zdt);

    AOnsListener aOnsListener = new AOnsListener();
    aOnsListener.onMessage("hello");
  }

}
